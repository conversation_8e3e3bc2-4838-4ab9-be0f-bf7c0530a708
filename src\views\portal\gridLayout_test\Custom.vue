<template>
  <div class="conatiner">
    <div class="cus-header">
      <div class="cus-button">
        <el-button type="success" v-if="btnTraggle" :icon="Plus" @click="handelModelName()"
          >新增</el-button
        >
        <el-button type="primary" v-else :icon="Edit" @click="handelModelName()">修改</el-button>
        <el-button :icon="Delete" @click="deleteItem">删除</el-button>
      </div>
    </div>
    <div class="cus-body">
      <div class="body-left">
        <el-tabs v-model="tab_activeName">
          <el-tab-pane label="创建组件" name="first">
            <div>
              <el-collapse v-model="activeName">
                <el-collapse-item title="办公中心" name="work">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in workList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'save')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        class="_svg"
                        :title="item.title"
                        :icon="item.icon"
                        :fill="fill"
                        :item-id="item.itemId"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="服务中心" name="service">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in serviceList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'save')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        class="_svg"
                        :title="item.title"
                        :icon="item.icon"
                        :fill="fill"
                        :item-id="item.itemId"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="资讯中心" name="consult">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in consultList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'save')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        :title="item.title"
                        :item-id="item.itemId"
                        :icon="item.icon"
                        :fill="fill"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <!-- <el-collapse-item title="我的工作" name="myWork">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in myWorkList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'save')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        :title="item.title"
                        :item-id="item.itemId"
                        :icon="item.icon"
                        :fill="fill"
                      />
                    </div>
                  </div>
                </el-collapse-item> -->
              </el-collapse>
            </div>
          </el-tab-pane>
          <el-tab-pane label="修改组件" name="second">
            <div>
              <el-collapse v-model="activeName">
                <el-collapse-item title="办公中心" name="work">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in workCenterList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'update')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        class="_svg"
                        :title="item.title"
                        :icon="item.icon"
                        :fill="fill"
                        :item-id="item.itemId"
                        :data-id="item.id"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="服务中心" name="service">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in serviceCenterList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'update')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        class="_svg"
                        :title="item.title"
                        :icon="item.icon"
                        :fill="fill"
                        :item-id="item.itemId"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="资讯中心" name="consult">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in consultCenterList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'update')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        :title="item.title"
                        :item-id="item.itemId"
                        :icon="item.icon"
                        :fill="fill"
                        :data-id="item.id"
                      />
                    </div>
                  </div>
                </el-collapse-item>
                <!-- <el-collapse-item title="我的工作" name="myWork">
                  <div class="collapse-content">
                    <div
                      draggable="true"
                      class="item"
                      v-for="(item, index) in myWorkCenterLList"
                      :key="index"
                      @dragstart="(e) => dragstart(item, 'update')"
                    >
                      <SvgIcon
                        @mouseenter="mouseenter"
                        @mouseleave="mouseleave"
                        :title="item.title"
                        :item-id="item.itemId"
                        :icon="item.icon"
                        :fill="fill"
                      />
                    </div>
                  </div>
                </el-collapse-item> -->
              </el-collapse>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="body-center">
        <div
          class="body-content"
          ref="contentMain"
          @dragleave="dragleave"
          @dragover.prevent="dragover"
          @drop="drop"
        >
          <div class="div-border" v-if="show">
            <component :is="componentName" :itemJson="propForm" />
            <div class="delete-btn">
              <el-button type="danger" size="small" :icon="Delete" @click="handleDelete" />
            </div>
          </div>
        </div>
      </div>
      <div class="body-right">
        <div class="header"><span class="title">组件配置</span></div>
        <div style="padding: 10px"
          ><component :is="formName" ref="dynamicForm" :itemJson="propForm"
        /></div>
      </div>
    </div>
  </div>

  <el-dialog v-model="centerDialogVisible" title="组件默认配置" width="500" align-center>
    <el-row>
      <el-col :span="22">
        <el-form-item label="组件名称">
          <el-input v-model="modelName" placeholder="请输入组件名称" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取消</el-button>
        <el-button @click="handleSaveOrUpdate(false)">使用默认值</el-button>
        <el-button type="primary" @click="handleSaveOrUpdate(true)"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { Plus, Delete, Edit } from '@element-plus/icons-vue'
import SvgIcon from './SvgIcon.vue'
import {
  addLayoutData,
  delLayoutData,
  dragComponentList,
  updateLayoutData
} from '@/api/system/layout'

const message = useMessage()

import { consultList, customList, serviceList, workList } from '@/utils/custom'

defineOptions({
  name: 'Custom'
})
const centerDialogVisible = ref(false)
const modelName = ref()
const propForm = ref()

const tab_activeName = ref('first')

const dataList = [...workList, ...consultList, ...serviceList]

const activeName = ref(['work'])
const contentMain = ref<HTMLElement>()

// 组件数据
const show = ref(false) //是否显示组件
const componentName = ref() //当前组件标识
const formName = ref() //当前组件配置

const dynamicForm = ref()
const startItem = ref<any>()

//保存数据库数据结构
interface Data {
  id?: string
  name?: string
  sign?: string
  layoutJson?: Object
  itemJson?: Object
  icon?: string
  category?: string
  itemId?: string
}

const defaultLayout = ref({
  x: 0,
  y: 0,
  w: 3,
  h: 12
})

//保存数据库数据
let data = ref<Data>({})

//新增还是修改
const saveOrUpdate = ref('')
const btnTraggle = ref(true)

let itemJson = {}

// 处理名称
const handelModelName = () => {
  centerDialogVisible.value = true
  if (!dynamicForm.value.form) {
    return message.warning('请先配置组件')
  }
  let form = dynamicForm.value.form
  itemJson = { ...form }
  data.value.itemJson = itemJson
  if (startItem.value.id) {
    saveOrUpdate.value = startItem.value.id
  }
  if (startItem.value.defaultLayout) {
    defaultLayout.value = JSON.parse(startItem.value.defaultLayout)
  }
  if (startItem.value.title) {
    modelName.value = startItem.value.title
  }
}

const handleSaveOrUpdate = (type: boolean) => {
  if (modelName.value && type) {
    data.value.name = modelName.value
  }
  data.value.layoutJson = defaultLayout.value

  if (saveOrUpdate.value === '') {
    try {
      addLayoutData(data.value).then(() => {
        message.success('新增组件成功')
        //初始化data
        data.value = {}
        //更新数据 清空页面
        resetData()
        show.value = false
        componentName.value = ''
        formName.value = ''
        getWorkCenterList()
        getServiceCenterList()
        getConsultCenterList()
      })
    } catch (err) {
      message.error('新增组件失败')
    }
  } else {
    try {
      data.value.id = saveOrUpdate.value
      updateLayoutData(data.value).then(() => {
        message.success('修改组件成功')
        //初始化data
        data.value = {}
        //更新数据 清空页面
        resetData()
        show.value = false
        componentName.value = ''
        formName.value = ''
        getWorkCenterList()
        getServiceCenterList()
        getConsultCenterList()
      })
    } catch (err) {
      message.error('修改组件失败')
    }
  }
  centerDialogVisible.value = false
}

interface listData {
  id?: string //数据库id
  title?: string //组件名称
  icon?: string //组件图标
  itemId?: string //组件唯一标识
  itemJson?: Object //组件配置属性
  defaultLayout?: string //默认布局
}

const resetData = () => {
  workCenterList.value = []
  serviceCenterList.value = []
  consultCenterList.value = []
  centerList.value = []
}

//办公中心数据
const workCenterList = ref<listData[]>([])
//服务中心数据
const serviceCenterList = ref<listData[]>([])
//资讯中心数据
const consultCenterList = ref<listData[]>([])
//我的工作数据
// const myWorkCenterLList = ref<listData[]>([])

//上面三个数据总和
const centerList = ref<listData[]>([])

//获得办公中心组件列表
const getWorkCenterList = async () => {
  const ret = await dragComponentList('workCenter')
  ret.map((item) => {
    let work: listData = {}
    work.id = item?.id
    work.title = item?.name
    work.icon = item?.icon
    work.itemId = item?.itemId
    work.itemJson = item?.itemJson
    work.defaultLayout = item?.defaultLayout
    workCenterList.value.push(work)
    centerList.value.push(work)
  })
}
//获得服务中心组件列表
const getServiceCenterList = async () => {
  const ret = await dragComponentList('serviceCenter')
  ret.map((item) => {
    let service: listData = {}
    service.id = item?.id
    service.title = item?.name
    service.icon = item?.icon
    service.itemId = item?.itemId
    service.itemJson = item?.itemJson
    service.defaultLayout = item?.defaultLayout
    serviceCenterList.value.push(service)
    centerList.value.push(service)
  })
}

//获得咨询中心组件列表
const getConsultCenterList = async () => {
  const ret = await dragComponentList('consultCenter')
  ret.map((item) => {
    let consult: listData = {}
    consult.id = item?.id
    consult.title = item?.name
    consult.icon = item?.icon
    consult.itemId = item?.itemId
    consult.itemJson = item?.itemJson
    consult.defaultLayout = item?.defaultLayout
    consultCenterList.value.push(consult)
    centerList.value.push(consult)
  })
}
//获得我的工作
// const getMyWorkList = async () => {
//   const ret = await dragComponentList('myWork')
//   ret.map((item) => {
//     let myWork: listData = {}
//     myWork.id = item?.id
//     myWork.title = item?.name
//     myWork.icon = item?.icon
//     myWork.itemId = item?.itemId
//     myWork.itemJson = item?.itemJson
//     myWork.defaultLayout = item?.defaultLayout
//     myWorkCenterLList.value.push(myWork)
//     centerList.value.push(myWork)
//   })
// }

const parentRect = ref()

onMounted(() => {
  if (contentMain.value) {
    parentRect.value = contentMain.value.getBoundingClientRect()
  }
  getWorkCenterList()
  getServiceCenterList()
  getConsultCenterList()
  // getMyWorkList()
})

const fill = ref('#6F6161')
const mouseenter = (event) => {
  let target: Element = event.target
  let svg = target.querySelector('.svg')
  svg?.setAttribute('fill', '#fff')
  let title = target.querySelector('.title')
  // title.style.color = '#fff'
}
const mouseleave = (event) => {
  let target: Element = event.target
  let svg = target.querySelector('.svg')
  svg?.setAttribute('fill', '#6F6161')
  let title = target.querySelector('.title')
  // title.style.color = '#6F6161'
}

const innerDiv = document.createElement('div')
innerDiv.style.height = '80px'
innerDiv.style.width = '80%'
innerDiv.style.margin = '0 auto'
innerDiv.style.border = '2px dotted black'

//拖拽触发，通常用于设置拖动数据
const dragstart = (item: any, type: string) => {
  startItem.value = item
  if (type == 'save') {
    btnTraggle.value = true
  } else {
    btnTraggle.value = false
  }
}

const handleDelete = () => {
  show.value = false
  formName.value = undefined
  componentName.value = undefined
}
// 删除组件
const deleteItem = async () => {
  console.log(startItem.value)
  if (startItem.value.id) {
    const res = await delLayoutData({ id: startItem.value.id })
  }
}

// 进入是处理数据
const dragover = () => {}
//离开释放区域
const dragleave = () => {}

// 拖拽是否触发方法
const drop = () => {
  if (startItem.value) {
    // 找到当前组件
    const map = customList.value.find((item) => item.id === startItem.value.itemId)
    if (map) {
      componentName.value = map.name
      formName.value = map.formName
      data.value.name = map.title //data是需要保存数据库的封装数据
      data.value.sign = map.name.name
    }
    // 指定当前组件信息
    const tmp = startItem.value
    data.value.icon = tmp?.icon
    data.value.category = tmp?.category
    data.value.itemId = tmp?.itemId
    console.log(data.value)

    //拖拽完成之后设置id 用于判断是 创建还是修改 数据
    //按钮切换
    if (btnTraggle.value) {
      propForm.value = null
    } else {
      let json = centerList.value.find((item) => item.id === startItem.value.id)
      propForm.value = json?.itemJson
    }
  }
  show.value = true
  try {
    if (contentMain.value) {
      contentMain.value.removeChild(innerDiv)
    }
  } catch (err) {}
}
</script>

<style lang="scss" scoped>
.conatiner {
  // margin-top: 4%;
  width: 100%;
  height: 97vh;
  padding: 0 20px;
  box-sizing: border-box;
  background-color: #fff;
}

.cus-header {
  height: 5%;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #d4d4d4;
}

.cus-button {
  position: absolute;
  right: 0%;
  bottom: 5%;
}

.cus-body {
  height: 80%;
  display: flex;
  width: 100%;
}

.body-left,
.body-right {
  min-width: 20%;
  overflow-y: auto;
}

.body-center {
  min-width: 60%;
  padding: 20px;
  background-color: #f5f5f5;
  height: 800px;
}

.body-content {
  background-color: #fff;
  height: 100%;
  overflow-y: auto;
  padding: 20px 10px;
}

.header {
  border-bottom: 1px solid #d4d4d4;
  height: 7%;
  position: relative;
}

.title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: bold;
}

.collapse-content {
  display: flex;
  flex-wrap: wrap;
}

.item {
  height: 80px;
  width: 80px;
  margin-left: 10px;
}

.item:hover {
  background-color: #b9d6fe;
  cursor: pointer;
}

.inner-div {
  height: 80px;
  width: 400px;
  background-color: #007bff;
  margin: 0 auto;
  border: 1px dotted black;
}

.div-border {
  border: 2px solid #007bff;
  height: auto;
  padding-bottom: 40px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.delete-btn {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.drag-title {
  text-align: center;
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-top: 10px;
  color: #007bff;
}
</style>
