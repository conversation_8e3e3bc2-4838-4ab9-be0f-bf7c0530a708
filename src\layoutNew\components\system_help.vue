<script setup lang="ts">
import * as portal from "@/api/portal";
import {getAccessToken} from "@/utils/auth";
import {Close, FullScreen} from "@element-plus/icons-vue"
import Document from '@/views/portal/helpDocument/document.vue';
import wxtsDialog from './wxts.vue'
import lxywDialog from './lxyw.vue'
import Template from "@/views/template.vue";

const baseUrl = import.meta.env.VITE_TOURL_PREFIX
const sideShow = ref(false);
const showBackTop = ref(false);
const wxtsDialogShow = ref(false);
const lxywDialogShow = ref(false);


//打开帮助文档
let title = ref("");
const full = ref(false);
let openDialog = ref(false);
const docHeight = ref(450);

const closeOpen = () => {
  sideShow.value = !sideShow.value
}
const showYjfk = async () => {
  const ret = await portal.getGuid();
  const url = baseUrl + "/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ad6b01070e794366ac11727ad66a0014&ID=" + ret + "&token=" + getAccessToken()
  openWindow(url, "意见反馈", 1346, 600)
}
const openWindow = (url: string, title: string, width: number, height: number) => {
  // 获取屏幕的宽度和高度
  const screenWidth = screen.width;
  const screenHeight = screen.height;
  // 计算窗口的x和y坐标以使其居中
  const x = (screenWidth - width) / 2;
  const y = (screenHeight - height) / 2;
  const newWindow = window.open(
    url,
    title,
    `width=${width},height=${height},left=${x},top=${y}`
  );
  if (newWindow && newWindow.focus) {
    newWindow.focus();
  }
  return newWindow;
};

//头部滚动事件
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  // 当滚动超过50px时，背景变为纯白色，否则是半透明
  if (scrollTop > 1) {
    showBackTop.value = true;
  } else {
    showBackTop.value = false;
  }
};
// 监听页面滚动
onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});
// 移除监听页面滚动
onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
const backTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

const updateTitle = (newTitle: string) => {
  title.value = newTitle;
}
const openFull = () => {
  full.value ? (full.value = false) : (full.value = true);
  if (full.value) {
    docHeight.value = 680
  } else {
    docHeight.value = 450
  }

}
const openHelp = () => {
  openDialog.value = true;
}
const showWxtsDialog = () => {
  wxtsDialogShow.value = true;
}
const closeDialog = () => {
  wxtsDialogShow.value = false;
  lxywDialogShow.value = false;
}
const showLxyw = () => {
  lxywDialogShow.value = true;
}
</script>

<template>
  <div class="help">
    <div class="help_side" :class="[sideShow?'animated slideOutUp':'wxts_dialog_close']">
      <div class="close-icon" @click="closeOpen">
        <el-icon color="#fff">
          <Close/>
        </el-icon>
      </div>
      <div class="item-row" @click="showWxtsDialog()">
        <img class="icon w-16 h-16" src="@/assets/imgs/help/wxts.png" alt="意见反馈"/>
        <span class="text">温馨提示</span>
      </div>
      <div class="item-row" @click="openHelp()">
        <img class="icon w-16 h-16" src="@/assets/imgs/help/help_work.png" alt="意见反馈"/>
        <span class="text">帮助文档</span>
      </div>
      <div class="item-row" @click="showLxyw">
        <img class="icon w-16 h-16" src="@/assets/imgs/help/lxyw.png" alt="意见反馈"/>
        <span class=" text">联系运维</span>
      </div>
      <div class="item-row" @click="showYjfk">
        <img class="icon w-16 h-16" src="@/assets/imgs/help/yjfk_mdpi.png" alt="意见反馈"/>
        <span class="text">意见反馈</span>
      </div>
      <div class="item-row" v-if="showBackTop" @click="backTop">
        <img class="w-16 h-16" src="@/assets/imgs/home/<USER>" alt="返回顶部"/>
        <span class="text">返回顶部</span>
      </div>
    </div>
    <transition name="fade">
      <div class="help_sm_side" v-if="!sideShow" :class="[sideShow?'animated slideOutDown':'']">
        <div class="align-center" @click="closeOpen" title="系统帮助">
          <img class="w-36 h-36" src="@/assets/imgs/home/<USER>" alt="系统帮助"/>
        </div>
        <div class="align-center" v-if="showBackTop" @click="backTop" title="返回顶部">
          <img class="w-36 h-36" src="@/assets/imgs/home/<USER>" alt="返回顶部"/>
        </div>
      </div>
    </transition>

    <!--  温馨提示弹窗  -->
    <wxtsDialog :show-dialog="wxtsDialogShow" @close-dialog="closeDialog"/>
    <lxywDialog :show-dialog="lxywDialogShow" @close-dialog="closeDialog"/>
    <!-- 帮助文档弹窗 -->
    <el-dialog
      v-model="openDialog"
      center
      :title="title"
      hight="80%"
      width="70%"
      top="10vh"
      append-to-body
      destroy-on-close
      :fullscreen="full">
      <template #header>
        <div class="avue-crud_dialog_header">
          <span>{{ title }}</span>
          <span class="avue-crud_dialog_menu" @click="openFull" style="cursor: pointer;">
                <el-icon>
                  <FullScreen/>
                </el-icon>
              </span>
        </div>
      </template>
      <Document @updateTitle="updateTitle" :docHeight="docHeight"/>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.wxts_dialog_close {
  position: fixed;
  right: 10px;
  bottom: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(30px);
  }
}

.help {
  position: fixed;
  z-index: 99999;
}

.animated {
  animation-duration: 1.5s;
  animation-fill-mode: both;
}

@keyframes slideOutUp {
  from {
    position: fixed;
    right: 20px;
    bottom: 0;
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, -20%, 0);
  }
}

.slideOutUp {
  animation-name: slideOutUp;
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
}

.slideOutDown {
  animation-name: slideOutDown;
}

.help_side {
  position: fixed;
  right: 20px;
  bottom: 0;

  .close-icon {
    cursor: pointer;
    font-size: 12px;
    position: absolute;
    left: -12px;
    top: -12px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    background: rgb(62, 163, 247);
  }
}

.help_dialog_close {
  display: none;
}

.item-row {
  width: 68px;
  //background: #e9e8ff;
  border: 1px solid #e9e8ff;
  border-radius: 4px;
  box-shadow: 0px 3px 10px rgba(5, 36, 82, 0.08);
  color: #333;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  margin-top: 5px;
  cursor: pointer;
  padding: 10px;
  background: #fff;
  .icon {
    width: 32px;
    height: 32px;
  }

  .text {
    margin-top: 8px;
    font-size: 14px;
  }
}

.item-row:hover {
  .text {
    color: rgb(85, 119, 255);
  }

}

.back_top {
  height: 36px;
  background: #fff;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  box-shadow: 0px 3px 10px rgba(5, 36, 82, 0.08);
  color: #333;
  align-items: center;
  justify-content: center;
  display: flex;
  margin-top: 10px;
  cursor: pointer;
}

.help_sm_side {
  height: 60px;
  width: 60px;
  position: fixed;
  cursor: pointer;
  right: 40px;
  bottom: 80px;
  z-index: 1000;

  .align-center {
    display: flex;
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 12px rgba(0, 0, 0, .12);
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;

    .w-36 {
      width: 36px;
    }

    .h-36 {
      height: 36px;
    }
  }

  .hide_luntan {
    width: 60px;
    height: 60px;
    margin-top: 10px;
    background: linear-gradient(135deg, #3E9FFF 0%, #176FF2 100%);
    border-radius: 4px;
  }
}

.back_pos {
  position: fixed;
  cursor: pointer;
  right: 20px;
  bottom: 40px;
  z-index: 1000;
  padding: 0 12px;
}

.avue-crud_dialog_header {
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: center;
  align-items: center;
}

.avue-crud_dialog_menu {
  margin-right: 3px;
}

.h-16 {
  height: 16px;
}

.w-16 {
  width: 16px;
}

.m-l-8 {
  margin-left: 8px;
}

.f-s-14 {
  font-size: 14px;
}

.p-l-10 {
  padding-left: 10px;
}

.p-l-5 {
  padding-left: 5px;
}
</style>
