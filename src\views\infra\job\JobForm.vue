<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="任务编码" prop="taskCode">
            <el-input v-model="formData.taskCode" placeholder="请输入任务编码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="任务分组" prop="taskGroup">
            <el-select
              v-model="formData.taskGroup"
              placeholder="请选择任务状态"
              clearable
              class="!w-240px"
            >
              <el-option label="系统平台" value="平台系统" />
              <el-option label="数据中台" value="数据中台" />
              <el-option label="电建通" value="电建通" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="CRON 表达式" prop="cron">
            <crontab v-model="formData.cron" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="ApiUrl">
            <DynamicTable :ModelType="'ApiUrl'" :tableList="formData.taskApiUrlList" :show="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="ApiSQL">
            <DynamicTable :ModelType="'ApiSQL'" :tableList="formData.taskApiSQLList" :show="true" />
          </el-form-item>
        </el-col>
      </el-row>
<!-- 
      <el-form-item label="是否启动" prop="status">
        <el-select v-model="formData.taskState" placeholder="请选择请求方式" style="width: 200px">
          <el-option label="开启" value="0" />
          <el-option label="暂停" value='4' />
        </el-select>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as JobApi from '@/api/infra/job'
import DynamicTable from './components/DynamicTable.vue'
import emitter from '@/utils/mitt'
defineOptions({ name: 'JobForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
interface TableType {
  id: string
  newTimerTaskID: string
  headerKey: string
  sortIndex: number
  headerValue: string
  requestUrl: string
  stepCode: string
  stepName: string
  requestType: string
  // sql
  execSQL: string
  connName: string
  remark: string
}

const formData = ref({
  id: '',
  taskName: '',
  cron: '',
  isDeleted: '',
  taskState: undefined,
  taskGroup: '',
  remark: '',
  lastRunTime: '',
  lastRunEndTime: '',
  taskCode: '',
  stateStr: '',
  createUser: '',
  createUserID: '',
  createTime: '',
  modifyUser: '',
  modifyUserID: '',
  modifyTime: '',
  taskApiUrlList: [],
  taskApiSQLList: []
})

// 执行任务列表
const apiSQLList = ref([])
const apiUrlList = ref([])

const formRules = reactive({
  taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  taskCode: [{ required: true, message: '处理器的名字不能为空', trigger: 'blur' }],
  cron: [{ required: true, message: 'CRON 表达式不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await JobApi.getTask(id)
      console.log(formData.value)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交按钮 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    formData.value.taskApiSQLList = apiSQLList.value
    formData.value.taskApiUrlList = apiUrlList.value

    const data = formData.value
    if (formType.value === 'create') {
      message.success(t('common.createSuccess'))
    } else {
      await JobApi.updateTask(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

//监听属性的变化
emitter.on('ApiUrlOrSQLData', (obj: any) => {
  if (obj.modelType) {
    if (obj.modelType === 'ApiUrl') {
      apiUrlList.value = obj.tableList
    }
    if (obj.modelType === 'ApiSQL') {
      apiSQLList.value = obj.tableList
    }
  }
})

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: '',
    taskName: '',
    cron: '',
    isDeleted: '',
    taskState: undefined,
    taskGroup: '',
    remark: '',
    lastRunTime: '',
    lastRunEndTime: '',
    taskCode: '',
    init: undefined,
    stateStr: '',
    createUser: '',
    createUserID: '',
    createTime: '',
    modifyUser: '',
    modifyUserID: '',
    modifyTime: '',
    taskApiUrlList: [],
    taskApiSQLList: []
  }
  formRef.value?.resetFields()
}
</script>
