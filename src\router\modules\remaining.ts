import {Layout} from '@/utils/routerHelper'
import alone from './alone'
const {t} = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)
 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'          设置该路由的图标
 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)
 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)
 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)
 activeMenu: '/dashboard'  显示高亮的路由路径
 followAuth: '/dashboard'  跟随哪个路由进行权限过滤
 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'RedirectChild',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/index',
    name: 'Home',
    meta: {},
    children: [
      {
        path: 'index',
        component: () => import('@/views/Home/Index.vue'),
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      }
    ]
  },
  // portal主页
  {
    path: '/HomeTheme',
    component: () => import('@/layoutNew/layout.vue'),
    name: 'HomeTheme',
    meta: {
      hidden: true
    },
    children: [
      // 新首页
      {
        path: 'newsHome',
        alias: 'newsHome/index',
        component: () => import('@/views/portal/home.vue'),
        name: "newsHome",
        meta: {
          hidden: true
        }
      },
    ]
  },
  {
    path: '/Portal',
    alias: '/',
    redirect: '/Portal/WorkCenter',
    component: () => import('@/layoutNew/layout.vue'),
    name: 'Portal',
    meta: {
      hidden: true
    },
    children: [
      // 服务中心
      {
        path: 'ServiceCenter',
        alias: 'ServiceCenter/Index',
        component: () => import('@/views/portal/serviceCenter/index.vue'),
        name: 'ad86013c-bf5d-49d4-a11c-15239cd47d55',
        meta: {
          hidden: true
        }
      },
      // 资讯中心
      {
        path: 'Home',
        alias: 'Home/Index',
        component: () => import('@/views/portal/newsCenter.vue'),
        name: 'ad86013c-b70d-46b1-9db4-9ff6e26e72f4',
        meta: {
          hidden: true
        }
      },
      // 办公中心
      {
        path: 'WorkCenter',
        redirect: '/Portal/WorkCenter/workIndex',
        alias: 'WorkCenter/workIndex',
        component: () => import('@/views/portal/officeCenter/workCenter/index.vue'),
        name: 'ad86013c-c8fa-46be-933b-58512a434a68',
        meta: {
          hidden: true
        },
        children: [
          // 办公中心
          {
            path: 'workIndex',
            alias: 'workIndex/Index',
            component: () => import('@/views/portal/officeCenter/index.vue'),
            name: "PortalWorkIndex",
            meta: {
              hidden: true
            }
          },
          // 我的待办
          {
            path: 'myWork',
            alias: 'myWork/Index',
            component: () => import('@/views/portal/myWork.vue'),
            name: "PortalMyWork",
            meta: {
              hidden: true
            }
          },
        ]
      },
      // 项目中心
      {
        path: 'ProjectCenter',
        alias: 'Home/ProjectCenter',
        component: () => import('@/views/portal/projectCenter.vue'),
        name: 'ad86013c-b1de-43d2-9eb0-c01b09ecfe52',
        meta: {
          hidden: true
        }
      },
      // 资讯详情页
      {
        path: 'NewsDetail',
        alias: 'NewsCenter/NewsDetail',
        component: () => import('@/views/portal/newsDetail.vue'),
        name: 'NewsDetail',
        meta: {
          hidden: true
        }
      },
      // 咨询列表
      {
        path: 'publicInfoList',
        alias: 'NewsCenter/publicInfoList',
        component: () => import('@/views/portal/publicInfoList.vue'),
        name: 'PublicInfoList',
        meta: {
          hidden: true
        }
      },
      // 门户系统个人中心
      {
        path: 'UserCenter',
        alias: 'UserCenter',
        component: () => import('@/views/portal/UserCenter.vue'),
        name: 'UserCenter',
        meta: {
          hidden: false
        },
        children: [
          // 基本信息
          {
            path: 'userInfo',
            alias: 'UserCenter/userInfo',
            component: () => import('@/views/portal/userCenter/userInfo.vue'),
            name: "userInfo",
            meta: {
              hidden: true
            }
          },
        ]
      },
      //个性化布局
      {
        path: 'Layout',
        alias: 'Layout',
        component: () => import('@/views/portal/gridLayout_test/PortalCustomization.vue'),
        name: 'Layout',
        meta: {
          hidden: true
        }
      },
      {
        path: 'newsLayout',
        alias: 'newsLayout',
        component: () => import('@/views/portal/gridLayout/newsCenterLayout.vue'),
        name: 'newsLayout',
        meta: {
          hidden: true
        }
      },

      // 布局页面
      {
        path: 'WarmPromptList',
        alias: 'WarmPromptList',
        component: () => import('@/views/portal/workCenter/WarmPromptList.vue'),
        name: 'WarmPromptList',
        meta: {
          hidden: true,
          title: "列表"
        }
      },
      {
        path: '/WarmPromptDetail',
        alias: 'WarmPromptDetail',
        component: () => import('@/views/portal/workCenter/WarmPromptDetail.vue'),
        name: 'WarmPromptDetail',
        meta: {
          hidden: true,
          title: "详情"
        }
      },
    ]
  },
  {
    path: '/user',
    component: Layout,
    name: 'UserInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'profile',
        component: () => import('@/views/Profile/Index.vue'),
        name: 'Profile',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: t('common.profile')
        }
      },
      {
        path: 'notify-message',
        component: () => import('@/views/system/notify/my/index.vue'),
        name: 'MyNotifyMessage',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '我的站内信'
        }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    name: 'dict',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'type/data/:dictType',
        component: () => import('@/views/system/dict/data/index.vue'),
        name: 'SystemDictData',
        meta: {
          title: '字典数据',
          noCache: true,
          hidden: true,
          canTo: true,
          icon: '',
          activeMenu: '/system/dict'
        }
      }
    ]
  },
  {
    path: '/codegen',
    component: Layout,
    name: 'CodegenEdit',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit',
        component: () => import('@/views/infra/codegen/EditTable.vue'),
        name: 'InfraCodegenEditTable',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '修改生成配置',
          activeMenu: 'infra/codegen/index'
        }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    name: 'JobL',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'job-log',
        component: () => import('@/views/infra/job/logger/index.vue'),
        name: 'InfraJobLog',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '调度日志',
          activeMenu: 'infra/job/index'
        }
      }
    ]
  },
  {
    path: '/portal/home/<USER>',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/mocklogin',
    component: () => import('@/views/MockLogin/MockLogin.vue'),
    name: 'mocklogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/Portal/MsgList',
    component: () => import('@/views/portal/message/msgList.vue'),
    name: 'MsgList',
    meta: {
      hidden: true
    }
  },
  {
    path: '/Portal/EditerList',
    component: () => import('@/views/portal/favorite/editerList.vue'),
    name: 'EditerList',
    meta: {
      hidden: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  },
  {
    path: '/empty',
    component: () => import('@/views/portal/empty.vue'),
    name: 'empty',
    meta: {
      hidden: true
    }
  },
  alone
]

export default remainingRouter
