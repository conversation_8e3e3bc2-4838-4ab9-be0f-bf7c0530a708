<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.spanStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'CollectForm'
})
const props = defineProps<{itemJson:any}>()

const form = ref({
  titleList: {},
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
  isShow: {
    showMywork: true,
    showRanKing: true
  }
})

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('collect', form.value)
  },
  { immediate: true, deep: true }
)

defineExpose({ form })

onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
})

</script>
