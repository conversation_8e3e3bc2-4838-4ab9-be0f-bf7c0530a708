<template>
  <div class="service-menu-item" :style="itemStyle.modelStyle">
    <ul class="custom-breadcrumb">
      <template v-for="(item, index) in enterpriseNature" :key="index">
        <li>
          <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
          <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
        </li>
      </template>
    </ul>
    <ul class="custom-breadcrumb">
      <template v-for="(item, index) in businessNature" :key="index">
        <li>
          <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
          <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
        </li>
      </template>
    </ul>
    <ul class="custom-breadcrumb">
      <template v-for="(item, index) in serverNature">
        <li :key="index" v-if="index == 0 || index == 2">
          <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
          <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
        </li>
      </template>
      <template v-for="(item, index) in serverNature">
        <li :key="index" v-if="index == 1">
          <span style="color: #e5e5e5; padding-right: 10px">|</span>
          <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
        </li>
      </template>
    </ul>
  </div>
</template>
<script setup lang="ts">
import $ from 'jquery'
import * as PortalApi from '@/api/system/portal'
import { ref, onMounted } from 'vue'
import emitter from '@/utils/mitt'

const enterpriseNature = ref()
const businessNature = ref()
const serverNature = ref()

interface itemType {
  titleList: {
    id: ''
    title: ''
  }
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {}
}

const onGoToLocation = function (id) {
  let eid = $(id)
  $('html,body').animate(
    {
      scrollTop: parseFloat(eid.offset().top) - 70
    },
    200
  )
}
const props = defineProps<{ itemJson: itemType; itemId: string; serviceList: any[] }>()

const loadData = async function (serviceList: any[]) {
  enterpriseNature.value = []
  serverNature.value = []
  businessNature.value = []

  // let result = await PortalApi.serviceCenterloadData({ Search: searchContent.value })
  let result
  if (serviceList != undefined) {
    result = serviceList
  } else {
    result = await PortalApi.serviceCenterloadData({ Search: '' })
  }

  result.forEach((firstLevel) => {
    //*******特殊处理*******
    if (
      firstLevel.Name == '党建工团' ||
      firstLevel.Name == '行政办公' ||
      firstLevel.Name == '三会决策' ||
      firstLevel.Name == '事项督办' ||
      firstLevel.Name == '纪监审法务'
    ) {
      enterpriseNature.value.push(firstLevel)
    } else if (
      firstLevel.Name == '外事管理' ||
      firstLevel.Name == '档案管理' ||
      firstLevel.Name == '用车管理'
    ) {
      serverNature.value.push(firstLevel)
    } else {
      businessNature.value.push(firstLevel)
    }

    //*******特殊处理*******
    if (firstLevel.SubMenu.length > 0) {
      firstLevel.SubMenu.forEach(function (secondLevel) {
        secondLevel.IsExpandCollapse = false
        if (secondLevel.SubMenu.length > 0) {
          secondLevel.IsMoreThanFour = secondLevel.SubMenu.length > 4
          secondLevel.ShowList = secondLevel.SubMenu.slice(0, 4)
          secondLevel.SwitchList = secondLevel.SubMenu.slice(4, secondLevel.SubMenu.length)
        }
        secondLevel.SubMenu = []
      })
    }
  })
}

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {}
})
const isShow = ref()

emitter.on('ServiceLeftNavFrom', (obj: any) => {
  init(obj)
})

const init = (itemJson: itemType) => {
  if (itemJson) {
    itemStyle.value = itemJson.itemStyle
    isShow.value = itemJson.isShow
  }
}

onMounted(() => {
  loadData(props.serviceList)
  init(props.itemJson)
})
</script>

<style scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/master.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/admin.css');
</style>
