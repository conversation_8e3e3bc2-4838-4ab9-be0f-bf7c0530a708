<template>
  <el-button plain @click="saveIdIds">保存</el-button>
  <el-input
      v-model="query"
      style="width: 240px"
      placeholder="名称"
      @input="onQueryChanged"
    />
    <el-button @click="toggleExpandAll">
      {{ isAllExpanded ? '收起' : '展开' }}
    </el-button>
    <div style="float: right;font-size: 12px;margin: 5px">
      联动选择子节点 <el-switch v-model="checkStrictlyInverted" />
    </div>
    <el-tree-v2
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      :highlight-current="true"
      node-key="id"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="checkedKeys"
      :check-strictly="checkStrictly"
      :show-checkbox="showCheckbox"
      :check-on-click-node="true"
      :expand-on-click-node="true"
      :filter-method="filterMethod"
      :item-size="44"
      @node-click="clickNode"
      @node-contextmenu="contextmenuNode"
      :height="windowHeight*0.72"
      @check-change="handleCheckChange"
    />

</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { ElTreeV2 } from 'element-plus'
import type {TreeNode, TreeNodeData} from 'element-plus/es/components/tree-v2/src/types'
const emit = defineEmits(['nodeClicked','nodeContextmenu','saveSucess']);
const message = useMessage() // 消息弹窗

const checkStrictly = ref(true);
const checkStrictlyInverted = computed({
  get() {
    return !checkStrictly.value;
  },
  set(value: boolean) {
    checkStrictly.value = !value;
  }
});
import {AGroupApi,GroupResReqVO} from '@/api/system/group'

const getKey = (prefix: string, id: number) => {
  return `${prefix}-${id}`
}
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  showCheckbox:{
    type: Boolean,
    required: false,
  },
  checkedKeys:{
    type: Array,
    required: false,
  }

});
const treeProps = {
  children: 'children',
  label: 'name',
};
const isAllExpanded = ref(false)
const query = ref('')
const treeRef = ref<InstanceType<typeof ElTreeV2>>()
const expandedKeys = ref(["MenuRoot"])
const showCheckbox = props.showCheckbox||ref(true)
const checkedKeys = props.checkedKeys||ref([])
const treeData = props.data
const onQueryChanged = (query: string) => {
  treeRef.value!.filter(query)
}
const filterMethod = (query: string, node: TreeNode) => {
  return node.name?.toString()?.includes(query) ?? false;
}

const toggleExpandAll = () => {
  isAllExpanded.value = !isAllExpanded.value;
  if (isAllExpanded.value) {
    expandAll(treeData);
  } else {
    collapseAll();
  }
};

// 展开所有行
const expandAll = (rows) => {
  const keys = getAllIds(rows);
  treeRef.value?.setExpandedKeys(keys)
};

//遍历所有的节点获取id
const getAllIds = (tree) => {
  return tree.reduce((acc, node) => {
    acc.push(node.id);
    if (node.children) {
      acc.push(...getAllIds(node.children));
    }
    return acc;
  }, []);
}

// 收起所有行
const collapseAll = () => {
  treeRef.value?.setExpandedKeys([])
};
//鼠标单击事件
const clickNode = (data: TreeNodeData, node: TreeNode, e: MouseEvent) => {
  emit('nodeClicked', node);
}
const contextmenuNode = (e: Event, data: TreeNodeData, node: TreeNode) => {
  emit('nodeContextmenu', node);
}
const windowHeight = ref(0);

onMounted(()=>{
  windowHeight.value = window.innerHeight;

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    windowHeight.value = window.innerHeight;
  });
})
const groupId = ref("");
const open = async (ids: any,id:string) => {
  checkStrictly.value = true;
  groupId.value = id;
  //获取ids里的数组值
  if (ids != null) {
    await nextTick()
    treeRef.value.setCheckedKeys(ids);
    // queryParams.parentID = id;
  }
};
const saveIdIds = async () => {
  if(groupId.value){
    //定义一个GroupResReqVO类型的data
    const groupResReq: GroupResReqVO = {
      groupId: groupId.value,
      resIds: treeRef.value.getCheckedKeys(),
    };
    const res = await AGroupApi.saveGroupRes(groupResReq)
    if(res){
      message.success("保存成功")
    }else{
      message.error("保存异常，请联系管理员")
    }
    emit('saveSucess');
  }
};

/**
 * 特殊联动操作
 * @param data
 * @param checked
 * @param indeterminate
 */
const handleCheckChange = (data, checked, indeterminate) => {
  //获取ref所有被选中的值
  const allCheckedKeys = treeRef.value.getCheckedKeys();
  //获取当前节点的所有子孙节点的key
  const allChildKeys = getAllChildKeys(data);
  //获取当前节点的父节点的key
  const parentKeys = getParentNodes(treeData, data);
  if (checked) {
    //当前节点被选中，则将其所有父类节点和子类节点都设置为选中状态
    let combinedArray = [...new Set([...allCheckedKeys,...parentKeys])];
    //设置整个树选中的key
    treeRef.value.setCheckedKeys(combinedArray);
  }else{
    //取消的时候，只取消其子节点
    if(allCheckedKeys.length>0){
      //从allCheckedKeys中删除allChildKeys
      const leave = allCheckedKeys.filter((key) => !allChildKeys.includes(key));
      treeRef.value.setCheckedKeys(leave);
    }
  }
};

//获取子节点
const getAllChildKeys = (node) => {
  const result = [];
  const traverse = (nodes) => {
    if (!nodes) return;
    nodes.forEach((n) => {
      result.push(n.id);
      if (n.children && n.children.length > 0) {
        traverse(n.children);
      }
    });
  };
  traverse(node.children);
  return result;
};
//获取父节点
const getParentNodes = (tree, node) => {
  const path = [];
  const findNode = (nodes, target) => {
    for (const n of nodes) {
      if (n.id === target.id) {
        return true;
      }
      if (n.children && n.children.length > 0) {
        path.push(n.id);
        if (findNode(n.children, target)) {
          return true;
        }
        path.pop();
      }
    }
    return false;
  };
  findNode(tree, node);
  return path;
};
defineExpose({open}) // 提供 open 方法，用于打开弹窗

</script>
