import { createApp, App } from 'vue'
import AutoLogoutDialog from '@/components/AutoLogoutDialog/index.vue'
import ElementPlus from 'element-plus'

class AutoLogoutDialogManager {
  private dialogApp: App | null = null
  private dialogInstance: any = null
  private container: HTMLElement | null = null

  /**
   * 显示自动退出登录弹窗
   */
  show() {
    if (this.dialogInstance) {
      // 如果已经存在，直接显示
      this.dialogInstance.show()
      return
    }

    // 创建容器
    this.container = document.createElement('div')
    this.container.id = 'auto-logout-dialog-container'
    document.body.appendChild(this.container)

    // 创建Vue应用实例
    this.dialogApp = createApp(AutoLogoutDialog)
    this.dialogApp.use(ElementPlus)

    // 挂载到容器
    this.dialogInstance = this.dialogApp.mount(this.container)

    // 显示弹窗
    this.dialogInstance.show()

    // 禁止页面滚动
    document.body.style.overflow = 'hidden'
  }

  /**
   * 隐藏弹窗
   */
  hide() {
    if (this.dialogInstance) {
      this.dialogInstance.hide()
    }
    
    // 恢复页面滚动
    document.body.style.overflow = ''
  }

  /**
   * 销毁弹窗
   */
  destroy() {
    if (this.dialogApp) {
      this.dialogApp.unmount()
      this.dialogApp = null
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
      this.container = null
    }

    this.dialogInstance = null
    
    // 恢复页面滚动
    document.body.style.overflow = ''
  }
}

// 创建全局实例
export const autoLogoutDialogManager = new AutoLogoutDialogManager()
