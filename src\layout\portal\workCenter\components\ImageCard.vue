<template>
  <div class="hbm-zxzx-cell zxzx_banner" m="t-4">
    <el-carousel trigger="click" :height="itemStyle.carouselHeadel" :interval="itemStyle.interval">
      <el-carousel-item v-for="(item, index) in banner" :key="index">
        <a @click="detail_url('TPXW', item.ID, '图片新闻')" :title="item.Title" target="_blank">
          <img
            :src="item.PicFile"
            class="TPXWImg"
            :style="itemStyle.imgStyle"
            @load="handleImageLoaded"
          />
          <div class="emptybox" v-if="loading_image">
            <div class="line-scale loading_icon">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
        </a>
        <div class="btm" :style="itemStyle.btmStyle" v-if="isShow.titleIsShow">
          <h3 :style="itemStyle.titleStyle" class="carousel-title">{{ item.Title }}</h3>
          <a
            class="more"
            :style="itemStyle.moreStyle"
            :href="
              item.link == undefined ? '/Portal/NewsCenter/PublicInfoList?code=TPXW' : item.link
            "
            target="_blank"
            >更多 ></a
          >
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ImageCard'
})
interface itemType {
  titleList: []
  itemStyle: {
    titleStyle: ''
    carouselHeadel: '500px'
    btmStyle: ''
    imgStyle: ''
    moreStyle: ''
    interval: 3000
  }
  isShow: {
    titleIsShow: boolean
  }
}
const props = defineProps<{ itemJson: itemType }>()

const itemStyle = ref({
  titleStyle: {},
  carouselHeadel: '',
  btmStyle: {},
  imgStyle: {},
  moreStyle: {},
  interval: 1000
})

const isShow = ref({
  titleIsShow: true
})

//监听属性
emitter.on('image', (obj: itemType) => {
  init(obj)
})
//解除绑定事件
onUnmounted(() => {
  emitter.off('image')
})

//图片加载
const loading_image = ref(true)
//图片列表
const banner = ref()

//获取图片列表
const get_tpxw = async () => {
  const ret = await PortalApi.newsCenterGetNewsList({ code: 'TPXW', pageSize: 8 })
  banner.value = ret.records
}

const get_banner = async () => {
  //系统脚本库 - 资讯中心_首页_抬头banner图片
  let result = await PortalApi.execSystemScript({
    code: 'SQL_ae0c00eda596438f90547de550efa3ed',
    ExecData: ''
  })
  banner.value = result
}

//跳转图片新闻
const detail_url = async (code, id, cate) => {
  const url =
    '/Portal/NewsCenter/NewsDetail?Code=' + code + '&ID=' + id + '&navigation=' + encodeURI(cate)
  window.open(url)
}

//监听图片加载
const handleImageLoaded = () => {
  if (!loading_image.value) {
    return
  }
  loading_image.value = false
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    itemStyle.value = itemJson.itemStyle
    isShow.value = itemJson.isShow
  }
  if (itemJson.titleList.length != 0) {
    banner.value = itemJson.titleList
  } else {
    get_tpxw()
  }
}

onMounted(() => {
  if (props.itemJson && !isEmptyObject2(props.itemJson)) {
    init(props.itemJson)
  } else {
    get_tpxw()
  }
  setTimeout(() => {
    // 图片动态最多1.5秒，1.5秒后关闭loading
    if (loading_image.value) {
      loading_image.value = false
    }
  }, 1500)
})

const isEmptyObject2 = (obj: object) => {
  return JSON.stringify(obj) === '{}'
}
</script>

<style lang="scss" scoped>
.hbm-zxzx-cell {
  background-color: transparent;
  width: 100%;
}

.btm {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);

  .carousel-title {
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 14px;
    font-family: '微软雅黑';
    text-align: center;

    display: inline-block;
    background: none;

    border: none;
    height: 40px;
    line-height: 40px;
    width: 85%;

    padding: 0 10px;
    margin: 0;
    box-sizing: border-box;

    overflow: hidden; //隐藏文字
    text-overflow: ellipsis; //显示 ...
    white-space: nowrap; //不换行
  }

  .more {
    width: 50px;
    font-size: 13px;
    color: #fff;
  }
}

:deep(.el-carousel__indicators) {
  position: absolute;
  bottom: 0px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: -10px;
}

.more {
  color: #fff;
  position: absolute;
  right: 2px;
}

a:hover,
a:active {
  text-decoration: none;
  color: #fff;
  /* 设置鼠标悬停时的颜色 */
}
.emptybox {
  width: 80px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  transform: translate(-50%, -50%);
}
.emptybox .loading_icon div {
  background: #999;
}
</style>
