<template>
  <div>
    <el-scrollbar height="100%">
      <div class="body" id="apps">
        <div class="body-inner">
          <!--抬头滚动图片-->
          <el-carousel
            trigger="click"
            height="338px"
            style="width: 1378px; margin-bottom: 15px"
            indicator-position="none"
          >
            <el-carousel-item
              v-for="(item, index) in banner"
              :key="index"
              style="background-color: #f9fafd"
            >
              <a :href="_url(item.url)" @click="topImgLog(item)" target="_blank">
                <img
                  v-show="!loading_image"
                  :src="proxy.$getFullUrl('/BasicApplication/DownloadFile?FileID=' + item.img)"
                  style="width: 100%; height: 338px; object-fit: cover"
                  @load="handleImageLoaded"
                />

                <div class="emptybox" v-if="loading_image" style="margin-top: 150px">
                  <div class="line-scale loading_icon">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              </a>
            </el-carousel-item>
          </el-carousel>

          <div class="hbm-zxzx-body-leftbar1">
            <div class="hbm-zxzx-sec hbm-zxzx-sec1">
              <div class="hbm-zxzx-row">
                <!--图片新闻-->
                <div class="hbm-zxzx-cell zxzx_banner" m="t-4">
                  <el-carousel trigger="click" height="338px">
                    <el-carousel-item v-for="(item, index) in tpxw" :key="index">
                      <a
                        :href="detail_url('TPXW', item.ID, '图片新闻', item)"
                        @click="NewsPageLog('TPXW', item, '图片新闻')"
                        :title="item.Title"
                        target="_blank"
                      >
                        <img
                          v-show="!loading_image2"
                          :src="item.PicFile"
                          class="TPXWImg"
                          @load="handleImage()"
                        />

                        <div class="emptybox" v-if="loading_image2" style="margin-top: 150px">
                          <div class="line-scale loading_icon">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                          </div>
                        </div>
                      </a>
                      <h3 class="carousel-title"
                        >{{ item.Title }}
                        <a
                          href="/Portal/NewsCenter/PublicInfoList?code=TPXW"
                          class="more"
                          target="_blank"
                          >更多 ></a
                        >
                      </h3>
                    </el-carousel-item>
                  </el-carousel>
                </div>

                <!--集团要闻&时政新闻-->
                <div class="hbm-zxzx-cell toper" style="width: 514px">
                  <div class="gcs_tab_jtywszxw">
                    <div class="gcs_tabhd">
                      <span
                        ><b><i>HOT</i></b></span
                      >
                      <ul>
                        <li
                          @click="tab1_index = 1"
                          :class="1 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                          atr="jtyw"
                        ></li>
                        <li
                          @click="tab1_index = 2"
                          :class="2 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                          style="margin-left: 50px"
                          atr="szxw"
                        ></li>
                      </ul>
                      <a
                        :href="
                          1 == tab1_index
                            ? '/Portal/NewsCenter/PublicInfoList?code=JTYW'
                            : '/Portal/NewsCenter/PublicInfoList?code=SZXW'
                        "
                        target="_blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>

                    <div class="gcs_tabbd">
                      <div v-if="1 == tab1_index" class="gcs_tabitem">
                        <ul class="article_list top_news" v-if="jtyw && jtyw.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in jtyw" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('XMRYRM', item.ID, '集团要闻', item)"
                              @click="NewsPageLog('XMRYRM', item, '集团要闻')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>

                        <el-empty v-else description="数据为空" />
                      </div>

                      <div v-if="2 == tab1_index" class="gcs_tabitem" v-cloak>
                        <ul class="article_list top_news" v-if="szxw && szxw.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in szxw" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="item.OtherWebAddres"
                              @click="NewsPageLog('SZXW', item, '时政新闻')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--院发文&人员任免&院通知&院公告-->
            <div class="hbm-zxzx-sec hbm-zxzx-sec2">
              <div class="hbm-zxzx-row">
                <!--院发文&人员任免-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_yfwbmfwryrm" cty="tab" grp="zxzx_yfwbmfwryrm">
                    <div class="gcs_tabhd" grp="zxzx_yfwbmfwryrm">
                      <ul>
                        <li
                          @click="tab2_index = 1"
                          :class="1 == tab2_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                        >
                          院发文
                        </li>
                        <li
                          @click="tab2_index = 2"
                          :class="2 == tab2_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                        >
                          人员任免
                        </li>
                      </ul>
                      <a
                        :href="
                          1 == tab2_index
                            ? '/Portal/NewsCenter/PublicInfoList?code=YNFW'
                            : '/Portal/NewsCenter/PublicInfoList?code=RYRM'
                        "
                        target="_blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>

                    <div class="gcs_tabbd" grp="zxzx_yfwbmfwryrm">
                      <div v-if="1 == tab2_index" class="gcs_tabitem" grp="zxzx_yfwbmfwryrm">
                        <ul class="article_list" v-if="yfw && yfw.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in yfw" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('YNFW', item.ID, '院发文', item)"
                              @click="NewsPageLog('YNFW', item, '院发文')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>

                        <el-empty v-else description="数据为空" />
                      </div>

                      <div v-if="2 == tab2_index" class="gcs_tabitem" grp="zxzx_yfwbmfwryrm">
                        <ul class="article_list">
                          <li class="item has_icon" v-for="(item, index) in ryrm" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('RYRM', item.ID, '人员任免', item)"
                              @click="NewsPageLog('RYRM', item, '人员任免')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <!--院通知-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_yntz gcs_tab_zxzx_single" cty="tab" grp="zxzx_yntz">
                    <div class="gcs_tabhd" grp="zxzx_yntz">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="yntz">
                          院通知
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <a
                        href="/Portal/NewsCenter/PublicInfoList?code=YNTZ"
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>

                    <div class="gcs_tabbd" grp="zxzx_yntz">
                      <div class="gcs_tabitem" grp="zxzx_yntz" atr="yntz">
                        <ul class="article_list" v-if="ytz && ytz.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in ytz" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('YNTZ', item.ID, '院内通知', item)"
                              @click="NewsPageLog('YNTZ', item, '院内通知')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>
                        <el-empty v-else description="数据为空" />
                      </div>
                    </div>
                  </div>
                </div>

                <!--院公告-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_yngg gcs_tab_zxzx_single" cty="tab" grp="zxzx_yngg">
                    <div class="gcs_tabhd" grp="zxzx_yngg">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="yngg">
                          院公告
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <a
                        href="/Portal/NewsCenter/PublicInfoList?code=YNGG"
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_yngg">
                      <div class="gcs_tabitem" grp="zxzx_yngg" atr="yngg">
                        <ul class="article_list" v-if="ygg && ygg.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in ygg" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('YNGG', item.ID, '院内公告', item)"
                              @click="NewsPageLog('YNGG', item, '院内公告')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>
                        <el-empty v-else description="数据为空" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--项目人员任免&部门发&重点报道-->
            <div class="hbm-zxzx-sec hbm-zxzx-sec3">
              <div class="hbm-zxzx-row">
                <!--项目人员任免-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                    <div class="gcs_tabhd" grp="zxzx_zdbd">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="XMRYRM">
                          项目人员任免
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <a
                        href="/Portal/NewsCenter/PublicInfoList?code=XMRYRM"
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>
                    <div class="gcs_tabbd">
                      <ul class="article_list" v-if="xmryrm && xmryrm.length > 0" v-cloak>
                        <li class="item" v-for="(item, index) in xmryrm" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('XMRYRM', item.ID, '项目人员任免', item)"
                            @click="NewsPageLog('XMRYRM', item, '项目人员任免')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>

                <!--部门发文-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                    <div class="gcs_tabhd" grp="zxzx_zdbd">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="BMFW">
                          部门发文
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <div class="hbm-moreico fa fa-list-ul"></div>
                      <a
                        href="/Portal/NewsCenter/PublicInfoList?code=BMFW"
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_zdbd">
                      <div class="gcs_tabitem" grp="zxzx_zdbd" atr="zdbd">
                        <ul class="article_list" v-if="bmfw && bmfw.length > 0" v-cloak>
                          <li class="item has_icon" v-for="(item, index) in bmfw" :key="index">
                            <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                            <a
                              class="link"
                              :href="detail_url('BMFW', item.ID, '部门发文', item)"
                              @click="NewsPageLog('BMFW', item, '部门发文')"
                              target="_blank"
                              :title="item.Title"
                              v-text="item.Title"
                            ></a>
                            <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                          </li>
                        </ul>
                        <el-empty v-else description="数据为空" />
                      </div>
                    </div>
                  </div>
                </div>

                <!--重点报道-->
                <div class="hbm-zxzx-cell" style="width: 383px">
                  <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                    <div class="gcs_tabhd" grp="zxzx_zdbd">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="zdbd">
                          重点报道
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <a
                        href="/Portal/NewsCenter/PublicInfoList?code=ZDBD"
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_zdbd">
                      <ul class="article_list" v-if="zdbd && zdbd.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in zdbd" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('ZDBD', item.ID, '重点报道', item)"
                            @click="NewsPageLog('ZDBD', item, '重点报道')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--财务资金-->
            <div class="hbm-zxzx-sec hbm-zxzx-sec4 wlaqsj">
              <div class="hbm-zxzx-row">
                <div class="hbm-zxzx-cell" style="width: 100%">
                  <div class="gcs_tab_wlaqzthd gcs_tab_zxzx_single" cty="tab" grp="zxzx_wlaqzthd">
                    <div class="gcs_tabhd" grp="zxzx_wlaqzthd">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="CWZJZL">
                          财务资金专栏
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_wlaqzthd">
                      <div class="gcs_tabitem" grp="zxzx_wlaqzthd" atr="wlaqzthd">
                        <div class="gcs_zxzx_wlaqzthd_childs_cont">
                          <div class="gcs_zxzx_wlaqzthd_listitem">
                            <div class="gcs_zxzx_wlaqzthd_zstz">
                              <div class="gcs_zxzx_ss_head" style="position: relative">
                                <span class="gcs_zxzx_ss_head_ico"></span>
                                <span class="gcs_zxzx_ss_head_title">财务资金</span>
                                <a
                                  href="/Portal/NewsCenter/PublicInfoList?code=CWZJ"
                                  target="__blank"
                                  class="hbm-moreico fa fa-list-ul"
                                ></a>
                              </div>
                              <div class="gcs_zxzx_ss_body">
                                <ul class="article_list" v-if="cwzj && cwzj.length > 0" v-cloak>
                                  <li
                                    class="item has_icon"
                                    v-for="(item, index) in cwzj"
                                    :key="index"
                                  >
                                    <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                    <a
                                      class="link"
                                      :href="detail_url('CWZJ', item.ID, '财务资金', item)"
                                      @click="NewsPageLog('CWZJ', item, '财务资金')"
                                      target="_blank"
                                      :title="item.Title"
                                      v-text="item.Title"
                                    ></a>
                                    <span
                                      class="datetime"
                                      v-text="format_datetime(item.CreateTime)"
                                    ></span>
                                  </li>
                                </ul>
                                <el-empty v-else description="数据为空" />
                              </div>
                            </div>
                          </div>

                          <div class="gcs_zxzx_wlaqzthd_listitem">
                            <div class="gcs_zxzx_wlaqzthd_wkt">
                              <div class="gcs_zxzx_ss_head" style="position: relative">
                                <span class="gcs_zxzx_ss_head_ico"></span>
                                <span class="gcs_zxzx_ss_head_title">税务管理</span>
                                <a
                                  href="/Portal/NewsCenter/PublicInfoList?code=SWGL"
                                  target="__blank"
                                  class="hbm-moreico fa fa-list-ul"
                                ></a>
                              </div>
                              <div class="gcs_zxzx_ss_body">
                                <ul class="article_list" v-if="swgl && swgl.length > 0" v-cloak>
                                  <li
                                    class="item has_icon"
                                    v-for="(item, index) in swgl"
                                    :key="index"
                                  >
                                    <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                    <a
                                      class="link"
                                      :href="detail_url('SWGL', item.ID, '税务管理', item)"
                                      @click="NewsPageLog('SWGL', item, '税务管理')"
                                      target="_blank"
                                      :title="item.Title"
                                      v-text="item.Title"
                                    ></a>
                                    <span
                                      class="datetime"
                                      v-text="format_datetime(item.CreateTime)"
                                    ></span>
                                  </li>
                                </ul>
                                <el-empty v-else description="数据为空" />
                              </div>
                            </div>
                          </div>

                          <div class="gcs_zxzx_wlaqzthd_listitem">
                            <div class="gcs_zxzx_wlaqzthd_wlaqsj">
                              <div class="gcs_zxzx_ss_head" style="position: relative">
                                <span class="gcs_zxzx_ss_head_ico"></span>
                                <span class="gcs_zxzx_ss_head_title">资产与产权管理</span>
                                <a
                                  href="/Portal/NewsCenter/PublicInfoList?code=ZCYCQ"
                                  target="__blank"
                                  class="hbm-moreico fa fa-list-ul"
                                ></a>
                              </div>
                              <div class="gcs_zxzx_ss_body">
                                <ul class="article_list" v-if="zcycq && zcycq.length > 0" v-cloak>
                                  <li
                                    class="item has_icon"
                                    v-for="(item, index) in zcycq"
                                    :key="index"
                                  >
                                    <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                    <a
                                      class="link"
                                      :href="detail_url('ZCYCQ', item.ID, '资产与产权管理', item)"
                                      @click="NewsPageLog('ZCYCQ', item, '资产与产权管理')"
                                      target="_blank"
                                      :title="item.Title"
                                      v-text="item.Title"
                                    ></a>
                                    <span
                                      class="datetime"
                                      v-text="format_datetime(item.CreateTime)"
                                    ></span>
                                  </li>
                                </ul>
                                <el-empty v-else description="数据为空" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--科技创新园地-->
            <div class="hbm-zxzx-sec hbm-zxzx-sec5">
              <div class="hbm-zxzx-row">
                <div class="hbm-zxzx-cell" style="width: 100% !important">
                  <div class="gcs_tab_kjcxyd gcs_tab_zxzx_single" cty="tab" grp="zxzx_kjcxyd">
                    <div class="gcs_tabhd" grp="zxzx_kjcxyd">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="yfw">
                          科技创新园地
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                      <a
                        v-cloak
                        v-if="kjcxy && kjcxy.length > 0"
                        :href="
                          '/Portal/NewsCenter/PublicInfoList?code=' + kjcxy[tab3_index]['Code']
                        "
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                      ></a>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_kjcxyd">
                      <div class="gcs_tabitem" grp="zxzx_kjcxyd" atr="yfw">
                        <div class="gcs_tab_kjcxyd_childs" cty="tab">
                          <div class="gcs_tabhd" style="float: left">
                            <ul v-cloak v-if="kjcxy && kjcxy.length > 0">
                              <li
                                @click="tab3_index = index"
                                :class="
                                  index == tab3_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'
                                "
                                atr="item1"
                                v-for="(item, index) in kjcxy"
                                :key="index"
                              >
                                <span class="gcs_tabliico"></span
                                ><span :title="item.CatalogName" v-text="item.CatalogName"></span>
                              </li>
                            </ul>
                            <el-empty v-else description="数据为空" />
                          </div>

                          <div class="gcs_tabbd" style="float: right; width: 938px">
                            <div class="gcs_tabitem" v-if="kjcxy && kjcxy.length > 0" v-cloak>
                              <div
                                class="gcs_newslist gcs_newslist_kjcx_dynamic"
                                v-for="(item, index) in kjcxy"
                                :key="index"
                              >
                                <templatete v-if="index == tab3_index">
                                  <div
                                    v-for="(i, idx) in item.list"
                                    :key="idx"
                                    class="gcs_newslist_item"
                                    cid="77783"
                                    :title="i.Title"
                                  >
                                    <span class="gcs_title bgtitle">
                                      <a
                                        :href="detail_url(item.Code, i.ID, item.CatalogName, item)"
                                        @click="NewsPageLog(item.Code, i, item.CatalogName)"
                                        target="_blank"
                                        :title="i.Title"
                                        v-text="i.Title"
                                      ></a>
                                    </span>
                                    <span class="gcs_dt" v-text="format_date(i.CreateTime)"></span>
                                  </div>
                                </templatete>
                              </div>
                            </div>
                            <el-empty v-else description="数据为空" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="hbm-zxzx-body-rightbar1">
            <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-ztzl">
              <div class="hbm-zxzx-row">
                <div class="hbm-zxzx-cell" style="width: 100%; height: 328px">
                  <div class="gcs_tab_ztzl gcs_tab_zxzx_single" cty="tab" grp="zxzx_ztzl">
                    <div class="gcs_tabhd" grp="zxzx_ztzl">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="ztzl"
                          >专题专栏
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_ztzl">
                      <div class="gcs_tabitem" grp="zxzx_ztzl" atr="ztzl">
                        <ul class="gcs_ztzl">
                          <li
                            class="gcs_ztzl_zlgh"
                            @click="
                              NewsPageLog(
                                'ZLGH',
                                {
                                  Title: '战略规划',
                                  url: 'http://10.10.1.173:8001/Portal/NewsCenter/PublicInfoList?code=ZLGH',
                                  tag: '战略规划'
                                },
                                '专题专栏'
                              )
                            "
                          >
                            <a
                              href="http://10.10.1.173:8001/Portal/NewsCenter/PublicInfoList?code=ZLGH"
                              target="_blank"
                              >战略规划</a
                            >
                          </li>

                          <li
                            class="gcs_ztzl_szkmy"
                            style="display: none"
                            @click="
                              NewsPageLog(
                                'SZKMY',
                                {
                                  Title: '数字昆明院',
                                  url: 'http://10.10.1.28:9003/',
                                  tag: '数字昆明院'
                                },
                                '专题专栏'
                              )
                            "
                          >
                            <a href="http://10.10.1.28:9003/" target="_blank">数字昆明院</a>
                          </li>

                          <li
                            class="gcs_ztzl_szyxt"
                            @click="
                              NewsPageLog(
                                'SZYXT',
                                {
                                  Title: '市政院系统',
                                  url: 'http://10.10.10.90:88'
                                },
                                '专题专栏'
                              )
                            "
                          >
                            <a href="http://10.10.10.90:88" target="_blank">市政院系统</a>
                          </li>

                          <li
                            class="gcs_ztzl_yzxx"
                            @click="
                              NewsPageLog(
                                'YZXX',
                                {
                                  Title: '院长信箱',
                                  url: 'http://10.10.1.29/yzxx/YZXX_List.asp'
                                },
                                '专题专栏'
                              )
                            "
                          >
                            <a href="http://10.10.1.29/yzxx/YZXX_List.asp" target="_blank"
                              >院长信箱</a
                            >
                          </li>

                          <li
                            class="gcs_ztzl_jlbgt"
                            @click="
                              NewsPageLog(
                                'JLYS',
                                {
                                  Title: '减利因素曝光台',
                                  url: '/Portal/NewsCenter/PublicInfoList?code=JLBGT'
                                },
                                '专题专栏'
                              )
                            "
                          >
                            <a
                              href="/Portal/NewsCenter/PublicInfoList?code=JLBGT"
                              title="减利因素曝光台"
                              target="_blank"
                              >减利因素曝光台</a
                            >
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-ksrk">
              <div class="hbm-zxzx-row">
                <div class="hbm-zxzx-cell" style="width: 100%; min-height: 300px">
                  <div class="gcs_tab_ksrk gcs_tab_zxzx_single" cty="tab" grp="zxzx_ksrk">
                    <div class="gcs_tabhd" grp="zxzx_ksrk">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="yfw">
                          快速入口
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_ksrk">
                      <div class="list" v-cloak v-if="ksrk && ksrk.length > 0">
                        <a
                          target="_blank"
                          :title="item.Name"
                          class="item"
                          v-for="(item, index) in ksrk"
                          :key="index"
                          :href="_url(item.Url)"
                          @click="NewsPageLog('KSRK', item, '快速入口')"
                        >
                          <span class="text" v-text="item.Name"></span>
                          <span class="gcs_aaarow_flag">>></span>
                        </a>
                      </div>
                      <div class="list emptybox" v-else style="margin-top: 160px">
                        <div class="no-data">
                          <img style="width: 80px; heigth: 80px" src="@/assets/imgs/nodata.png" />
                          <div class="text" style="color: #999; margin-top: 10px">暂无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-yqlj">
              <div class="hbm-zxzx-row">
                <div class="hbm-zxzx-cell" style="width: 100%; min-height: 300px">
                  <div class="gcs_tab_yqlj gcs_tab_zxzx_single" cty="tab" grp="zxzx_yqlj">
                    <div class="gcs_tabhd" grp="zxzx_yqlj">
                      <ul>
                        <li class="gcs_tabhd_item gcs_cur" atr="yqlj">
                          友情链接
                          <div class="gcs_cur_inner"></div>
                        </li>
                      </ul>
                    </div>
                    <div class="gcs_tabbd" grp="zxzx_yqlj">
                      <div class="list" v-cloak v-if="yqlj && yqlj.length > 0">
                        <a
                          :href="item.Url"
                          target="_blank"
                          :title="item.Name"
                          class="item"
                          v-for="(item, index) in yqlj"
                          :key="index"
                          @click="NewsPageLog('YQLJ', item, '友情链接')"
                        >
                          <span class="text" v-text="item.Name"></span>
                          <span class="gcs_aaarow_flag">>></span>
                        </a>
                      </div>
                      <div class="list emptybox" v-else style="margin-top: 80px">
                        <div class="no-data">
                          <img style="width: 80px; heigth: 80px" src="@/assets/imgs/nodata.png" />
                          <div class="text" style="color: #999; margin-top: 10px">暂无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import $ from 'jquery'
import { getAccessToken } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'
import { getCurrentInstance } from 'vue'
export default {
  data() {
    return {
      banner: [], //顶部轮播图抬头图片
      tpxw: [], //图片新闻
      jtyw: [], //集团要闻
      szxw: [], //时政新闻
      yfw: [], //院发文
      ryrm: [], //人员任免
      ytz: [], //院通知
      ygg: [], //院公告
      ksrk: [], //快速入口
      xmryrm: [], //项目人员任免
      bmfw: [], //部门发文
      zdbd: [], //重点报道
      yqlj: [], //友情链接
      //fbgs: [], //分包公示
      //fbgs_count: 0, //分包公示总数
      //zstz: [], //知识拓展
      //wkt: [], //微课堂
      //wlaqsj: [], //网络安全事件
      cwzj: [], //财务资金
      swgl: [], //税务管理
      zcycq: [], //资产与产权
      kjcxy: [], //科技创新园地
      tab1_index: 1, //集团要闻&时政新闻tab切换索引
      tab2_index: 1, //院发文&人员任免tab切换索引
      tab3_index: 0, //科技创新园tab切换索引
      proxy: {},
      loading_image: false,
      loading_image2: false,
      imageCount: 0,
      baseUrl: import.meta.env.VITE_TOURL_PREFIX
    }
  },

  created: function () {
    this.loading_image = true
    this.loading_image2 = true
    this.get_banner() //获取 - 顶部轮播图抬头图片
    this.get_tpxw() //获取 - 图片新闻
    this.get_jtyw() //获取 - 集团要闻
    this.get_szxw() //获取 - 时政新闻
    this.get_yfw() //获取 - 院发文
    this.get_ryrm() //获取 - 人员任免
    this.get_ytz() //获取 - 院通知
    this.get_ygg() //获取 - 院公告
    this.get_ksrk() //获取 - 快速入口
    this.get_xmryrm() //获取 - 项目人员任免
    this.get_bmfw() //获取 - 部门发文
    this.get_zdbd() //获取 - 重点报道
    this.get_yqlj() //获取 - 友情链接
    //this.get_fbgs(); //获取 - 分包公示
    //this.get_zstz(); //获取 - 知识拓展
    //this.get_wkt(); //获取 - 微课堂
    //this.get_wlaqsj(); //获取 - 网络安全事件
    this.get_cwzj()
    this.get_swgl()
    this.get_zcycq()
    this.get_kjcxy() //获取 - 科技创新园地
    setTimeout(() => {
      // 图片动态最多3秒，3秒后关闭loading
      if (this.loading_image) {
        this.loading_image = false
      }
      if (this.loading_image2) {
        this.loading_image2 = false
      }
    }, 1500)
  },

  mounted: function () {
    const { proxy } = getCurrentInstance()
    this.proxy = proxy
  },

  //vue需要的方法
  methods: {
    //顶部抬头轮播图
    get_banner: async function () {
      //系统脚本库 - 资讯中心_首页_抬头banner图片
      let result = await PortalApi.execSystemScript({
        code: 'SQL_ae0c00eda596438f90547de550efa3ed',
        ExecData: ''
      })
      this.banner = result
      let banner_timer = setInterval(function () {
        if (this.banner && this.banner.length > 0) {
          clearInterval(banner_timer)
          //顶部抬头banner图片swiper
          let banner_swiper = new Swiper('.banner_swiper', {
            loop: true,
            speed: 1200,
            autoplay: {
              delay: 6000 //5秒切换一次
            },
            navigation: {
              nextEl: '.swiper-button-next1',
              prevEl: '.swiper-button-prev1'
            }
          })
        }
      }, 100)
    },

    //banner图片新闻
    get_tpxw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'TPXW', pageSize: 8 })
      this.tpxw = result.records
      let tpxw_timer = setInterval(function () {
        if (this.tpxw && this.tpxw.length > 0) {
          clearInterval(tpxw_timer)
          //图片新闻swiper
          let news_banner_swiper = new Swiper('.news_banner_swiper', {
            loop: true,
            speed: 800,
            autoplay: {
              delay: 4000 //5秒切换一次
            },
            pagination: {
              el: '.swiper-pagination',
              clickable: true
            },
            navigation: {
              nextEl: '.swiper-button-next2',
              prevEl: '.swiper-button-prev2'
            }
          })
        }
      }, 100)
    },

    //集团要闻
    get_jtyw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'JTYW', pageSize: 7 })
      this.jtyw = result.records
    },

    //时政要闻
    get_szxw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'SZXW', pageSize: 7 })
      this.szxw = result.records
    },

    //院发文
    get_yfw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNFW', pageSize: 6 })
      this.yfw = result.records
    },

    //人员任免
    get_ryrm: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'RYRM', pageSize: 6 })
      this.ryrm = result.records
    },

    //院通知
    get_ytz: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNTZ', pageSize: 6 })
      this.ytz = result.records
    },

    //院公告
    get_ygg: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNGG', pageSize: 6 })
      this.ygg = result.records
    },

    //快速入口
    get_ksrk: async function () {
      let result = await PortalApi.newsCenterGetLinks({ type: '快速入口', page: 1, pageSize: 10 })
      this.ksrk = result.records
    },

    //项目人员任免
    get_xmryrm: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'XMRYRM', pageSize: 6 })
      this.xmryrm = result.records
    },

    //部门发文
    get_bmfw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'BMFW', pageSize: 6 })
      this.bmfw = result.records
    },

    //重点报道
    get_zdbd: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'ZDBD', pageSize: 6 })
      this.zdbd = result.records
    },

    //友情链接
    get_yqlj: async function () {
      let result = await PortalApi.newsCenterGetLinks({ type: '友情链接', page: 1, pageSize: 6 })
      this.yqlj = result.records
    },

    //分包公示
    get_fbgs: async function () {
      //系统脚本库 - 资讯中心_首页_分包公示_列表
      const userStore = useUserStore()
      let result = await PortalApi.execSystemScript({
        code: 'SQL_ae0c00bd40c0463d920c662411019169',
        ExecData: JSON.stringify([
          { UserDeptID: userStore.getUser.deptId, UserID: userStore.getUser.id }
        ])
      })
      this.fbgs = result

      //系统脚本库 - 资讯中心_首页_分包公示_总数
      let fbgsResult = await PortalApi.execSystemScript({
        code: 'SQL_ae0400f1a54a4a1199c46d5296d9aed7',
        ExecData: ''
      })
      this.fbgs_count = result[0]['Column1']
    },

    //知识拓展
    get_zstz: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'ZSTZ', pageSize: 5 })
      this.zstz = result.records
    },

    //微课堂
    get_wkt: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'WKT', pageSize: 5 })
      this.wkt = result.records
    },

    //网络安全事件
    get_wlaqsj: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'WLAQSJ', pageSize: 5 })
      this.wlaqsj = result.records
    },
    //财务资金
    get_cwzj: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'CWZJ', pageSize: 5 })
      this.cwzj = result.records
    },
    //税务管理
    get_swgl: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'SWGL', pageSize: 5 })
      this.swgl = result.records
    },
    //资产与产权管理
    get_zcycq: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'ZCYCQ', pageSize: 5 })
      this.zcycq = result.records
    },
    //科技创新园地
    get_kjcxy: async function () {
      let result = await PortalApi.newsCenterGetTechnologicalInnovation({ pageSize: 11 })
      //循环data，反序列化list为json格式
      // for (let i = 0; i < result.length; i++) {
      //   result[i].list = JSON.parse(result[i].list)
      // }
      this.kjcxy = result
    },

    //截取日期，格式：月-日
    format_datetime: function (time) {
      let date_str = /\d{4}-\d{1,2}-\d{1,2}/g.exec(time)[0]
      let date_arr = date_str.split('-')
      return date_arr[1] + '-' + date_arr[2]
    },

    //截取日期，格式：年-月-日
    format_date: function (time) {
      let date_str = /\d{4}-\d{1,2}-\d{1,2}/g.exec(time)[0]
      return date_str
    },

    //新闻详情url
    detail_url: function (code, id, cate, item) {
      return (
        '/Portal/NewsCenter/NewsDetail?Code=' +
        code +
        '&ID=' +
        id +
        '&navigation=' +
        encodeURI(cate)
      ) //url中文编码
    },
    //监听图片加载
    handleImageLoaded: function () {
      if (!this.loading_image) {
        return
      }
      this.loading_image = false
    },
    handleImage: function () {
      if (!this.loading_image2) {
        return
      }
      this.loading_image2 = false
    },
    //分包公示详情url
    fbgs_detail_url: function (_id) {
      return (
        '/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ac4000eb3bc34ec395451892f25ae8b5&FuncType=View&ID=' +
        _id +
        '&token=' +
        getAccessToken()
      )
    },

    //头部新闻baner图片url
    _url: function (url) {
      if (url != null && url != '') {
        if (url.indexOf('http') == -1 && url.indexOf('https') == -1) {
          if (url.indexOf('/UIBuilder/UIViewer/') != -1) {
            url = this.baseUrl + url
          }
        }

        if (url.indexOf('ftp://') == -1) {
          //排除ftp链接
          if (url.indexOf('?') > 0) {
            url += '&' //如果已经有？号则拼接&符号
          } else {
            url += '?' //如果没有？号则拼接?符号
          }
          if (url.indexOf('token') == -1) {
            //最后拼接token
            url += 'token=' + getAccessToken()
          }
        }
      }
      // console.log(url)
      return url
    },
    topImgLog(item) {
      const objdata = {
        pagename: item.title,
        pageurl: item.url,
        tag: '咨询中心',
        id: item.ID
      }
      const log = LogApi.pageLog(objdata)
    },
    NewsPageLog(code, item, tag) {
      const objdata = {
        pagename: item.Title ? item.Title : item.Name,
        pageurl: item.Title ? this.detail_url(code, item.ID, tag) : item.Url,
        tag: tag,
        id: item.ID
      }
      const log = LogApi.pageLog(objdata)
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/newsCenterMaster.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/newsCenterIndex.css');
@import url('@/assets/css/swiper.min.css');
@import url('@/assets/css/uikit.min.css');
@import url('@/assets/css/font-awesome.css');

.qkg {
  display: inline-block;
  background: none;
  border: none;
  color: #fff;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 0;
  font-family: '微软雅黑';
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.body-inner {
  width: 1378px;
  /*margin-left: 262px;*/
  margin: 0 auto;
  height: 100%;
  background-color: transparent;
  margin-top: 74px;
}

.hbm-zxzx-body-leftbar1 {
  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
}

.gcs_ztzl {
  padding: 0;
}

.demonstration {
  color: var(--el-text-color-secondary);
}

// .el-carousel__item h3 {
//   // color: #475669;
//   opacity: 0.75;
//   // line-height: 40px;
//   margin: 0;
//   text-align: center;
// }

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  // background-color: #d3dce6; //图片背景颜色
  background-color: #ffffff;
}

.TPXWImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-title {
  color: #fff;
  position: absolute;
  bottom: 0px;
  left: 0px;
  font-size: 14px;
  text-align: center;
  // opacity: 0.85;

  display: inline-block;
  background: none;
  border: none;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 0;
  font-family: '微软雅黑';
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  background: rgba(0, 0, 0, 0.3);
}

:deep(.el-carousel__indicators) {
  // padding-bottom: 15px;
  position: absolute;
  bottom: -14px;
  // left: 0px;
}

.more {
  color: #fff;
  position: absolute;
  right: 2px;
}

a:hover,
a:active {
  text-decoration: none;
  color: rgb(38, 31, 31);
  /* 设置鼠标悬停时的颜色 */
}
</style>
