<template>
  <div class="container">
    <h2 class="title">可拖拽面板示例</h2>
    
    <div class="panel-container">
      <DraggablePanel
        v-for="(panel, index) in panels"
        :key="index"
        :title="panel.title"
        :subtitle="panel.subtitle"
        :initialX="panel.x"
        :initialY="panel.y"
        :initialWidth="panel.width"
        :initialHeight="panel.height"
        :showNumber="panel.showNumber"
        :numberCount="panel.numberCount"
        @more-click="handleMoreClick(index)"
      >
        <div class="panel-demo-content">
          <div class="content-row">这是可拖拽面板 #{{ index + 1 }}</div>
          <div class="content-row">可以自由拖动位置和调整大小</div>
          <div v-if="index === 0" class="panel-actions">
            <button class="action-btn" @click="addPanel">添加面板</button>
          </div>
        </div>
      </DraggablePanel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DraggablePanel from './DraggablePanel.vue'

// 面板数据
const panels = ref([
  {
    title: '面板1',
    subtitle: 'Panel One',
    x: 50,
    y: 100,
    width: 400,
    height: 300,
    showNumber: true,
    numberCount: 5
  },
  {
    title: '面板2',
    subtitle: 'Panel Two',
    x: 500,
    y: 100,
    width: 350,
    height: 250,
    showNumber: false,
    numberCount: 0
  },
  {
    title: '面板3',
    subtitle: 'Panel Three',
    x: 300,
    y: 400,
    width: 450,
    height: 280,
    showNumber: true,
    numberCount: 12
  }
])

// 处理"更多"按钮点击
const handleMoreClick = (index: number) => {
  alert(`您点击了面板 ${panels.value[index].title} 的更多按钮`)
}

// 添加新面板
const addPanel = () => {
  const newPanelIndex = panels.value.length + 1
  
  panels.value.push({
    title: `面板${newPanelIndex}`,
    subtitle: `Panel ${newPanelIndex}`,
    x: 100 + Math.random() * 200,
    y: 150 + Math.random() * 200,
    width: 350 + Math.random() * 100,
    height: 250 + Math.random() * 100,
    showNumber: Math.random() > 0.5,
    numberCount: Math.floor(Math.random() * 20)
  })
}
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
  position: relative;
}

.title {
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.panel-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 100px);
}

.panel-demo-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .content-row {
    margin-bottom: 10px;
    color: #555;
  }
  
  .panel-actions {
    margin-top: auto;
    
    .action-btn {
      background-color: #1e87f0;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #0f7ae5;
      }
    }
  }
}
</style>
