<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="父ID" prop="parentID">
        <el-input
          v-model="queryParams.parentID"
          placeholder="请输入父ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="全ID" prop="fullID">
        <el-input
          v-model="queryParams.fullID"
          placeholder="请输入全ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="排序索引" prop="sortIndex">
        <el-input
          v-model="queryParams.sortIndex"
          placeholder="请输入排序索引"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="code" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入code"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="节点类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择节点类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="iconcls" prop="iconCls">
        <el-input
          v-model="queryParams.iconCls"
          placeholder="请输入iconcls"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="主界面地址" prop="url">
        <el-input
          v-model="queryParams.url"
          placeholder="请输入主界面地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="控制类型" prop="ctrlType">
        <el-select
          v-model="queryParams.ctrlType"
          placeholder="请选择控制类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="权限，可以为页面控件ID，数据的查询条件" prop="auth">
        <el-input
          v-model="queryParams.auth"
          placeholder="请输入权限，可以为页面控件ID，数据的查询条件"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="子系统编号" prop="systemCode">
        <el-input
          v-model="queryParams.systemCode"
          placeholder="请输入子系统编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建用户id" prop="createUserID">
        <el-input
          v-model="queryParams.createUserID"
          placeholder="请输入创建用户id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="修改用户" prop="modifyUser">
        <el-input
          v-model="queryParams.modifyUser"
          placeholder="请输入修改用户"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="修改用户id" prop="modifyUserID">
        <el-input
          v-model="queryParams.modifyUserID"
          placeholder="请输入修改用户id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifyTime">
        <el-date-picker
          v-model="queryParams.modifyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="节点状态（未发布，已发布）" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择节点状态（未发布，已发布）"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="节点编辑权限" prop="editAuth">
        <el-input
          v-model="queryParams.editAuth"
          placeholder="请输入节点编辑权限"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="节点编辑权限" prop="editAuthUser">
        <el-input
          v-model="queryParams.editAuthUser"
          placeholder="请输入节点编辑权限"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="节点的连接页面" prop="configUrl">
        <el-input
          v-model="queryParams.configUrl"
          placeholder="请输入节点的连接页面"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="主数据库连接" prop="mainDBConn">
        <el-input
          v-model="queryParams.mainDBConn"
          placeholder="请输入主数据库连接"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="relateId" prop="relateID">
        <el-input
          v-model="queryParams.relateID"
          placeholder="请输入relateId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="关联表" prop="relateTable">
        <el-input
          v-model="queryParams.relateTable"
          placeholder="请输入关联表"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否主界面" prop="isMainUrl">
        <el-input
          v-model="queryParams.isMainUrl"
          placeholder="请输入是否主界面"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否删除" prop="isDeleted">
        <el-input
          v-model="queryParams.isDeleted"
          placeholder="请输入是否删除"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="isStandard" prop="isStandard">
        <el-input
          v-model="queryParams.isStandard"
          placeholder="请输入isStandard"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:configmanage:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:configmanage:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="父ID" align="center" prop="parentID" />
      <el-table-column label="全ID" align="center" prop="fullID" />
      <el-table-column label="排序索引" align="center" prop="sortIndex" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="code" align="center" prop="code" />
      <el-table-column label="节点类型" align="center" prop="type" />
      <el-table-column label="iconcls" align="center" prop="iconCls" />
      <el-table-column label="主界面地址" align="center" prop="url" />
      <el-table-column label="控制类型" align="center" prop="ctrlType" />
      <el-table-column label="权限，可以为页面控件ID，数据的查询条件" align="center" prop="auth" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="子系统编号" align="center" prop="systemCode" />
      <el-table-column label="创建人" align="center" prop="createUser" />
      <el-table-column label="创建用户id" align="center" prop="createUserID" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="修改用户" align="center" prop="modifyUser" />
      <el-table-column label="修改用户id" align="center" prop="modifyUserID" />
      <el-table-column
        label="修改时间"
        align="center"
        prop="modifyTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="节点状态（未发布，已发布）" align="center" prop="status" />
      <el-table-column label="节点编辑权限" align="center" prop="editAuth" />
      <el-table-column label="节点编辑权限" align="center" prop="editAuthUser" />
      <el-table-column label="节点的连接页面" align="center" prop="configUrl" />
      <el-table-column label="主数据库连接" align="center" prop="mainDBConn" />
      <el-table-column label="relateId" align="center" prop="relateID" />
      <el-table-column label="关联表" align="center" prop="relateTable" />
      <el-table-column label="是否主界面" align="center" prop="isMainUrl" />
      <el-table-column label="是否删除" align="center" prop="isDeleted" />
      <el-table-column label="isStandard" align="center" prop="isStandard" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:configmanage:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:configmanage:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ConfigmanageForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ConfigmanageApi, ConfigmanageVO } from '@/api/system/configmanage'
import ConfigmanageForm from './ConfigmanageForm.vue'

/** 系统配置结构树 列表 */
defineOptions({ name: 'Configmanage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ConfigmanageVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  parentID: undefined,
  fullID: undefined,
  sortIndex: undefined,
  name: undefined,
  code: undefined,
  type: undefined,
  iconCls: undefined,
  url: undefined,
  ctrlType: undefined,
  auth: undefined,
  description: undefined,
  systemCode: undefined,
  createUser: undefined,
  createUserID: undefined,
  createTime: [],
  modifyUser: undefined,
  modifyUserID: undefined,
  modifyTime: [],
  status: undefined,
  editAuth: undefined,
  editAuthUser: undefined,
  configUrl: undefined,
  mainDBConn: undefined,
  relateID: undefined,
  relateTable: undefined,
  isMainUrl: undefined,
  isDeleted: undefined,
  isStandard: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ConfigmanageApi.getConfigmanagePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ConfigmanageApi.deleteConfigmanage(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ConfigmanageApi.exportConfigmanage(queryParams)
    download.excel(data, '系统配置结构树.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
