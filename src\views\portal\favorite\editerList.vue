<template>
  <!-- 搜索框 -->
  <div class="mb-4" style="margin-top: 20px; margin-left: 20px;">
    <el-button :icon="Plus">添加</el-button>
    <el-button :icon="Delete">移除</el-button>
    <el-button :icon="Top">上移</el-button>
    <el-button :icon="Bottom">下移</el-button>
    <el-button :icon="DocumentChecked">保存</el-button>
    <el-input v-model="search" style="width: 240px;margin-left: 60%;" placeholder="请输入关键字" :suffix-icon="Search" />
  </div>
  <!-- 表格 -->

  <el-table :data="tableData" style="width: 100%">
    <el-table-column type="selection" width="200" />
    <el-table-column prop="date" label="菜单">
      <template #default="scope">
        <el-input v-if="scope.row.menu" v-model="scope.row.date">
          <template #append>
            <el-button :icon="CloseBold" @click="rowClose(scope.row)" />
            <el-button :icon="MoreFilled" @click="treeViewShow(scope.row)" />
          </template>
        </el-input>
        <span @click="scope.row.menu = true" v-else>{{ scope.row.date }}</span>
      </template>
    </el-table-column>

    <el-table-column prop="name" label="别名">
      <template #default="scope">
        <el-input v-if="scope.row.alias" v-model="scope.row.name" />
        <span v-else @click="scope.row.alias = true">{{ scope.row.name }}</span>
      </template>
    </el-table-column>
  </el-table>


  <!-- 弹出框 -->
  <el-dialog v-model="dialogTreeisible" title="个人授权菜单选择" width="400" style="margin-top: 15%">
    <el-input v-model="filterText" style="width: 100%;margin-bottom: 20px" placeholder="请输入节点名称">
      <template #append>
        <el-button :icon="CloseBold" @click="filterText = ''" />
      </template>
    </el-input>
    <div style=" height: 400px;display: block;overflow-y: scroll;">
      <el-tree ref="treeRef" style="max-width: 600px;" :height="100" class="filter-tree" :data="dataTree"
        :props="defaultProps" accordion :filter-node-method="filterNode" @node-click="handleNodeClick"/>
    </div>
  </el-dialog>

</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Search, Plus, Delete, Top, Bottom, DocumentChecked, MoreFilled, CloseBold } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus';
import * as ByResourceApi from '@/api/system/byResource'
import { defaultProps } from '@/utils/tree'
const search = ref('')
const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

// 树组件
interface Tree {
  name: string
  children?: Tree[]
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})


//树点击事件
const handleNodeClick = (node) => {
  // 在这里添加你的逻辑代码
  alert(node.name)
}

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}

const dialogTreeisible = ref<boolean>(false)
//树弹窗梳理
const treeViewShow = (row: any) => {
  dialogTreeisible.value = true;

}



//行清理数据
const rowClose = (row: any) => {
  alert("清理")
  row.date = '';
}
const dataTree = ref<Array<string>>();

//行处理获取结构数
const getTree = () => {
  ByResourceApi.getResTree2(undefined).then((rel) => {
    dataTree.value = eval("(" + rel + ")")
  })
}


const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
]
// 数据初始化
onMounted(() => {
  //获取树结构
  getTree();
})

</script>
