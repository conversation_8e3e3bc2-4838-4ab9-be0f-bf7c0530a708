<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <el-form-item label="父节点" prop="parentID">
        <el-tree-select
          v-model="formData.parentID"
          :data="aGroupTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          placeholder="请选择父节点"
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="全称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入全称"/>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="简称" prop="shortName">
            <el-input v-model="formData.shortName" placeholder="请输入简称"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="编号" prop="code">
            <el-input v-model="formData.code" placeholder="请输入编号"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型">
              <el-option label="组织" value="Organization"/>
              <el-option label="部门" value="Department"/>
              <el-option label="科室" value="SubDepartment"/>
              <el-option label="管理部门" value="ManagerDept"/>
              <el-option label="生产部门" value="ProductionDept"/>
            </el-select>
          </el-form-item>
        </el-col>


      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="排序号" prop="sortIndex">
            <el-input v-model="formData.sortIndex" placeholder="请输入排序" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在地" prop="location">
            <el-input v-model="formData.location" placeholder="请输入所在地" />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <el-form-item label="颜色" prop="color">
            <el-input v-model="formData.color" placeholder="请输入颜色" />
          </el-form-item>
        </el-col>
      </el-row>


      <el-form-item label="描述"   prop="description">
        <el-input v-model="formData.description" type="textarea" rows=4 placeholder="请输入描述"/>
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AGroupApi, AGroupVO } from '@/api/system/group'
import { defaultProps, handleTree } from '@/utils/tree'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";

/** 系统角色管理 表单 */
defineOptions({ name: 'AGroupForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  name: undefined,
  shortName: undefined,
  code: undefined,
  parentID: '',
  fullID: undefined,
  groupType: undefined,
  type: undefined,
  sortIndex: undefined,
  isDeleted: undefined,
  deleteTime: undefined,
  orgLevel: undefined,
  description: undefined,
  location: undefined,
  systemCode: undefined,
  outKey: undefined,
  connName: undefined,
  userSQL: undefined,
  fullName: undefined,
  orgRole: undefined,
  categoryID: undefined,
  categoryName: undefined,
  useCategory: undefined,
  propCategory: undefined,
  isleader: undefined,
  creditCode: undefined,
  deptUnit: undefined,
  deptUnitName: undefined,
  tlevel: undefined,
  createTime: undefined,
  modifyTime: undefined,
  jsyh: undefined,
  jSYHName: undefined,
  platformTag: undefined,
  categoryIDName: undefined,
  isEditable: undefined,
  sfkfrysy: undefined,
  glYwName: undefined,
  glYwId: undefined,
  userCountInRole: undefined,
  menuCountInRole: undefined,
  clJsyhname: undefined,
  color: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '编号不能为空', trigger: 'blur' }],
  parentID: [{ required: true, message: '父节点不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const aGroupTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AGroupApi.getAGroup(id)
    } finally {
      formLoading.value = false
    }
  }
  await getAGroupTree()
}

const parentIdSet2 = (id :string) => {
  console.log(12321,id);
  formData.value.parentID = id
};
defineExpose({ open, parentIdSet2 }) // 提供 open 方法，用于打开弹窗


/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AGroupVO
    if (formType.value === 'create') {
      data.groupType='Org'
      await AGroupApi.createAGroup(data)
      message.success(t('common.createSuccess'))
    } else {
      await AGroupApi.updateAGroup(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: undefined,
    shortName: undefined,
    code: undefined,
    parentID: undefined,
    fullID: undefined,
    groupType: undefined,
    type: undefined,
    sortIndex: undefined,
    isDeleted: undefined,
    deleteTime: undefined,
    orgLevel: undefined,
    description: undefined,
    location: undefined,
    systemCode: undefined,
    outKey: undefined,
    connName: undefined,
    userSQL: undefined,
    fullName: undefined,
    orgRole: undefined,
    categoryID: undefined,
    categoryName: undefined,
    useCategory: undefined,
    propCategory: undefined,
    isleader: undefined,
    creditCode: undefined,
    deptUnit: undefined,
    deptUnitName: undefined,
    tlevel: undefined,
    createTime: undefined,
    modifyTime: undefined,
    jsyh: undefined,
    jSYHName: undefined,
    platformTag: undefined,
    categoryIDName: undefined,
    isEditable: undefined,
    sfkfrysy: undefined,
    glYwName: undefined,
    glYwId: undefined,
    userCountInRole: undefined,
    menuCountInRole: undefined,
    clJsyhname: undefined,
    color: undefined,
  }
  formRef.value?.resetFields()
}

const orgParams=reactive({
  groupType: 'Org',
})
/** 获得系统角色管理树 */
const getAGroupTree = async () => {
  aGroupTree.value = []
  const data = await AGroupApi.getAGroupList(orgParams)
  const root: Tree = { id: 0, name: '顶级系统角色管理', children: [] }
  root.children = handleTree(data, 'id', 'parentID')
  aGroupTree.value.push(root)
}
</script>
