<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="父ID" prop="parentID">
        <el-tree-select
          v-model="formData.parentID"
          :data="aResTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          placeholder="请选择父ID"
        />
      </el-form-item>
      <el-form-item label="全ID" prop="fullID">
        <el-input v-model="formData.fullID" placeholder="请输入全ID" />
      </el-form-item>
      <el-form-item label="排序索引" prop="sortIndex">
        <el-input v-model="formData.sortIndex" placeholder="请输入排序索引" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="图标" prop="iconCls">
        <el-input v-model="formData.iconCls" placeholder="请输入图标" />
      </el-form-item>
      <el-form-item label="Url" prop="url">
        <el-input v-model="formData.url" placeholder="请输入Url" />
      </el-form-item>
      <el-form-item label="控制类型" prop="ctrlType">
        <el-select v-model="formData.ctrlType" placeholder="请选择控制类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="权限，可以为页面控件ID，数据的查询条件" prop="auth">
        <el-input v-model="formData.auth" placeholder="请输入权限，可以为页面控件ID，数据的查询条件" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="系统编码" prop="systemCode">
        <el-input v-model="formData.systemCode" placeholder="请输入系统编码" />
      </el-form-item>
      <el-form-item label="创建人" prop="createUser">
        <el-input v-model="formData.createUser" placeholder="请输入创建人" />
      </el-form-item>
      <el-form-item label="创建人id" prop="createUserID">
        <el-input v-model="formData.createUserID" placeholder="请输入创建人id" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="formData.createTime"
          type="date"
          value-format="x"
          placeholder="选择创建时间"
        />
      </el-form-item>
      <el-form-item label="修改人" prop="modifyUser">
        <el-input v-model="formData.modifyUser" placeholder="请输入修改人" />
      </el-form-item>
      <el-form-item label="修改人id" prop="modifyUserID">
        <el-input v-model="formData.modifyUserID" placeholder="请输入修改人id" />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifyTime">
        <el-date-picker
          v-model="formData.modifyTime"
          type="date"
          value-format="x"
          placeholder="选择修改时间"
        />
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-radio-group v-model="formData.publishStatus">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上次发布状态" prop="prePublishStatus">
        <el-radio-group v-model="formData.prePublishStatus">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="逻辑删除" prop="isDeleted">
        <el-select v-model="formData.isDeleted" placeholder="请选择逻辑删除">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入口的执行类型（弹出页面，再右侧添加子tab，执行一个js函数）" prop="execType">
        <el-select v-model="formData.execType" placeholder="请选择入口的执行类型（弹出页面，再右侧添加子tab，执行一个js函数）">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="js脚本内容（预留）" prop="scriptContent">
        <Editor v-model="formData.scriptContent" height="150px" />
      </el-form-item>
      <el-form-item label="关联配置项的ID" prop="releateConfigID">
        <el-input v-model="formData.releateConfigID" placeholder="请输入关联配置项的ID" />
      </el-form-item>
      <el-form-item label="第二次打开时是否刷新页面" prop="isRefreshPage">
        <el-input v-model="formData.isRefreshPage" placeholder="请输入第二次打开时是否刷新页面" />
      </el-form-item>
      <el-form-item label="首页的图标样式" prop="bootstrapCls">
        <el-input v-model="formData.bootstrapCls" placeholder="请输入首页的图标样式" />
      </el-form-item>
      <el-form-item label="需求部门" prop="requireDept">
        <el-input v-model="formData.requireDept" placeholder="请输入需求部门" />
      </el-form-item>
      <el-form-item label="使用范围" prop="useScope">
        <el-input v-model="formData.useScope" placeholder="请输入使用范围" />
      </el-form-item>
      <el-form-item label="关键词" prop="keyWord">
        <el-input v-model="formData.keyWord" placeholder="请输入关键词" />
      </el-form-item>
      <el-form-item label="特殊用户" prop="specialUser">
        <el-input v-model="formData.specialUser" placeholder="请输入特殊用户" />
      </el-form-item>
      <el-form-item label="是否是子系统" prop="isSubsystem">
        <el-input v-model="formData.isSubsystem" placeholder="请输入是否是子系统" />
      </el-form-item>
      <el-form-item label="业务目录" prop="businessCategory">
        <el-input v-model="formData.businessCategory" placeholder="请输入业务目录" />
      </el-form-item>
      <el-form-item label="文件" prop="files">
        <el-input v-model="formData.files" placeholder="请输入文件" />
      </el-form-item>
      <el-form-item label="Isenterprise" prop="isenterprise">
        <el-input v-model="formData.isenterprise" placeholder="请输入Isenterprise" />
      </el-form-item>
      <el-form-item label="Abbreviation" prop="abbreviation">
        <el-input v-model="formData.abbreviation" placeholder="请输入Abbreviation" />
      </el-form-item>
      <el-form-item label="Servicereamrk" prop="servicereamrk">
        <el-input v-model="formData.servicereamrk" placeholder="请输入Servicereamrk" />
      </el-form-item>
      <el-form-item label="关联制度" prop="institutions">
        <el-input v-model="formData.institutions" placeholder="请输入关联制度" />
      </el-form-item>
      <el-form-item label="关联业务" prop="business">
        <el-input v-model="formData.business" placeholder="请输入关联业务" />
      </el-form-item>
      <el-form-item label="服务指南" prop="serviceDirectory">
        <el-input v-model="formData.serviceDirectory" placeholder="请输入服务指南" />
      </el-form-item>
      <el-form-item label="输入数据" prop="inRes">
        <el-input v-model="formData.inRes" placeholder="请输入输入数据" />
      </el-form-item>
      <el-form-item label="输入数据" prop="inResName">
        <el-input v-model="formData.inResName" placeholder="请输入输入数据" />
      </el-form-item>
      <el-form-item label="输出数据" prop="outRes">
        <el-input v-model="formData.outRes" placeholder="请输入输出数据" />
      </el-form-item>
      <el-form-item label="输出数据" prop="outResName">
        <el-input v-model="formData.outResName" placeholder="请输入输出数据" />
      </el-form-item>
      <el-form-item label="门户显示" prop="isProtal">
        <el-input v-model="formData.isProtal" placeholder="请输入门户显示" />
      </el-form-item>
      <el-form-item label="相关文件" prop="otherFile">
        <UploadFile v-model="formData.otherFile" />
      </el-form-item>
      <el-form-item label="菜单图片" prop="menuImage">
        <UploadImg v-model="formData.menuImage" />
      </el-form-item>
      <el-form-item label="是否是标题" prop="isTitle">
        <el-input v-model="formData.isTitle" placeholder="请输入是否是标题" />
      </el-form-item>
      <el-form-item label="sqrole" prop="sqrole">
        <el-input v-model="formData.sqrole" placeholder="请输入sqrole" />
      </el-form-item>
      <el-form-item label="sqorg" prop="sqorg">
        <el-input v-model="formData.sqorg" placeholder="请输入sqorg" />
      </el-form-item>
      <el-form-item label="squser" prop="squser">
        <el-input v-model="formData.squser" placeholder="请输入squser" />
      </el-form-item>
      <el-form-item label="sqroleId" prop="sqroleId">
        <el-input v-model="formData.sqroleId" placeholder="请输入sqroleId" />
      </el-form-item>
      <el-form-item label="sqorgId" prop="sqorgId">
        <el-input v-model="formData.sqorgId" placeholder="请输入sqorgId" />
      </el-form-item>
      <el-form-item label="squserId" prop="squserId">
        <el-input v-model="formData.squserId" placeholder="请输入squserId" />
      </el-form-item>
      <el-form-item label="KeyWordId" prop="keyWordId">
        <el-input v-model="formData.keyWordId" placeholder="请输入KeyWordId" />
      </el-form-item>
      <el-form-item label="glywId" prop="glywId">
        <el-input v-model="formData.glywId" placeholder="请输入glywId" />
      </el-form-item>
      <el-form-item label="glyw" prop="glyw">
        <el-input v-model="formData.glyw" placeholder="请输入glyw" />
      </el-form-item>
      <el-form-item label="glzdId" prop="glzdId">
        <el-input v-model="formData.glzdId" placeholder="请输入glzdId" />
      </el-form-item>
      <el-form-item label="glzd" prop="glzd">
        <el-input v-model="formData.glzd" placeholder="请输入glzd" />
      </el-form-item>
      <el-form-item label="SqRoleCount" prop="sqRoleCount">
        <el-input v-model="formData.sqRoleCount" placeholder="请输入SqRoleCount" />
      </el-form-item>
      <el-form-item label="SqUserCount" prop="sqUserCount">
        <el-input v-model="formData.sqUserCount" placeholder="请输入SqUserCount" />
      </el-form-item>
      <el-form-item label="SxYewu" prop="sxYewu">
        <el-input v-model="formData.sxYewu" placeholder="请输入SxYewu" />
      </el-form-item>
      <el-form-item label="SxYewuName" prop="sxYewuName">
        <el-input v-model="formData.sxYewuName" placeholder="请输入SxYewuName" />
      </el-form-item>
      <el-form-item label="XxYewu" prop="xxYewu">
        <el-input v-model="formData.xxYewu" placeholder="请输入XxYewu" />
      </el-form-item>
      <el-form-item label="XxYewuName" prop="xxYewuName">
        <el-input v-model="formData.xxYewuName" placeholder="请输入XxYewuName" />
      </el-form-item>
      <el-form-item label="dgRelatedFiles" prop="dgRelatedFiles">
        <el-input v-model="formData.dgRelatedFiles" placeholder="请输入dgRelatedFiles" />
      </el-form-item>
      <el-form-item label="AuthUserID" prop="authUserID">
        <el-input v-model="formData.authUserID" placeholder="请输入AuthUserID" />
      </el-form-item>
      <el-form-item label="AuthUserName" prop="authUserName">
        <el-input v-model="formData.authUserName" placeholder="请输入AuthUserName" />
      </el-form-item>
      <el-form-item label="AuthRoleID" prop="authRoleID">
        <el-input v-model="formData.authRoleID" placeholder="请输入AuthRoleID" />
      </el-form-item>
      <el-form-item label="AuthRoleName" prop="authRoleName">
        <el-input v-model="formData.authRoleName" placeholder="请输入AuthRoleName" />
      </el-form-item>
      <el-form-item label="DataAuth" prop="dataAuth">
        <el-input v-model="formData.dataAuth" placeholder="请输入DataAuth" />
      </el-form-item>
      <el-form-item label="CtrAuth" prop="ctrAuth">
        <el-input v-model="formData.ctrAuth" placeholder="请输入CtrAuth" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AResApi, AResVO } from '@/api/system/ares'
import { defaultProps, handleTree } from '@/utils/tree'

/** 资源管理 表单 */
defineOptions({ name: 'AResForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  parentID: undefined,
  fullID: undefined,
  sortIndex: undefined,
  name: undefined,
  code: undefined,
  type: undefined,
  iconCls: undefined,
  url: undefined,
  ctrlType: undefined,
  auth: undefined,
  description: undefined,
  systemCode: undefined,
  createUser: undefined,
  createUserID: undefined,
  createTime: undefined,
  modifyUser: undefined,
  modifyUserID: undefined,
  modifyTime: undefined,
  publishStatus: undefined,
  prePublishStatus: undefined,
  isDeleted: undefined,
  execType: undefined,
  scriptContent: undefined,
  releateConfigID: undefined,
  isRefreshPage: undefined,
  bootstrapCls: undefined,
  requireDept: undefined,
  useScope: undefined,
  keyWord: undefined,
  specialUser: undefined,
  isSubsystem: undefined,
  businessCategory: undefined,
  files: undefined,
  isenterprise: undefined,
  abbreviation: undefined,
  servicereamrk: undefined,
  institutions: undefined,
  business: undefined,
  serviceDirectory: undefined,
  inRes: undefined,
  inResName: undefined,
  outRes: undefined,
  outResName: undefined,
  isProtal: undefined,
  otherFile: undefined,
  menuImage: undefined,
  isTitle: undefined,
  sqrole: undefined,
  sqorg: undefined,
  squser: undefined,
  sqroleId: undefined,
  sqorgId: undefined,
  squserId: undefined,
  keyWordId: undefined,
  glywId: undefined,
  glyw: undefined,
  glzdId: undefined,
  glzd: undefined,
  sqRoleCount: undefined,
  sqUserCount: undefined,
  sxYewu: undefined,
  sxYewuName: undefined,
  xxYewu: undefined,
  xxYewuName: undefined,
  dgRelatedFiles: undefined,
  authUserID: undefined,
  authUserName: undefined,
  authRoleID: undefined,
  authRoleName: undefined,
  dataAuth: undefined,
  ctrAuth: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const aResTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AResApi.getARes(id)
    } finally {
      formLoading.value = false
    }
  }
  await getAResTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AResVO
    if (formType.value === 'create') {
      await AResApi.createARes(data)
      message.success(t('common.createSuccess'))
    } else {
      await AResApi.updateARes(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    parentID: undefined,
    fullID: undefined,
    sortIndex: undefined,
    name: undefined,
    code: undefined,
    type: undefined,
    iconCls: undefined,
    url: undefined,
    ctrlType: undefined,
    auth: undefined,
    description: undefined,
    systemCode: undefined,
    createUser: undefined,
    createUserID: undefined,
    createTime: undefined,
    modifyUser: undefined,
    modifyUserID: undefined,
    modifyTime: undefined,
    publishStatus: undefined,
    prePublishStatus: undefined,
    isDeleted: undefined,
    execType: undefined,
    scriptContent: undefined,
    releateConfigID: undefined,
    isRefreshPage: undefined,
    bootstrapCls: undefined,
    requireDept: undefined,
    useScope: undefined,
    keyWord: undefined,
    specialUser: undefined,
    isSubsystem: undefined,
    businessCategory: undefined,
    files: undefined,
    isenterprise: undefined,
    abbreviation: undefined,
    servicereamrk: undefined,
    institutions: undefined,
    business: undefined,
    serviceDirectory: undefined,
    inRes: undefined,
    inResName: undefined,
    outRes: undefined,
    outResName: undefined,
    isProtal: undefined,
    otherFile: undefined,
    menuImage: undefined,
    isTitle: undefined,
    sqrole: undefined,
    sqorg: undefined,
    squser: undefined,
    sqroleId: undefined,
    sqorgId: undefined,
    squserId: undefined,
    keyWordId: undefined,
    glywId: undefined,
    glyw: undefined,
    glzdId: undefined,
    glzd: undefined,
    sqRoleCount: undefined,
    sqUserCount: undefined,
    sxYewu: undefined,
    sxYewuName: undefined,
    xxYewu: undefined,
    xxYewuName: undefined,
    dgRelatedFiles: undefined,
    authUserID: undefined,
    authUserName: undefined,
    authRoleID: undefined,
    authRoleName: undefined,
    dataAuth: undefined,
    ctrAuth: undefined,
  }
  formRef.value?.resetFields()
}

/** 获得资源管理树 */
const getAResTree = async () => {
  aResTree.value = []
  const data = await AResApi.getAResList()
  const root: Tree = { id: 0, name: '顶级资源管理', children: [] }
  root.children = handleTree(data, 'id', 'parentID')
  aResTree.value.push(root)
}
</script>