<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-select
              v-model="nowTitle"
              placeholder="请选择"
              style="width: 160px"
              clearable
              @change="msgChange"
            >
              <el-option
                v-for="item in titleList"
                :key="item.type"
                :label="item.title"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'MsgForm'
})
const props = defineProps<{ itemJson: any }>()

const titleList = ref([
  {
    type: 'msg',
    title: '我的消息',
    isShowNum: false
  },
  {
    type: 'myfile',
    title: '我的文件',
    isShowNum: false
  }
])
const nowTitle = ref('')
const form = ref({
  titleList: {
    type: 'msg',
    title: '我的消息',
    isShowNum: false
  },
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
  isShow: {}
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('msg', form.value)
  },
  { immediate: true, deep: true }
)

const msgChange = () => {
  const title = titleList.value.find((item) => item.type == nowTitle.value)

  if (title) {
    form.value.titleList = title
  }
}
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  // setValue()
})
// const setValue = () => {
//   value.value = form.value.titleList.map((item) => item.id)
// }
</script>
