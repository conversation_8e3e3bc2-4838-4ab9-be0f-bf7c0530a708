﻿//替换掉Url中的{}参数

import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'

export const replaceUrl = function(settings, pty) {
    const userStore = useUserStore()
    // 测试数据
    const _UserInfo = {
        "ID": userStore.user.id,
        "Name": userStore.user.nickname,
        "DeptID": userStore.user.deptId,
        "DeptName": "",
        "DeptFullID": "",
        "WorkNo": userStore.user.workNo,
        "Duties": "",
        "ParttimeDeptID": "",
        "ParttimeDeptName": "",
        "Office": "",
        "OfficeId": "",
        "OfficeName": "",
        "LastLoginIP": "",
        "MobilePhone": "",
        "Token": getAccessToken()
    }
    if (!pty)
        pty = "url";
    if (!settings[pty])
        return;
    var str = settings[pty]; 
    var guid = "";
    var result = str.replace(/\{[0-9a-zA-Z_]*\}/g, function (e) {
        var key = e.substring(1, e.length - 1);
        
        if (key == "GUID") {
            if (!guid) {
                $.ajax({
                    url: "GetGuid",
                    type: "post",
                    async: false,
                    success: function (text, textStatus) {
                        guid = text;
                    }
                });
            }
            return guid;
        }
        switch (key) {
            case "UserID":
                if (_UserInfo)
                    return _UserInfo.ID;
                break;
            case "UserName":
                if (_UserInfo)
                    return _UserInfo.Name;
                break;
            case "UserDeptID":
                if (_UserInfo)
                    return _UserInfo.DeptID;
                break;
            case "UserDeptFullID":
                if (_UserInfo)
                    return _UserInfo.DeptFullID;
                break;
            case "UserOfficeID":
                if (_UserInfo)
                    return _UserInfo.OfficeID;
                break;
            case "UserOfficeName":
                if (_UserInfo)
                    return _UserInfo.OfficeName;
                break;
            case "WorkNo":
                if (_UserInfo)
                    return _UserInfo.WorkNo;
                break;
            case "UserDeptName":
                if (_UserInfo)
                    return _UserInfo.DeptName;
                break;
            case "CurrentTime":
                if (_DateInfo)
                    return _DateInfo.CurrentTime;
                break;
            case "CurrentDate":
                if (_DateInfo)
                    return _DateInfo.CurrentDate;
                break;
            case "Year":
                if (_DateInfo)
                    return _DateInfo.Year;
                break;
            case "YearMonth":
                if (_DateInfo)
                    return _DateInfo.YearMonth;
                break;
            case "Month":
                if (_DateInfo)
                    return _DateInfo.Month;
                break;
            case "Day":
                if (_DateInfo)
                    return _DateInfo.Day;
                break;
            case "Token":
                if (_UserInfo)
                    return _UserInfo.Token;
                break;
            default:
                break;
        }
        
        if (settings.currentRow && settings.currentRow[key])//从当前行返回
            return settings.currentRow[key];
        if (settings.paramFrom) { //从指定控件返回
            var rtnValue = "";
            var ctrl = mini.get(settings.paramFrom);
            if (ctrl == undefined) {
                msgUI("Id为" + settings.paramFrom + "的控件不存在！", 3);
                return;
            }
            else if (ctrl.showCheckBox) {
                rtnValue= getValues(ctrl.getCheckedNodes(), key);
            }
            else if (ctrl.getSelecteds)
                rtnValue= getValues(ctrl.getSelecteds(), key);
            else if (ctrl.getValue)
                rtnValue = ctrl.getValue();

            if (rtnValue != "" && rtnValue != undefined)
                return rtnValue;
        }
        else if (getObj(key)) {
            var rtnValue = "";
            var ctrl = getObj(key);
            if (ctrl.getValue)
                rtnValue= ctrl.getValue();
            if (rtnValue != "" && rtnValue != undefined)
                return rtnValue;
        }

        if (hasQueryString(key)) //从地址栏返回
            return getQueryString(key);

        return "";
        //return e;
    });

    settings[pty] = result;
    return result;
}