import { defineStore } from 'pinia'
import { store } from '../index'
import { getEnumDefList } from '@/api/system/enum'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache('sessionStorage')

export const useEnumStore = defineStore('enum', {
  state: () => ({
    enums: [],
    enumMap: new Map(), // Add a Map to store the transformed data
    initialized: false,
  }),
  getters: {
    getEnums: (state) => state.enums,
    getEnumMap: (state) => state.enumMap, // Getter for the Map
    isInitialized: (state) => state.initialized,
  },
  actions: {
    // Helper function to convert array to Map
    convertArrayToMap(enumsArray) {
      const map = new Map();
      if (enumsArray && Array.isArray(enumsArray)) {
        enumsArray.forEach(enumItem => {
          // Assuming each enumItem has an 'id' or similar unique identifier
          // Adjust the key as needed based on your data structure
          map.set(enumItem.code, enumItem);
        });
      }
      return map;
    },

    async initialize() {
      try {
        const res = await getEnumDefList();
        const enumsData = res.data ? res.data : res;
        this.enums = enumsData;
        this.enumMap = await this.convertArrayToMap(enumsData); // Convert new data to Map
        this.initialized = true;
        wsCache.set(CACHE_KEY.enums, this.enums, { exp: 60 });
      } catch (error) {
        // Handle error if needed
        throw error;
      }
    },
  },
});

// 导出同步的 useEnum 函数
export function useEnum() {
  return useEnumStore(store);
}
