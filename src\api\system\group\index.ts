import request from '@/config/axios'

export  interface GroupResReqVO {
  groupId: string;
  resIds: string[];
}
// 系统角色管理 VO
export interface AGroupVO {
  id: string;
  name: string // 名称
  shortName: string // 简称
  code: string // 编号
  parentID: string // 父节点ID
  fullID: string // 节点全路径
  groupType: string // 分组类型（组织或角色）
  type: string // 类型
  sortIndex: number // 排序
  isDeleted: string // 删除状态
  deleteTime: Date // 删除日期
  orgLevel: number // 组织等级
  description: string // 描述
  location: string // 所在地
  systemCode: string // 子系统编号
  outKey: string // 外部数据Key
  connName: string // 数据库
  userSQL: string // 查询SQL
  fullName: string // 部门全路径名称
  orgRole: string // 组织角色
  categoryID: string // 所属分类
  categoryName: string // 所属分类名称
  useCategory: string // 使用分类
  propCategory: string // 属性分类
  isleader: number // 是否是领导
  creditCode: string // creditCode
  deptUnit: string // DeptUnit
  deptUnitName: string // DeptUnitName
  tlevel: number // Tlevel
  createTime: Date // 创建时间
  modifyTime: Date // 修改时间
  jsyh: string // 角色用户
  jSYHName: string // 角色用户名称
  platformTag: string // 平台标识
  categoryIDName: string // 所属分类名称
  isEditable: string // 是否开发人员维护
  sfkfrysy: string // 是否开发人员使用
  glYwName: string // GlYwName
  glYwId: string // GlYwId
  userCountInRole: number // UserCountInRole
  menuCountInRole: number // MenuCountInRole
  clJsyhname: string // Cl_JSYHName
  color: string // 颜色
}

// 系统角色管理 API
export const AGroupApi = {
  // 保存系统角色管理
  saveGroupRes: async (data: GroupResReqVO) => {
    return await request.post({ url: `/system/A-group/saveGroupRes`, data })
  },

  // 查询系统角色管理详情
  getResIdsByGroupId: async (id: number) => {
    return await request.get({ url: `/system/A-group/getResIdsByGroupId?id=` + id })
  },
  // 查询组织管理分页
  getAGroupPage: async (params: any) => {
    return await request.get({ url: `/system/A-group/page`, params })
  },
  // 查询系统角色管理列表
  getAGroupList: async (params) => {
    return await request.get({ url: `/system/A-group/list`, params })
  },

  // 查询系统角色管理详情
  getAGroup: async (id: number) => {
    return await request.get({ url: `/system/A-group/get?id=` + id })
  },

  // 新增系统角色管理
  createAGroup: async (data: AGroupVO) => {
    return await request.post({ url: `/system/A-group/create`, data })
  },

  // 修改系统角色管理
  updateAGroup: async (data: AGroupVO) => {
    return await request.put({ url: `/system/A-group/update`, data })
  },

  // 删除系统角色管理
  deleteAGroup: async (id: number) => {
    return await request.delete({ url: `/system/A-group/delete?id=` + id })
  },

  // 导出系统角色管理 Excel
  exportAGroup: async (params) => {
    return await request.download({ url: `/system/A-group/export-excel`, params })
  },
}
