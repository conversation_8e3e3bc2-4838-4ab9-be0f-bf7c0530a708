<template>
  <div class="portal_page">
    <div class="uk-card uk-card-head">
      <span class="current-location-title">当前位置：</span>
      <el-breadcrumb separator-icon="ArrowRight" style="display: inline-block;">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item>办公中心</el-breadcrumb-item>
        <el-breadcrumb-item>列表</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="uk-card uk-card-body">
      <div v-for="item in warmPromptList" :key="item.ID">
        <warm-card :warmItem="item" />
      </div>
      <el-empty description="暂时没有温馨提示信息" v-if="warmPromptList == undefined" />
      <div class="emptybox" v-if="show_loading">
        <div class="line-scale loading_icon">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>

      <div class="pager" v-if="!show_loading">
        <el-pagination v-model:current-page="form_data.page" v-model:page-size="form_data.pageSize" :total="total"
          style="text-align: center;margin-top: 20px;margin-bottom: 20px;" layout="jumper, prev, pager, next, total"
          @current-change="currentChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import WarmCard from './WarmCard.vue';
import * as PortalApi from '@/api/system/portal'
interface warmPrompt {
  RID: string;
  ID: string;
  Code: string;
  Type: string;
  Title: string;
  OrderIndex: string;
  PhotoAddress: string;
  Content: string;
  StartTime: string;
  EndTime: string;
  CreateTime: string;
}
const warmPromptList = ref<Array<warmPrompt>>()
const total = ref(50)
const show_loading = ref(false)
//列表查询参数
let form_data = ref({
  page: 1,
  pageSize: 50,
});



// 获取温馨提示列表
const getWarmPromptList = async () => {
  show_loading.value = true;
  try {
    const ret = await PortalApi.GetWarmPromptList(form_data.value);
    total.value = ret.total
    warmPromptList.value = ret.records
    console.log(warmPromptList.value)
  } finally {
    show_loading.value = false;
  }

}

function currentChange() {
  getWarmPromptList()
}

onMounted(() => {
  getWarmPromptList()
})


</script>

<style lang="scss" scoped>
.portal_page {
  height: 100%;
  width: 1252px;
  margin: auto;
}

.uk-card {
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
}

.uk-card-head {
  height: 55px;
  margin-top: 6px;

  span {
    margin-left: 20px;
    color: #60627a;
  }
}

.uk-card-body {
  margin-top: 10px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.loading_icon {
  margin-top: 240px;

  div {
    background-color: #888;
  }
}

.uk-card-body::-webkit-scrollbar {
  width: 8px;
}

.uk-card-body::-webkit-scrollbar-track {
  background: #e5e5e5;
}

.uk-card-body::-webkit-scrollbar-thumb {
  background: #cecece;
}
</style>
