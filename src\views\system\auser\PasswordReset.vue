<template>
  <Dialog  :title="dialogTitle" v-model="dialogVisible" width="600px" :close-on-click-modal="false">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="密码" prop="name">
        <el-input v-model="formData.password" type="password" placeholder="请输入密码"/>
      </el-form-item>
      <el-form-item label="确认密码" prop="repassword">
        <el-input v-model="formData.repassword" type="password"  placeholder="请再次输入密码"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {AUserApi, UserResetPwd} from '@/api/system/auser'


const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  userId: '',
  password: '',
  repassword: '',

})
const formRules = reactive({
  password: [{required: true, message: '密码不能为空', trigger: 'change'}],
  repassword: [{required: true, message: '确认密码不能为空', trigger: 'change'}],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (id?: string) => {
  dialogVisible.value = true
  dialogTitle.value = "重置密码"
  resetForm(id)
  console.log(formData);
}
const message = useMessage() // 消息弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  console.log(123456,formData);
  if(formData.value.password!=formData.value.repassword){
    message.alertError("两次输入密码不一致");
    return;
  }
  formData.value.password = encodePassword(formData.value.password)
  formData.value.repassword = encodePassword(formData.value.password)
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserResetPwd

    await AUserApi.resetPassword(data)
    message.success("重置成功")
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
/** 重置表单 */
const resetForm = (id) => {
  formData.value = {
    userId: id,
    password: '',
    repassword: '',
  }
  formRef.value?.resetFields()
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗
// 密码编码
const encodePassword = (value) => {
  return  btoa(unescape(encodeURIComponent(value)));
}
</script>
<style>

.colored-underline {
  text-decoration: underline;
  text-decoration-color: #C50B4E; /* 你可以使用任何颜色 */
}
</style>
