<template>
  <div class="my_work">
    <my-panel height="auto" :show-more="false" style="border-bottom:0;">
      <template #title>
        <div class="header flex f_j_b">
          <div class="left flex">
            {{ get_cate_title }}
          </div>
          <label class="right flex">
            <input class="checkbox" type="checkbox" @change="onTitleToggle"/>
            <span>显示完整标题</span>
          </label>
        </div>
      </template>
      <div class="task" v-loading="loading">
        <div class="list flex f_c">
          <!--我的待办-->
          <template v-if="menu_actived === 'newtask'">
            <div
              class="item flex f_j_b"
              v-for="(item, index) in list"
              :key="index"
              style="cursor: pointer"
              @click="OpenView(2, item)">
              <div class="icon">
                <img
                  title="取消关注"
                  v-if="item.IsFocus == 'true'"
                  @click.stop="FocusFlow(0, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tuding-copy.png"
                />
                <img
                  title="关注任务"
                  v-else-if="item.IsFocus == 'false'"
                  @click.stop="FocusFlow(1, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tuding.png"
                />
              </div>

              <div class="title" @click="openDetails(item.URL)" v-text="item.ActivityName"></div>
              <div class="cate">
                <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
              </div>
              <div class="info">
                <span class="dept" :title="item.FromDeptName" v-if="item.FromDeptName">
                  {{ item.ShortDeptName }}：
                </span>
                <span class="dept" v-else></span>
                <span class="name" v-if="item.FromUserNames">{{ item.FromUserNames }}</span>
                <span class="name" v-else></span>
                <span class="time">{{ item.CreateTime.substring(0, 16) }}</span>
              </div>
            </div>
          </template>
          <!--我的已办-->
          <template v-if="menu_actived === 'completetask'">
            <div
              class="item flex f_j_b"
              v-for="(item, index) in list"
              :key="index"
              style="cursor: pointer"
              @click="OpenView(2, item)">
              <div class="icon">
                <img
                  title="取消关注"
                  v-if="item.IsFocus == 'true'"
                  @click.stop="FocusFlow(0, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tuding-copy.png"/>
                <img
                  title="关注任务"
                  v-else-if="item.IsFocus == 'false'"
                  @click.stop="FocusFlow(1, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tuding.png"/>
              </div>

              <div class="title" @click="openDetails(item.ViewUrl)" v-text="item.TaskName"></div>
              <div class="cate">
                <span v-if="item.GoodWaySoft === 'PC_SRMSFlow'">信息院综合管理系统</span>
                <span v-else-if="item.GoodWaySoft === 'KJXT'">科技系统</span>
                <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
              </div>
              <div class="info">
                <span class="dept" :title="item.CreateDeptName" v-if="item.CreateDeptName">
                  {{ item.CreateUserDeptName }}：
                </span>
                <span class="name">{{ item.CreateUserName }}</span>
                <span class="time">{{ item.ExecTime.substring(0, 16) }}</span>
              </div>
            </div>
          </template>
          <!--我的申请-->
          <template v-if="menu_actived === 'apply'">
            <div class="list" style="width: 100%">
              <div
                class="item todo"
                v-for="(item, index) in list"
                :key="index"
                @click="OpenView(2, item)">
                <div class="icon">
                  <img
                    title="取消关注"
                    v-if="item.IsFocus == true"
                    @click.stop="FocusFlow(0, item, menu_actived)"
                    src="@/assets/icons/portal/icon-tuding-copy.png"
                  />
                  <img
                    title="关注任务"
                    v-else
                    @click.stop="FocusFlow(1, item, menu_actived)"
                    src="@/assets/icons/portal/icon-tuding.png"
                  />
                </div>
                <div class="title ellipsis1" @click="openDetails(item.ViewUrl)" :title="item.Title">
                  {{ item.Title }}
                </div>
                <div class="apply-flow info" style="width: 360px;">
                  <div class="dept">
                    <div class="dept-type">
                      <span>审批状态：</span>
                      <span :title="item.FlowState">{{ item.FlowState }}</span>
                    </div>
                    <div class="dept-name">
                      <span>关注日期：</span>
                      <span v-if="item && item.CreateTime">{{
                          item.CreateTime.split(' ')[0]
                        }}</span>
                    </div>
                  </div>

                  <div class="step">
                    <span>当前审批步骤：</span>
                    <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <!--我的文件-->
          <template v-if="menu_actived === 'myfile'">
            <div
              class="item flex f_j_b"
              v-for="(item, index) in list"
              :key="index"
              style="cursor: pointer"
              @click="OpenView(2, item)">
              <div class="icon">
                <img :src="proxys.$getFullUrl(item.Icon)"/>
              </div>
              <div class="title" :title="item.FileName.substring(item.FileName.indexOf('_') + 1)">
                {{ item.FileName.substring(item.FileName.indexOf('_') + 1) }}
              </div>
              <div class="info">
                <img
                  @click.stop="DownLoadAndPreviewFile(item, 0)"
                  src="@/assets/imgs/icon-download.png"
                  title="下载文件"
                />
                <img
                  @click.stop="CancelSc(item)"
                  style="margin-left: 20px; width: 16px; height: 16px"
                  title="取消收藏"
                  src="@/assets/imgs/sc_a.png"
                />
                <img
                  title="取消关注"
                  v-if="item.IsFocus == 'true'"
                  @click.stop="FocusFlow(0, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tuding-copy.png"
                />
                <img
                  title="关注任务"
                  v-else-if="item.IsFocus == 'false'"
                  @click.stop="FocusFlow(1, item, menu_actived)"
                  src="@/assets/icons/portal/icon-tudingwb.png"
                />
              </div>
            </div>
          </template>
          <!--我的关注-->
          <template v-else-if="menu_actived === 'focus'">
            <div class="list" style="width: 100%">
              <div
                class="item todo"
                v-for="(item, index) in list"
                :key="index"
                @click="OpenView(2, item)">
                <div class="icon">
                  <img @click.stop="FocusFlow(0, item, menu_actived)"
                       src="@/assets/icons/portal/icon-tuding-copy.png"/>
                </div>
                <div class="title ellipsis1" :title="item.FlowName">{{ item.FlowName }}</div>
                <div class="apply-flow info" style="width: 360px;">
                  <div class="dept">
                    <div class="dept-type">
                      <span>审批状态：</span>
                      <span :title="item.FlowState">{{ item.FlowState }}</span>
                    </div>
                    <div class="dept-name">
                      <span>关注日期：</span>
                      <span v-if="item && item.CreateTime">{{
                          item.CreateTime.split(' ')[0]
                        }}</span>
                    </div>
                  </div>

                  <div class="step">
                    <span>当前审批步骤：</span>
                    <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <el-empty v-if="list.length === 0" description="数据为空"/>
        </div>
        <div class="pager">
          <el-pagination
            v-model:current-page="form_data.page"
            v-model:page-size="form_data.pageSize"
            :total="total"
            style="text-align: center; margin-top: 20px"
            layout="jumper, prev, pager, next, total"
            @current-change="currentChange"
          />
        </div>
      </div>
    </my-panel>
  </div>
</template>

<script setup>
import * as portal from '@/api/portal'
import * as PortalApi from '@/api/system/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import {useAppStore} from '@/store/modules/app'
import {getAccessToken} from '@/utils/auth'
import {getCurrentInstance} from 'vue'
import {getIsOpen, messageInfo} from '@/utils/websocket'
import {openTask} from '@/layout/portal/admin'
import {searchStore} from '@/store/modules/search'
import myPanel from '@/components/Panel/src/index.vue'
import {useTaskStore} from '@/store/modules/task'
import { cmaj } from "@/utils/legacy-ajax"
import {Storage, CACHE_KEY} from '@/hooks/web/useCache'

const {wsCache} = Storage()

//下载路径
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
const loading = ref(false)
const proxys = ref()
const list = ref([]) //数据列表
const total = ref(0) //数据总数
const message = useMessage()
const _search = searchStore()
const openDetails = (url) => {
  // window.open(url, '_blank');
}
const taskStore = useTaskStore()
const onTitleToggle = (event) => {
  const isChecked = event.target.checked
  let dyc = document.getElementsByClassName('title')
  if (isChecked) {
    for (let item of dyc) {
      item.style.whiteSpace = 'normal'
    }
  } else {
    for (let item of dyc) {
      item.style.whiteSpace = 'nowrap'
    }
  }
}
const get_cate_title = computed(() => {
  const menu_actived = useAppStore().work_menu
  if (useAppStore().work_menu == '' || useAppStore().work_menu == undefined) {
    return '我的待办'
  }
  switch (menu_actived) {
    case 'newtask':
      _search.setToolbarValue('newtask')
      return '我的待办'
      break

    case 'completetask':
      _search.setToolbarValue('completetask')
      return '我的已办'
      break

    case 'apply':
      _search.setToolbarValue('apply')
      return '我的申请'
      break

    case 'myfile':
      _search.setToolbarValue('myfile')
      return '我的文件'
      break

    case 'focus':
      _search.setToolbarValue('focus')
      return '我的关注'
      break

    default:
      _search.setToolbarValue('other')
      break
  }
})

const searchValue = ref('')
const MyFocusByIDPath = ref('/Portal/WorkCenter/GetMyFocusByID')

//获取store里菜单被选中项目
const menu_actived = computed(() => {
  if (useAppStore().work_menu == '' || useAppStore().work_menu == undefined) {
    //没有状态的时候，默认待办
    useAppStore().work_menu = 'newtask'
    _search.setToolbarValue('newtask')
    return 'newtask'
  }
  return useAppStore().work_menu
})
//我的工作点击处理
//打开任务
const OpenView = (type, item) => {
  var url
  useAppStore().store = true
  switch (type) {
    case 2:
      _search.setToolbarValue(menu_actived.value)
      if (menu_actived.value === 'newtask') {
        openTask(item)
      } else if (menu_actived.value === 'completetask') {
        window.open(item.ViewUrl)
      } else if (menu_actived.value === 'myfile') {
        url = item.URL + '&token=' + getAccessToken()
      } else if (menu_actived.value === 'focus') {
        PortalApi.GetMyFocusByID({ID: item.ID}).then((ret) => {
          if (!ret.newtask) {
            if (item.ExecURL.indexOf('{token}')) {
              url = item.ExecURL.replace('{token}', getAccessToken())
            } else {
              url = item.ExecURL + '&token=' + getAccessToken()
            }
            window.open(baseUrl + url)
          } else {
            openTask(ret.newtask)
          }
        })
      }
      break
  }
  const objdata = {
    funcname: item.Title ? item.Title : item.FlowName,
    funcurl: item.URL ? item.URL : item.ExecURL ? item.ExecURL : item.ViewUrl,
    menuId: item.ID
  }
  const log = LogApi.funcLog(objdata)
  if (!url) {
    return
  }
  url = baseUrl + url
  window.open(url)
}

//列表查询参数
let form_data = ref({
  type: menu_actived,
  activity: null,
  page: 1,
  pageSize: 10,
  key: 0
})

//api获取列表数据
async function get_list() {
  list.value = []
  loading.value = true
  form_data.value.key = form_data.value.key + 1
  let ret
  if (menu_actived.value == 'newtask') {
    await taskStore.initNewTaskList(form_data.value)
    let res = taskStore.getTask
    list.value = res.records
    total.value = res.total != undefined ? res.total : 0
    form_data.value.page = res.current
  } else if (menu_actived.value == 'completetask') {
    portal.getCompleteTasks(form_data.value).then((res) => {
      list.value = res.data.records
      total.value = res.data.total != undefined ? res.data.total : 0
      form_data.value.page = res.data.current
    })
  } else {
    ret = await portal.wdgz_list(form_data.value)
    let res = ret.data
    if (res && ret.key == form_data.value.key) {
      list.value = res.records
      total.value = res.total != undefined ? res.total : 0
      form_data.value.page = res.current
    }
  }

  loading.value = false
}

//监听菜单项改变事项，默认初始化加载一次监听
watch(
  menu_actived,
  (type) => {
    get_list() //查询数据
  },
  {immediate: true}
)

function currentChange() {
  get_list()
}

//监听菜单项改变事项，默认初始化加载一次监听
watch(
  messageInfo,
  (type) => {
    handleSocket(messageInfo.value) //查询数据
  },
  {deep: true}
)
watch(
  () => _search.newTaskSearchValue,
  (newValue) => {
    form_data.value.activity = newValue
    get_list() //查询数据
  }
)

//关注&取消关注
function FocusFlow(type, item, tabName) {
  var Title
  if (tabName == 'newtask') {
    Title = item.ActivityName
  } else if (tabName == 'completetask') {
    Title = item.TaskName
  } else if (tabName == 'apply') {
    Title = item.Title
  } else if (tabName == 'focus') {
    Title = item.FlowName
  }

  var msg = type == 0 ? '是否取消关注【' + Title + '】？' : '是否关注【' + Title + '】？'

  message.confirm(msg, '提示').then(() => {
    PortalApi.execMyFocus({
      type: type,
      tabName: tabName,
      myFlow: JSON.stringify(item)
    }).then((ret) => {
      if (ret) {
        ElMessage({
          message: type == 0 ? '已经取消关注！' : '关注成功，可在【我的关注】列表查看。',
          type: 'success',
          offset: '10'
        })
        get_list()
      } else {
        ElNotification({
          title: 'Success',
          message: 'This is a success message',
          type: 'error'
        })
      }
    })
  })
}

//检测新窗口是否关闭，关闭的时候将其从数组中移除
const checkwin = () => {
  var status = false
  if (taskwin != null && taskwin != undefined && taskwin.length > 0) {
    for (var i = 0; i < taskwin.length; i++) {
      status = taskwin[i].closed
      if (status) {
        taskwin.splice(i, 1) //在数组中移除已关闭的窗口
        break
      }
    }
  }
  if (status) {
    if (taskwin.length == 0) {
      clearInterval(timer)
    }
    get_list()
  }
}

function ErrorMsg(err) {
  UIkit.notification({
    message: err.message,
    status: 'warning',
    pos: 'top-right',
    timeout: 5 * 1000
  })
}

//获取标题
function get_title(item) {
  if (item && item.FileName) {
    return item.FileName.substring(item.FileName.indexOf('_') + 1)
  } else {
    return item.FileName
  }
}

// 取消收藏
function CancelSc(file) {
  var serverroot00 = 'http://***********:8097/' + 'files/cancelscmyfav/'
  var ev = window.event
  if (!ev) {
    return
  }
  var _fileinfo = [file.FileKey, '', '']
  var _lk = _fileinfo[0]
  var _opfalg = _scgetopflag()
  PortalApi.CancelSc()

  // 使用_cmaj函数
  cmaj(
    null,
    'post',
    serverroot00 + 'files/cancelscmyfav/',
    {
      fileKey: encodeURI(_lk),
      userId: wsCache.get(CACHE_KEY.USER).user.id,
      deptId: wsCache.get(CACHE_KEY.USER).user.deptId,
      opflag: JSON.stringify(_opfalg)
    },
    null,
    null,
    function (dat) {
      var _tar = ev.target
      $(_tar).closest('li').remove()
    },
    function (err) {
      console.log(err)
    }
  )
}

function _getquerystring(name, url) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)

  // 如果提供了url参数，则从url中解析
  if (arguments.length > 1 && url && typeof url === 'string') {
    var _urlarr = url.split('?')
    if (_urlarr.length > 1) {
      var _search = _urlarr[1]
      r = _search.match(reg)
    }
  }

  if (r != null) return decodeURI(r[2])
  return null
}

function _scgetopflag() {
  var rtn = {}
  var _skey = _getquerystring('sKey')
  var _stepkey = _getquerystring('StepKey')
  var _id = _getquerystring('Id') || _getquerystring('ID') || _getquerystring('id')
  var _templetcode = _getquerystring('TempletCode')
  if (_skey) {
    rtn['sKey'] = _skey
  }
  if (_stepkey) {
    rtn['StepKey'] = _stepkey
  }
  if (_id) {
    rtn['Id'] = _id
  }
  if (_templetcode) {
    rtn['TempletCode'] = _templetcode
  }
  return rtn
}

const handleSocket = (obj) => {
  if ('task' == obj.type) {
    get_list() //更新任务
  }
}

//下载文件 预览文件
async function DownLoadAndPreviewFile(file, type) {
  var url = file.FileKey.substring(file.FileKey.indexOf('|'), file.FileKey.length)
  var _pre = ''
  var _correctfilekey = function (filekey) {
    if (filekey && filekey.indexOf('|') != -1) {
      return filekey.split('|')[1]
    }
    return filekey
  }
  if (url.indexOf('http:') == 0 && url.indexOf('|') > 0) {
    _pre = url.split('|')[1]
    url = url.split('|')[0]
  } else {
    if (url.indexOf('|') == 0) {
      url = url.substring(1, url.length)
    }
  }
  switch (file.Cgroup) {
    case 'kd':
      //获取文件token
      const result = await PortalApi.GetFileToken({fileId: _correctfilekey(file.FileKey)})
      if (result) {
        if (url.indexOf('http:') < 0) {
          url = ''
        }
        if (type === 0) {
          window.open(downLoadUrl + '?FileToken=' + result)
        } else if (type === 1) {
          window.open(url + '/UIBuilder/UIViewer/PreviewPage?FileToken=' + result)
        }
      } else {
        alert('该文件验证失败，暂不能下载，请联系管理员！')
      }
      break

    case 'mini':
      window.open('http://*********:8080/FileStore/Download.aspx?FileId=' + encodeURI(url))
      break

    default:
      if (url) {
        var _alias = file.AliasName
        if (_alias) {
          switch (_alias) {
            case 'attachmentold':
              downLoadFile(url, 2)
              break
            case 'attachmentnew':
              downLoadFile(url, 1)
              break
          }
        } else {
          if (url.indexOf('javascript:') == 0) {
            // |javascript:POBrowser.openWindowModeless('http://***********:8001/PageOffice/ViewPDF.aspx?zxw=1&fileID=262376_员工职业资格取证及证书管理规定（2022年版）.pdf')
            if (url.indexOf('fileID') != -1) {
              let idurl = url.substring(url.indexOf('fileID'))
              if (idurl.indexOf('&') != -1) {
                idurl = idurl.substring(idurl.indexOf('&'))
              } else {
                idurl = idurl.replace("')", '').replace('fileID=', '')
              }
              downLoadFile(idurl, 1)
            }
          } else {
            if (type === 0) {
              if (url.indexOf('/BasicApplication/KindEditor/DownloadFile?path') != -1) {
                url = url.substring(url.indexOf('/BasicApplication'))
                url = baseUrl + url + '&token=' + getAccessToken()
              }
              window.open(url)
            } else if (type === 1) {
              window.open(url)
            } else {
              alert('该文件无法预览，请下载查看！')
            }
          }
        }
      }
      break
  }
}

//下载附件
const downLoadFile = async (fileName, type) => {
  if (parseInt(type) === 1) {
    // var newWindow = window.open()
    //获取文件token
    const result = await PortalApi.GetFileToken({fileId: fileName})
    if (result) {
      // FileApi.downloadFile(result)
      window.open(downLoadUrl + '?FileToken=' + result, "'_self'")
    } else {
      alert('该文件验证失败，暂不能下载，请联系管理员！')
    }
  } else {
    //老的中心网下载
    window.location.href = 'http://**********/Portal/Home/download?name=' + escape(fileName)
  }
}
//搜索任务
const searchMyWork = () => {
  get_list()
}

onMounted(() => {
  const {proxy} = getCurrentInstance()
  proxys.value = proxy
})
onUnmounted(() => {
  _search.setToolbarValue('other')
})
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:deep(.el-breadcrumb__inner) {
  color: #000000 !important;
  font-size: 18px;
}

.my_work {
  margin: 0 auto 10px auto;
  padding: 0 !important;
  margin-top: 10px !important;
  margin-bottom: 20px !important;
  width: 1252px;
  min-height: 680px;
  background: #fff;
  color: #666;
  position: relative;

  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .f_j_b {
    justify-content: space-between;
  }

  .f_c {
    flex-direction: column;
  }

  .header {
    width: calc(1252px - 44px);

    .left {
      .text {
        font-size: 16px !important;
        font-weight: bold;
        color: #333;
      }
    }

    .right {
      font-weight: bold;
      font-size: 14px;
      color: #555;

      .checkbox {
        margin-right: 8px;
      }
    }
  }

  .list {
    .item {
      width: 100%;
      margin: 0;
      padding: 16px 0;
      border-left: 2px solid #fff;
      border-bottom: 1px solid #d9d9d9;
      font-size: 16px;
    }

    .todo {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 10px !important;
    }

    .todo:hover {
      border-left: 2px solid #0070ff;
      background-color: #f4faff;
      color: #0070ff;
    }

    .title {
      flex: 1;
      margin-right: auto;
      color: rgb(85, 119, 255);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .cate {
      width: 200px;
      text-align: center !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .info {
      margin-left: auto;
      width: 374px;
      text-align: right !important;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .dept {
        display: inline-block;
        width: 144px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .name {
        margin-left: 10px;
        width: 70px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: left;
      }

      .time {
        margin-left: 10px;
        width: 140px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .apply-flow {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 20px;
      border-left: 1px solid #eee;
      font-size: 14px;

      .dept {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    .icon {
      width: 16px;
      height: 15px;
      margin-right: 10px;

      img {
        width: auto;
        height: 18px;
      }
    }
  }

  .pager {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 20px;
  }
}
</style>
