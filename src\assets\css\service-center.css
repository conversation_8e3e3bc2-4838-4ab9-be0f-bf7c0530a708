﻿html,
body {
    height: auto;
}

.logo {
    line-height: normal;
}

.uk-section-default {
    margin: 0 0 150px 0;
    background: #f9fafd;
}

.custom-left-navigation-title {
    margin-bottom: 2px;
}

.custom-left-navigation-title img {
    width: 30px;
    height: 20px;
    padding-right: 10px;
    margin-bottom: 6px;
}

.custom-left-navigation-title span {
    font-size: 16px;
    color: #333;
}

.custom-left-menu-list {
    margin: 0 -20px;
}

.custom-left-menu-list li div {
    padding: 5px 0;
    text-align: right;
}

.custom-left-menu-list li div div {
    color: #666;
    font-size: 14px;
    display: inline-block;
    padding-right: 80px;
    cursor: pointer;
}

.custom-left-menu-list li div span {
    display: inline-block;
    padding-right: 10px;
    width: 20px;
}

.custom-left-menu-list li {
    border-left: 4px solid white;
    margin-left: -30px;
}

/*          .custom-left-menu-list li:hover {
                color: #0070FF;
                cursor: pointer;
                border-left: 4px solid #0070FF;
                background: #F4FAFF;
            }*/

.custom-left-menu-entry-content-itme {
    position: absolute;
    left: 192px;
    top: 0;
    z-index: 999;
    min-width: 1192px;
    min-height: 350px;
    border: 1px solid #c6ddfb;
    background-color: #fff;
    /*      -webkit-box-shadow: 2px 0 2px #c6ddfb;
        box-shadow: 2px 0 2px #c6ddfb;*/
    -webkit-transition: top 0.25s ease;
    transition: top 0.25s ease;
}

.custom-breadcrumb li {
    display: inline-block;
    margin: 5px;
}

.custom-breadcrumb li a {
    color: #333;
    font-size: 14px;
}

.custom-breadcrumb li a:hover {
    text-decoration: none;
    color: #333;
    font-size: 14px;
}

.custom-breadcrumb li span {
    display: inline-block;
    text-align: right;
}

.uk-breadcrumb li a {
    color: #333;
}

.custom-card {
    margin-top: 10px;
}

.custom-card .custom-one-level-img {
    width: 20px;
    height: 20px;
    margin-top: -8px;
}

.custom-card .custom-one-level-title {
    font-size: 16px;
    color: #333;
}

.custom-grid .custom-grid-child > .custom-grid-content {
    margin: 10px 0 0 0;
    border: 1px solid #e5e5e5;
    min-height: 350px;
}

.custom-two-level {
    padding: 20px 20px 0 20px;
}

.custom-two-level .custom-two-level-img {
    width: 100%;
    min-height: 60px;
    max-height: 80px;
    margin-bottom: 15px;
    border-radius: 2px;
}

.custom-two-level hr {
    margin: 10px 0 !important;
}

.custom-grid-content .custom-small-img {
    width: 20px;
    height: 20px;
    margin-top: -5px;
}

.custom-grid-content ul {
    padding: 0 20px;
}

.view-more {
    width: 100%;
    height: 45px;
    border-top: 1px solid #e5e5e5;
    text-align: center;
    color: #999;
    font-size: 16px;
    padding: 10px 0 0 0;
}

.view-more .view-all {
    cursor: context-menu;
}
/**左侧显示菜单项*/
.custom-left-menu-bar {
    width: 240px;
    position: fixed;
    z-index: 999;
}

.custom-right-menu-content {
    position: relative;
    left: 230px;
    margin-bottom: 100px;
}

.custom-left-menu-entry-content-itme {
    padding: 20px;
}

.custom-table-menu-list tbody tr td:before {
    content: "";
    position: absolute;
    width: 1.5em;
    height: 1.5em;
    background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

.custom-table-menu-list tbody tr td a {
    padding-left: 20px;
    color: #333;
}

.custom-table-menu-list tbody tr td a:hover {
    padding-left: 20px;
    color: #333;
    text-decoration: none;
}

.uk-grid > * {
    padding-left: 10px;
    padding-right: 20px;
}

.uk-grid {
    padding-left: 30px;
    margin-right: -20px;
}

.custom-two-level-title {
    font-size: 16px;
    color: #333;
}

.right-entry-list li {
    width: 214px;
    height: 30px;
    vertical-align: middle;
    line-height: 30px;
    clear: both;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

.right-entry-list li .jump-link {
    display: block;
    padding-left: 15px;
    color: #666;
    font-size: 14px;
    width: 100%;
    text-decoration: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-left: 10px;
}

.right-entry-list li .jump-link:hover {
    color: #666;
}

.right-entry-list li .jump-link:before {
    content: "";
    position: absolute;
    left: -10px;
    top: 5px;
    width: 1.5em;
    height: 1.5em;
    background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
    background-repeat: no-repeat;
    background-position: 50% 50%;
    text-decoration: none;
}

.right-entry-list li .jump-link-modal {
    display: inline;
    color: #32b66b;
    font-size: 12px;
    border: 1px solid #32b66b;
    padding: 0 4px;
    border-radius: 3px;
    width: 58px;
}

.right-entry-list li .jump-link-modal:hover {
    display: inline;
    color: #32b66b;
    text-decoration: none;
    font-size: 12px;
    border: 1px solid #32b66b;
    padding: 0 4px;
    border-radius: 3px;
    width: 58px;
}

.custom-grid .custom-grid-item {
    min-width: 285px;
}

.right-entry-list .service-directory-modal {
    display: inline-block;
    width: 58px;
    /* position: relative; */
    /* top: -10px; */
}

.right-entry-list span {
    width: 120px;
    display: inline-block;
    /* position: relative; */
    right: 0;
    /* top: -6px; */
}
/**scroll*/
.right-entry-list::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 2px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
}

.right-entry-list::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: skyblue;
    background-image: -webkit-linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
}

.right-entry-list::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
}

.view-entry-all {
    position: absolute;
    border: 1px solid #c6ddfb;
    min-width: 256px;
    z-index: 10;
    background: #f9fafd;
    margin-top: -6px;
}

.view-entry-all {
    padding: 10px 20px 0 20px;
}

.uk-notification {
    top: 80px;
}

.uk-notification-message {
    background: #edf2fc;
}

.uk-notification-message div {
    color: #39f;
    font-size: 18px;
}

.uk-flex {
    display: block;
    padding-top: 10%;
}

.el-message--error {
    margin-top: 60px;
}
