<template>
  <div
    class="lay-container"
    v-loading="layoutList.length + groupList.length == 0"
    style="min-height: 80vh">
    <div class="layout-form" v-if="isShowLayoutConfig">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-checkbox v-model="config.showGroup" label="开启组件" size="large" />
          <el-checkbox v-model="config.showRemove" label="允许删除" size="large" />
          <el-checkbox
            v-model="config.draggable"
            label="允许拖拽"
            size="large"
            @change="changeDraggable"
          />
          <el-checkbox v-model="config.resizable" label="允许缩放" size="large" />
          <el-checkbox v-model="config.alignment" label="向上对齐" size="large" />
          <el-checkbox
            v-model="config.isDefault"
            label="默认布局"
            size="large"
            @change="changedefaultLayout"
          />
        </el-form-item>
        <el-form-item class="layout-form-button">
          <el-button size="small" @click="isShowConfig">
            <el-icon style="vertical-align: middle">
              <template #default>
                <Hide />
              </template>
            </el-icon>
            <span style="vertical-align: middle">隐藏</span>
          </el-button>
        </el-form-item>
        <el-form-item class="layout-form-button">
          <el-button size="small" @click="saverLayout">
            <el-icon style="vertical-align: middle">
              <template #default>
                <FolderChecked />
              </template>
            </el-icon>
            <span style="vertical-align: middle">保存</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="min-layout-show" @mouseenter="showElement" @mouseleave="hideElement">
      <div class="min-layout" v-show="showConfig" @click="isShowLayoutConfig = true">
        <Setting class="icon" />
        <el-text class="mx-1">布局配置</el-text>
      </div>
    </div>
    <!-- <div class="layoutJSON">
      <span>布局数据</span>
      <div class="columns">
        <div class="layoutItem" v-for="item in layoutList" :key="item.i">
          <b>{{ item.i }}</b
          >: [{{ item.x }}, {{ item.y }}, {{ item.w }}, {{ item.h }}]
        </div>
      </div>
    </div> -->
    <div class="group" v-if="config.showGroup">
      <div
        v-for="item in groupList"
        :key="item.i"
        class="droppable-element"
        draggable="true"
        unselectable="on"
        @drag="drag"
        @dragend="dragend"
        @dragstart="(e) => dragstart(item)"
        @click="handelList(item)"
      >
        <span>{{ item.name }}</span>
        <!-- <hr />
        <span>点击添加</span> -->
      </div>
    </div>

    <div
      id="content"
      draggable="false"
      @dragleave="dragleave"
      @dragover.prevent="dragover"
      @drop="drop"
      style="min-height: 95vh; margin-bottom: 20px; margin-top: -20px"
    >
      <grid-layout
        ref="gridlayout"
        :margin="[margin.widthMargin, margin.heightMargin]"
        :layout="layoutList"
        :col-num="margin.colNum"
        :row-height="margin.rowHeight"
        :is-draggable="config.draggable"
        :is-resizable="config.resizable"
        :vertical-compact="config.alignment"
        :use-css-transforms="true"
        :auto-size="true"
        class="top"
      >
        <grid-item
          style="margin-top: 20px"
          class="father"
          ref="items"
          v-for="item in layoutList"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          :minH="item.minH"
          :minW="item.minW"
          :style="item.itemId == 'serviceLeftNav' || item.itemId == 'leftNav' ? leftNayStyel : ''"
        >
          <el-scrollbar v-if="item.category == 'serviceCenter'">
            <component
              :id="item.i"
              :style="itemStyle"
              :itemJson="item.itemJson"
              :itemId="item.i"
              :serviceList="serviceList"
              :is="getItem(item.itemId)"
            />
          </el-scrollbar>
          <component
            v-else
            :id="item.i"
            :style="itemStyle"
            :itemJson="item.itemJson"
            :itemId="item.i"
            :serviceList="serviceList"
            :is="getItem(item.itemId)"
          />
          <span class="remove" v-show="config.showRemove" @click="removeItem(item.i)">
            <CloseBold style="width: 1em; height: 1em; margin-right: 2px" />
          </span>
        </grid-item>
      </grid-layout>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { FolderChecked, Hide, Setting } from '@element-plus/icons-vue'
import { CloseBold } from '@element-plus/icons-vue'
import * as LayoutApi from '@/api/system/layout'
import { customList } from '@/utils/custom'
import { useAppStore } from '@/store/modules/app'
import emitter from '@/utils/mitt'
import * as PortalApi from '@/api/system/portal'
import { searchStore } from '@/store/modules/search'
import { getIsOpen, toggleConnectStatus } from '@/utils/websocket'
const useStore = useAppStore()
const gSearchStore = searchStore()
defineOptions({
  name: 'Layout'
})

// 布局配置
const config = ref({
  id: '',
  userId: '',
  showGroup: false,
  showRemove: true,
  draggable: false, //拖拽
  resizable: true, //缩放
  alignment: true,
  isDefault: true
})

const margin = ref({
  widthMargin: 1,
  heightMargin: 10,
  colNum: 512,
  rowHeight: 0.1
})

const itemStyle = ref()

const leftNayStyel = ref()

const show_loading = ref(false)

// 布局对象信息
interface LayoutItem {
  id: string
  dataId: string
  name: string
  sign: string
  category: string
  itemId: string
  x: number
  y: number
  w: number
  h: number
  i: number
  minH: string
  minW: string
  itemJson: {
    titleList: {}
    itemSystem: {}
    isShow: {}
  }
  startUse: number
}
// 服务中心数据
const serviceList = ref()
// 获取布局列表数据
const layoutList = ref<Array<LayoutItem>>([])
const groupList = ref<Array<LayoutItem>>([])
const getLayoutList = () => {
  show_loading.value = true
  layoutList.value = []
  groupList.value = []

  try {
    leftNayStyel.value = {
      position: 'fixed',
      left: '95px',
      zIndex: 999
    }
    if (useStore.category == 'serviceCenter') {
      // 获取数据列表
      serviceDataHandel(gSearchStore.searchValue)
    } else {
      LayoutApi.getLayoutList({ category: useStore.category }).then((ret) => {
        if (ret.use) {
          layoutList.value = ret.use
        }
        if (ret.noUse) {
          groupList.value = ret.noUse
        }
        if (ret.config) {
          config.value = ret.config
        }
      })
    }
  } finally {
    show_loading.value = false
  }
}

//删除项
const removeItem = (i: number) => {
  const item = layoutList.value.find((item) => item.i == i)
  layoutList.value = layoutList.value.filter((item) => item.i !== i)
  if (item) {
    item.startUse = 0
    groupList.value.push(item)
  }
}

const startItem = ref()
const winWidth = ref(0)

// 拖动开始
const dragstart = (item: LayoutItem) => {
  startItem.value = item
  const myDiv = document.getElementById('content')
  if (myDiv) {
    winWidth.value = myDiv.offsetWidth
  }
  console.log(winWidth.value)
}
// 计算位置
const drag = (event) => {
  const partSize = winWidth.value / margin.value.colNum
  const pageX = event.pageX > 0 ? event.pageX : 0
  const pageY = event.pageY > 0 ? event.pageY : 0
  //列数
  const x =
    Math.round(pageX / partSize) >= margin.value.colNum
      ? margin.value.colNum
      : Math.round(pageX / partSize)
  startItem.value.x = x - startItem.value.w <= 0 ? 0 : x - startItem.value.w

  startItem.value.y = Math.round(pageY / 10 - 10 < 0 ? 0 : pageY / 10 - 10)
}
const dragend = () => {
  handelList(startItem.value)
}

const dragleave = () => {
  console.log('拖拽离开')
}
const dragover = () => {}
const drop = () => {}

let items: any = ref(null) //布局项引用{[key: string]:any}

let gridlayout: any = ref([]) //布局组件引用

const getItem = (itemId: string) => {
  const item = customList.value.find((item) => item.id == itemId)
  return item?.name
}
// 添加集合数据
const handelList = (item) => {
  groupList.value = groupList.value.filter((ite) => ite.i != item.i)
  const olditem = layoutList.value.find((ite) => ite.i == item.i)
  if (!olditem) {
    item.startUse = 1
    layoutList.value.push(item)
  }
}
// 更新添加布局
const layouts = ref<Array<any>>([])
const saverLayout = async () => {
  layouts.value = []
  const allList = [...layoutList.value, ...groupList.value]
  allList.forEach((item) => {
    let layStr = JSON.stringify({ x: item.x, y: item.y, w: item.w, h: item.h })
    let layItem = { id: item.id, layout: layStr, dataId: item.dataId, startUse: item.startUse }
    layouts.value.push(layItem)
  })
  const ret = await LayoutApi.addLayoutList({
    layoutList: layouts.value,
    config: config.value
  })
  if (ret) {
    useMessage().success('保存成功')
    getLayoutList()
  } else {
    useMessage().error('保存失败')
  }
}

const showConfig = ref(false)
// 展示
const showElement = () => {
  showConfig.value = true
}

const hideElement = () => {
  showConfig.value = false
}

// 服务中心数据处理
const serviceDataHandel = (value: string) => {
  serviceList.value = []
  PortalApi.serviceCenterloadData({ Search: value }).then((res) => {
    serviceList.value = res
    LayoutApi.getLayoutList({ category: useStore.category }).then((ret) => {
      if (ret.use) {
        if (value != '') {
          //存在搜索，过滤数据
          const nameLsit = serviceList.value.map((item) => {
            return item.Name
          })
          layoutList.value = ret.use.filter((item) => {
            if (
              nameLsit.includes(item.name) ||
              item.itemId == 'serviceLeftNav' ||
              item.itemId == 'serviceMenuItem'
            ) {
              return item
            }
          })
        } else {
          layoutList.value = ret.use
        }
      }
      if (ret.noUse) {
        groupList.value = ret.noUse
      }
      if (ret.config) {
        config.value = ret.config
      }
    })
  })
}
// 刷新布局
onMounted(() => {
  getLayoutList()
  if (!getIsOpen.value) {
    toggleConnectStatus()
  }
})

// 状态监听，暴露给父类
watch(
  () => useStore.category,
  () => {
    getLayoutList()
  },
  {
    deep: true
  }
)
watch(
  () => gSearchStore.searchValue,
  () => {
    getLayoutList()
  },
  {
    deep: true
  }
)
//允许拖转和缩放
const changeDraggable = () => {
  if (config.value.draggable) {
    itemStyle.value = {
      'pointer-events': 'none'
    }
  } else {
    itemStyle.value = {}
  }
}

// 使用默认布局
const changedefaultLayout = async () => {
  const res = await LayoutApi.saveLayoutConfig(config.value)
  if (res) {
    getLayoutList()
  }
}
const isShowLayoutConfig = ref(false)
const isShowConfig = () => {
  isShowLayoutConfig.value = false
  config.value.showGroup = false
}

// 处理高度变化
emitter.on('navHeightChange', (obj: any) => {
  layoutList.value.forEach((item) => {
    if (item.i == obj.itemId) {
      item.h = obj.height / margin.value.heightMargin + 5
    }
  })
})
const minWith = ref(1000)
emitter.on('navWidthChange', (obj: any) => {
  layoutList.value.forEach((item) => {
    if (item.i == obj.itemId) {
      minWith.value = item.w <= minWith.value ? item.w : minWith.value
      if (obj.width > 500) {
        item.w = margin.value.colNum * (obj.width / window.innerWidth + 0.2)
      } else {
        item.w = minWith.value < 60 ? 60 : minWith.value
      }
    }
  })
})

onUnmounted(() => {
  emitter.off('navHeightChange')
})
</script>

<style scoped lang="scss">
.lay-container {
  width: 90%;
  margin: 0 auto;
  margin-top: 3.5%;
  margin-bottom: 3%;
}

.father {
  position: 'fixed';
  box-sizing: border-box;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
}
::v-deep .vue-grid-placeholder {
  background-color: #ccc;
}

.layoutJSON {
  background: #ddd;
  border: 1px solid black;
  margin-top: 10px;
  padding: 10px;
}
.columns {
  -moz-columns: 120px;
  -webkit-columns: 120px;
  columns: 120px;
}
.bg {
  background-color: blue !important;
}
.group {
  position: absolute;
  left: 10px;
  top: 10%;
  // background-color: #fff;
  padding: 10px;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
  height: 78vh;
  width: 65px;
  overflow: auto;
  overflow-x: hidden;
  position: fixed;
  z-index: 99;
}
.remove {
  cursor: pointer;
  position: absolute;
  width: auto;
  top: 0; /* 调整距离顶部的距离 */
  right: 0; /* 调整距离左侧的距离 */
}
// 拖转图标
:deep(.vue-resizable-handle) {
}
.droppable-element {
  background-color: #fff;
  height: 50px;
  width: 60px;
  border: 1px solid #ccc;
  box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  font-size: 13px;
  margin-top: 10px;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.layout-form {
  position: absolute;
  background-color: #fff;
  width: 100px;
  top: 10%;
  right: 10px;
  position: fixed;
  z-index: 99;
  text-align: center;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
  .demo-form-inline {
    margin-left: 10px;
    margin-right: 5px;
  }
  .layout-form-button {
    margin-left: 10%;
  }
}
.min-layout-show {
  position: fixed;
  right: 10px;
  top: 110px;
  width: 72px;
  height: 200px;
  cursor: pointer;
  background-color: transparent;
}
.min-layout {
  position: fixed;
  right: 10px;
  bottom: 800px;
  width: 72px;
  height: 72px;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  background: #fff;

  .text {
    margin-top: auto;
    margin-bottom: 10px;
  }
  .mx-1 {
    margin-bottom: 10px;
  }
  .icon {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
    margin-bottom: auto;
    margin-top: 10px;
    transition: all 0.1s;
  }
}
.emptybox {
  width: 100px;
  height: 50px;
  position: absolute;
  z-index: 999;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  .line-scale div {
    background-color: #888;
  }
}
</style>
