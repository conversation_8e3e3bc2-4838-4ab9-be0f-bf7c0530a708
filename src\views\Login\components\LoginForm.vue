<template>
  <el-form
    v-loading="loginLoading"
    element-loading-text="正在登录，请稍后..."
    v-show="getShow"
    ref="formLogin"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
  >
    <el-row style="margin-right: -10px; margin-left: -10px">
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="username">
          <el-input
            v-model="loginData.loginForm.username"
            :placeholder="t('login.usernamePlaceholder')"
            :prefix-icon="iconAvatar"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="password">
          <el-input
            v-model="loginData.loginForm.password"
            :placeholder="t('login.passwordPlaceholder')"
            :prefix-icon="iconLock"
            type="password"
            show-password
            @keyup.enter="ge_code()"
          />
        </el-form-item>
      </el-col>

      <el-col
        :span="24"
        style="padding-right: 10px; padding-left: 10px; margin-top: -5px; margin-bottom: -20px"
      >
        <el-form-item>
          <el-row justify="space-between" style="width: 100%">
            <el-col :span="6">
              <el-checkbox v-model="loginData.loginForm.rememberMe">
                {{ t('login.remember') }}
              </el-checkbox>
            </el-col>
            <el-col :offset="6" :span="12">
              <el-link style="float: right" type="primary" @click="updatePassword()">{{
                t('login.forgetPassword')
              }}</el-link>
            </el-col>
          </el-row>
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <XButton
            :loading="loginLoading"
            :title="t('login.login')"
            class="w-[100%]"
            type="primary"
            @click="ge_code()"
          />
        </el-form-item>
      </el-col>

      <Verify
        ref="verify"
        :captchaType="captchaType"
        :imgSize="{ width: '400px', height: '200px' }"
        mode="pop"
        @success="handleLogin"
      />
    </el-row>
  </el-form>

  <UpdatePassword ref="updatePasswordRef" />
</template>

<script lang="ts" setup>
import { ElLoading } from 'element-plus'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useIcon } from '@/hooks/web/useIcon'
import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import { LoginStateEnum, useFormValid, useLoginState } from './useLogin'
import UpdatePassword from '@/views/Login/components/updatePassword.vue'
import { CATEGORY, useLayoutCache } from '@/hooks/web/categoryCache'
import { CACHE_KEY } from '@/hooks/web/useCache'

defineOptions({ name: 'LoginForm' })
const updatePasswordRef = ref()
const { t } = useI18n()
const message = useMessage()
const iconHouse = useIcon({ icon: 'ep:house' })
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const iconLock = useIcon({ icon: 'ep:lock' })
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const { setLoginState, getLoginState } = useLoginState()
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const loginLoading = ref(false)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字
const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN)
const { wsCache } = useLayoutCache()

const LoginRules = {
  tenantName: [required],
  username: [required],
  password: [required]
}

const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: '中国电建昆明院',
    username: '',
    password: '',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})

const socialList = [
  { icon: 'ant-design:wechat-filled', type: 30 },
  { icon: 'ant-design:dingtalk-circle-filled', type: 20 },
  { icon: 'ant-design:github-filled', type: 0 },
  { icon: 'ant-design:alipay-circle-filled', type: 0 }
]

// 获取验证码
const ge_code = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}

// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}

// 记住我
const getLoginFormCache = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe,
      tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
    loginData.loginForm.password = generateRandomString(100) + loginData.loginForm.password
  }
}

// 根据域名，获得租户信息
const getTenantByWebsite = async () => {
  const website = location.host
  const res = await LoginApi.getTenantByWebsite(website)
  if (res) {
    loginData.loginForm.tenantName = res.name
    authUtil.setTenantId(res.id)
  }
}

const loading = ref() // ElLoading.service 返回的实例
const emit = defineEmits(['updateLoading'])

// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  emit('updateLoading', true)

  try {
    await getTenantId()
    const data = await validForm()

    if (!data) {
      return
    }

    // loginData.loginForm.captchaVerification = params.captchaVerification

    const loginFormData = { ...loginData.loginForm }

    //检查密码
    if (loginFormData.password && loginFormData.password.length > 100) {
      loginFormData.password = loginFormData.password.slice(100)
    }
    loginFormData.password = encodePassword(loginFormData.password)

    //发起登录请求
    const res = await LoginApi.login(loginFormData)
    if (!res) {
      return
    }

    loading.value = ElLoading.service({
      lock: true,
      text: '加载中，请稍后...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    //记住密码
    if (loginData.loginForm.rememberMe) {
      if (loginData.loginForm.password && loginData.loginForm.password.length > 100) {
        loginData.loginForm.password = loginData.loginForm.password.slice(100)
      }
      authUtil.setLoginForm(loginData.loginForm)
    } else {
      authUtil.removeLoginForm()
    }

    authUtil.setToken(res)

    loading.value.close()

    if (!redirect.value) {
      redirect.value = '/'
    }

    //触发4A认证
    wsCache.set(CACHE_KEY.login4A, false) //登录前处理缓存
    LoginApi.getSimplInfo().then((ret) => {
      if (authUtil.Auth4ALogin(ret)) {
        // 判断是否为SSO登录
        if (redirect.value.indexOf('sso') !== -1) {
          window.location.href = window.location.href.replace('/Portal/home/<USER>', '')
        } else {
          if (wsCache.get(CATEGORY.IsLayout)) {
            push('/Portal/newsLayout')
          } else {
            push({ path: redirect.value || permissionStore.addRouters[0].path })
          }
        }
      }
    })
  } catch (error) {
    // emit('updateLoading', false)
    loginLoading.value = false
  }
  let autoLogoutTimeout = LoginApi.getAutoLoginOutTimeout()
  console.log('无操作退出登录时间======================>', autoLogoutTimeout)
}

//修改密码
const updatePassword = () => {
  updatePasswordRef.value.open(loginData.loginForm.username)
}

// 密码编码
const encodePassword = (value) => {
  return btoa(unescape(encodeURIComponent(value)))
}

// 社交登录
const doSocialLogin = async (type: number) => {
  if (type === 0) {
    message.error('此方式未配置')
  } else {
    loginLoading.value = true
    if (loginData.tenantEnable === 'true') {
      // 尝试先通过 tenantName 获取租户
      await getTenantId()
      // 如果获取不到，则需要弹出提示，进行处理
      if (!authUtil.getTenantId()) {
        try {
          const data = await message.prompt('请输入租户名称', t('common.reminder'))
          if (data?.action !== 'confirm') throw 'cancel'
          const res = await LoginApi.getTenantIdByName(data.value)
          authUtil.setTenantId(res)
        } catch (error) {
          if (error === 'cancel') return
        } finally {
          loginLoading.value = false
        }
      }
    }
    // 计算 redirectUri
    // tricky: type、redirect需要先encode一次，否则钉钉回调会丢失。
    // 配合 Login/SocialLogin.vue#getUrlValue() 使用
    const redirectUri =
      location.origin +
      '/social-login?' +
      encodeURIComponent(`type=${type}&redirect=${redirect.value || '/'}`)

    // 进行跳转
    const res = await LoginApi.socialAuthRedirect(type, encodeURIComponent(redirectUri))
    window.location.href = res
  }
}

watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)

onMounted(() => {
  getLoginFormCache()
  getTenantByWebsite()
})

const generateRandomString = (length) => {
  // 定义所有可能的字符
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    // 随机选择一个字符
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

const checkppp = (str) => {
  // 移除字符串两端的空格
  const trimmedStr = str.trim()
  // 正则表达式匹配Base64编码的字符，并检查是否以'='结束（允许0-2个'='作为填充）
  const base64Regex = /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$/

  // 匹配并检查字符串长度是否为4的倍数（如果不包括'='）
  // 注意：因为trim()可能会改变字符串的长度，所以这里使用原始长度减去两端的空格数来校验
  const isLengthValid = (trimmedStr.length + str.length - trimmedStr.length) % 4 === 0

  return base64Regex.test(trimmedStr) && isLengthValid
}
</script>

<style lang="scss" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.login-code {
  float: right;
  width: 100%;
  height: 38px;

  img {
    width: 100%;
    height: auto;
    max-width: 100px;
    vertical-align: middle;
    cursor: pointer;
  }
}
</style>
