import request from '@/config/axios'

//消息列表
export const getMsgList = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/GetReceiveList`,params })
}

//标记已读
export const setMsgRed = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/SetMsgRed`,params })
}
//删除
export const deleteMsg = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/DeleteMsg`,params })
}
//常用联系人
export const getNormalLinkManTree = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/GetNormalLinkManTree`,params })
}
//联系人树数据
export const getOrgTree = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/GetOrgTree`,params })
}
//详情
export const getReceiveModel = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/GetReceiveModel`,params })
}
//已读人数据
export const getReceiveReadList = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/GetReceiveReadList`,params })
}

//发送消息

export const saveMsg = async (params) => {
  return await request.post({ url: `system/InfoManage/Msg/Save`,params })
}
