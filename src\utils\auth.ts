import {useCache, CACHE_KEY} from '@/hooks/web/useCache'
import {TokenType} from '@/api/login/types'
import {decrypt, encrypt} from '@/utils/jsencrypt'
import * as PortalApi from '@/api/portal'
import emitter from './mitt'

const {wsCache} = useCache()

const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'

// 获取token
export const getAccessToken = () => {
  // 此处与TokenKey相同，此写法解决初始化时Cookies中不存在TokenKey报错
  return wsCache.get(AccessTokenKey) ? wsCache.get(AccessTokenKey) : wsCache.get('ACCESS_TOKEN')
}

// 刷新token
export const getRefreshToken = () => {
  return wsCache.get(RefreshTokenKey)
}

// 设置token
export const setToken = (token: TokenType) => {
  wsCache.set(RefreshTokenKey, token.refreshToken)
  wsCache.set(AccessTokenKey, token.accessToken)
}

// 删除token
export const removeToken = () => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return 'Bearer ' + token
}
// ========== 账号相关 ==========

export type LoginFormType = {
  tenantName: string
  username: string
  password: string
  rememberMe: boolean
}

export const getLoginForm = () => {
  const loginForm: LoginFormType = wsCache.get(CACHE_KEY.LoginForm)
  if (loginForm) {
    loginForm.password = decrypt(loginForm.password) as string
  }
  return loginForm
}

export const setLoginForm = (loginForm: LoginFormType) => {
  loginForm.password = encrypt(loginForm.password) as string
  wsCache.set(CACHE_KEY.LoginForm, loginForm, {exp: 30 * 24 * 60 * 60})
}

export const removeLoginForm = () => {
  wsCache.delete(CACHE_KEY.LoginForm)
}

// ========== 租户相关 ==========

export const getTenantId = () => {
  return wsCache.get(CACHE_KEY.TenantId)
}

export const setTenantId = (username: string) => {
  wsCache.set(CACHE_KEY.TenantId, username)
}

// ===========4A认证相关============

interface UserVO {
  id: string
  avatar: string
  nickname: string
  deptId: string
  menuStrs: string
  workNo: string
  duties: string
}

//是否加载
export const Auth4ALogin = (user) => {
  // 无论是否成功， 不影数据处理
  PortalApi.Get4ALoginUrl().then(ret => {
    if (ret) {
      load4A(user)
    } else {
      wsCache.set(CACHE_KEY.login4A, true)
    }
  }).catch(error => {
    console.log(error)
    wsCache.set(CACHE_KEY.login4A, true)
  })
  return true;
}
const load4A = (user: UserVO) => {
  //进行4A系统认证互信操作,仅处理测试用户
  // const login_4A = localStorage.getItem("4Alogin");
  //  && (login_4A == null || login_4A == "")
  const testuser = "2008033,2014028,2018019,2018143,2018147,2022469,2021026";
  const duties = "处级,副处级,科级,副科级,一级,二级,三级,七岗,六岗";
  const testdept = "01AAA006";  //待定
  if ((testuser.indexOf(user.workNo) != -1 || testdept.indexOf(user.deptId) != -1) || duties.indexOf(user.duties) != -1) {
    PortalApi.set4ACookie().then(ret => {
      if (ret) {
        PortalApi.Jsonp4ACookieis(ret).then(res => {
          // const jsonPart = res.match(/{.*}/)[0];
          // const data = JSON.parse(res);
          set4Acookie(res)
        })
      }
    }).catch(err => {
      console.log(err)
      wsCache.set(CACHE_KEY.login4A, true)
    })
  } else {
    wsCache.set(CACHE_KEY.login4A, true)
  }
  return true;
}
const toUrlopenWin = (url: string) => {
  const open = openWindow(url, "4AWeb", 100, 100)
  if (open) {
    setTimeout(() => {
      if (!open.closed) {
        open.close();
        emitter.emit('set4AcookieOK')
        wsCache.set(CACHE_KEY.login4A, true)
      }
    }, 4000);
  }
}

// 打开窗口
const openWindow = (url: string, title: string, width: number, height: number) => {
  // 获取屏幕的宽度和高度
  // const screenWidth = screen.width;
  // const screenHeight = screen.height;
  // 计算窗口的x和y坐标以使其居中
  const x = 0;
  const y = 0;

  const newWindow = window.open(
    url,
    title,
    `width=${width},height=${height},left=${x},top=${y},location=no,menubar=no,scrollbars=no,toolbar=no,status=no`
  );
  if (newWindow && newWindow.focus) {
    newWindow.focus();
  }
  return newWindow;
};

const set4Acookie = (e) => {
  // console.log("调用set4Acookie")
  if (e.data != undefined && e.data.setCookie == true) {
    const reurl = "https://yrz.powerchina.cn/tokenManager-kmy/?url=https://yrz.powerchina.cn"; //集团互信地址
    toUrlopenWin(reurl)
  }
}

// ========== 自动退出登录相关 ==========

class AutoLogoutManager {
  private timer: NodeJS.Timeout | null = null
  private timeout: number = 0
  private lastActivityTime: number = 0
  private readonly STORAGE_KEY = 'AUTO_LOGOUT_INFO'
  private readonly ACTIVITY_CHANNEL = 'AUTO_LOGOUT_ACTIVITY'
  private readonly LOGOUT_CHANNEL = 'AUTO_LOGOUT_TRIGGER'
  private readonly ACTIVITY_EVENTS = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
  private broadcastChannel: BroadcastChannel | null = null
  private logoutBroadcastChannel: BroadcastChannel | null = null

  constructor() {
    this.initBroadcastChannels()
    this.bindActivityListeners()
  }

  /**
   * 初始化跨标签页通信通道
   */
  private initBroadcastChannels() {
    try {
      // 用于同步用户活动的通道
      this.broadcastChannel = new BroadcastChannel(this.ACTIVITY_CHANNEL)
      this.broadcastChannel.onmessage = (event) => {
        if (event.data.type === 'activity') {
          this.lastActivityTime = event.data.timestamp
          this.saveToStorage()
          this.resetTimer()
        }
      }

      // 用于触发退出登录的通道
      this.logoutBroadcastChannel = new BroadcastChannel(this.LOGOUT_CHANNEL)
      this.logoutBroadcastChannel.onmessage = (event) => {
        if (event.data.type === 'logout') {
          // 其他标签页已经处理了退出登录，当前页面也需要跳转
          window.location.href = '/portal/home/<USER>'
        }
      }
    } catch (error) {
      console.warn('BroadcastChannel not supported, fallback to single tab mode:', error)
    }
  }

  /**
   * 启动自动退出定时器
   * @param timeoutSeconds 超时时间（秒）
   */
  start(timeoutSeconds: number) {
    this.timeout = timeoutSeconds * 1000 // 转换为毫秒
    this.lastActivityTime = Date.now()

    // 保存到本地存储，用于页面刷新后恢复
    this.saveToStorage()

    this.resetTimer()
    console.log(`自动退出登录已启动，超时时间：${timeoutSeconds}秒`)
  }

  /**
   * 重置定时器
   */
  private resetTimer() {
    this.clearTimer()

    if (this.timeout > 0) {
      this.timer = setTimeout(() => {
        this.handleAutoLogout()
      }, this.timeout)
    }
  }

  /**
   * 清除定时器
   */
  private clearTimer() {
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  }

  /**
   * 处理用户活动
   */
  private handleUserActivity = () => {
    const now = Date.now()
    this.lastActivityTime = now
    this.saveToStorage()
    this.resetTimer()

    // 向其他标签页广播用户活动
    if (this.broadcastChannel) {
      try {
        this.broadcastChannel.postMessage({
          type: 'activity',
          timestamp: now
        })
      } catch (error) {
        console.warn('Failed to broadcast activity:', error)
      }
    }
  }

  /**
   * 绑定用户活动监听器
   */
  private bindActivityListeners() {
    this.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, this.handleUserActivity, true)
    })
  }

  /**
   * 解绑用户活动监听器
   */
  private unbindActivityListeners() {
    this.ACTIVITY_EVENTS.forEach(event => {
      document.removeEventListener(event, this.handleUserActivity, true)
    })
  }

  /**
   * 处理自动退出登录
   */
  private async handleAutoLogout() {
    try {
      console.log('用户长时间无操作，自动退出登录')

      // 通知其他标签页即将退出登录
      if (this.logoutBroadcastChannel) {
        try {
          this.logoutBroadcastChannel.postMessage({
            type: 'logout',
            timestamp: Date.now()
          })
        } catch (error) {
          console.warn('Failed to broadcast logout:', error)
        }
      }

      // 清理本地存储
      this.clearStorage()

      // 导入用户store并执行退出登录
      const { useUserStoreWithOut } = await import('@/store/modules/user')
      const { useTagsViewStoreWithOut } = await import('@/store/modules/tagsView')
      const { deleteUserCache } = await import('@/hooks/web/useCache')

      const userStore = useUserStoreWithOut()
      const tagsViewStore = useTagsViewStoreWithOut()

      await userStore.loginOut().catch(() => {})
      deleteUserCache()
      tagsViewStore.delAllViews()

      // 跳转到登录页
      window.location.href = '/portal/home/<USER>'

    } catch (error) {
      console.error('自动退出登录失败:', error)
    }
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage() {
    const data = {
      timeout: this.timeout,
      lastActivityTime: this.lastActivityTime,
      startTime: Date.now()
    }
    wsCache.set(this.STORAGE_KEY, data)
  }

  /**
   * 从本地存储恢复
   */
  restore() {
    const data = wsCache.get(this.STORAGE_KEY)
    if (!data || !getAccessToken()) {
      return false
    }

    const { timeout, lastActivityTime } = data
    const now = Date.now()
    const timePassed = now - lastActivityTime

    // 如果已经超时，直接退出登录
    if (timePassed >= timeout) {
      this.handleAutoLogout()
      return true
    }

    // 否则继续计时，剩余时间 = 原超时时间 - 已过时间
    this.timeout = timeout
    this.lastActivityTime = lastActivityTime

    const remainingTime = timeout - timePassed
    this.timer = setTimeout(() => {
      // 在执行退出前，再次检查是否有其他标签页的活动更新了时间
      const currentData = wsCache.get(this.STORAGE_KEY)
      if (currentData && currentData.lastActivityTime > this.lastActivityTime) {
        // 有更新的活动时间，重新计算
        this.lastActivityTime = currentData.lastActivityTime
        const newTimePassed = Date.now() - this.lastActivityTime
        if (newTimePassed < this.timeout) {
          // 还没超时，重新设置定时器
          const newRemainingTime = this.timeout - newTimePassed
          this.timer = setTimeout(() => this.handleAutoLogout(), newRemainingTime)
          return
        }
      }
      this.handleAutoLogout()
    }, remainingTime)

    console.log(`自动退出登录已恢复，剩余时间：${Math.round(remainingTime / 1000)}秒`)
    return true
  }

  /**
   * 停止自动退出功能
   */
  stop() {
    this.clearTimer()
    this.clearStorage()
    console.log('自动退出登录已停止')
  }

  /**
   * 清理本地存储
   */
  private clearStorage() {
    wsCache.delete(this.STORAGE_KEY)
  }

  /**
   * 销毁实例
   */
  destroy() {
    this.stop()
    this.unbindActivityListeners()

    // 关闭BroadcastChannel
    if (this.broadcastChannel) {
      this.broadcastChannel.close()
      this.broadcastChannel = null
    }
    if (this.logoutBroadcastChannel) {
      this.logoutBroadcastChannel.close()
      this.logoutBroadcastChannel = null
    }
  }
}

// 创建全局实例
export const autoLogoutManager = new AutoLogoutManager()
