import {useCache, CACHE_KEY} from '@/hooks/web/useCache'
import {TokenType} from '@/api/login/types'
import {decrypt, encrypt} from '@/utils/jsencrypt'
import * as PortalApi from '@/api/portal'
import emitter from './mitt'

const {wsCache} = useCache()

const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'

// 获取token
export const getAccessToken = () => {
  // 此处与TokenKey相同，此写法解决初始化时Cookies中不存在TokenKey报错
  return wsCache.get(AccessTokenKey) ? wsCache.get(AccessTokenKey) : wsCache.get('ACCESS_TOKEN')
}

// 刷新token
export const getRefreshToken = () => {
  return wsCache.get(RefreshTokenKey)
}

// 设置token
export const setToken = (token: TokenType) => {
  wsCache.set(RefreshTokenKey, token.refreshToken)
  wsCache.set(AccessTokenKey, token.accessToken)
}

// 删除token
export const removeToken = () => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return 'Bearer ' + token
}
// ========== 账号相关 ==========

export type LoginFormType = {
  tenantName: string
  username: string
  password: string
  rememberMe: boolean
}

export const getLoginForm = () => {
  const loginForm: LoginFormType = wsCache.get(CACHE_KEY.LoginForm)
  if (loginForm) {
    loginForm.password = decrypt(loginForm.password) as string
  }
  return loginForm
}

export const setLoginForm = (loginForm: LoginFormType) => {
  loginForm.password = encrypt(loginForm.password) as string
  wsCache.set(CACHE_KEY.LoginForm, loginForm, {exp: 30 * 24 * 60 * 60})
}

export const removeLoginForm = () => {
  wsCache.delete(CACHE_KEY.LoginForm)
}

// ========== 租户相关 ==========

export const getTenantId = () => {
  return wsCache.get(CACHE_KEY.TenantId)
}

export const setTenantId = (username: string) => {
  wsCache.set(CACHE_KEY.TenantId, username)
}

// ===========4A认证相关============

interface UserVO {
  id: string
  avatar: string
  nickname: string
  deptId: string
  menuStrs: string
  workNo: string
  duties: string
}

//是否加载
export const Auth4ALogin = (user) => {
  // 无论是否成功， 不影数据处理
  PortalApi.Get4ALoginUrl().then(ret => {
    if (ret) {
      load4A(user)
    } else {
      wsCache.set(CACHE_KEY.login4A, true)
    }
  }).catch(error => {
    console.log(error)
    wsCache.set(CACHE_KEY.login4A, true)
  })
  return true;
}
const load4A = (user: UserVO) => {
  //进行4A系统认证互信操作,仅处理测试用户
  // const login_4A = localStorage.getItem("4Alogin");
  //  && (login_4A == null || login_4A == "")
  const testuser = "2008033,2014028,2018019,2018143,2018147,2022469,2021026";
  const duties = "处级,副处级,科级,副科级,一级,二级,三级,七岗,六岗";
  const testdept = "01AAA006";  //待定
  if ((testuser.indexOf(user.workNo) != -1 || testdept.indexOf(user.deptId) != -1) || duties.indexOf(user.duties) != -1) {
    PortalApi.set4ACookie().then(ret => {
      if (ret) {
        PortalApi.Jsonp4ACookieis(ret).then(res => {
          // const jsonPart = res.match(/{.*}/)[0];
          // const data = JSON.parse(res);
          set4Acookie(res)
        })
      }
    }).catch(err => {
      console.log(err)
      wsCache.set(CACHE_KEY.login4A, true)
    })
  } else {
    wsCache.set(CACHE_KEY.login4A, true)
  }
  return true;
}
const toUrlopenWin = (url: string) => {
  const open = openWindow(url, "4AWeb", 100, 100)
  if (open) {
    setTimeout(() => {
      if (!open.closed) {
        open.close();
        emitter.emit('set4AcookieOK')
        wsCache.set(CACHE_KEY.login4A, true)
      }
    }, 4000);
  }
}

// 打开窗口
const openWindow = (url: string, title: string, width: number, height: number) => {
  // 获取屏幕的宽度和高度
  // const screenWidth = screen.width;
  // const screenHeight = screen.height;
  // 计算窗口的x和y坐标以使其居中
  const x = 0;
  const y = 0;

  const newWindow = window.open(
    url,
    title,
    `width=${width},height=${height},left=${x},top=${y},location=no,menubar=no,scrollbars=no,toolbar=no,status=no`
  );
  if (newWindow && newWindow.focus) {
    newWindow.focus();
  }
  return newWindow;
};

const set4Acookie = (e) => {
  // console.log("调用set4Acookie")
  if (e.data != undefined && e.data.setCookie == true) {
    const reurl = "https://yrz.powerchina.cn/tokenManager-kmy/?url=https://yrz.powerchina.cn"; //集团互信地址
    toUrlopenWin(reurl)
  }
}
