<template>
  <div class="uk-flex work-bottom" id="workBottom">
    <div class="uk-card uk-card-default bottom-card">
      <div class="uk-flex uk-card-header">
        <div>
          <img :src="getIcon(ItemTitle.type)" />
          <span class="uk-card-title" :style="itemStyle.titleStyle">{{ ItemTitle.title }}</span>
          <span class="uk-badge" v-if="ItemTitle.isShowNum" v-text="ItemNewCount"></span>
        </div>
        <a @click="tabMore(ItemTitle.type, $event)"
          ><img src="@/assets/icons/portal/icon-more.png"
        /></a>
      </div>

      <div class="uk-card-body">
        <!-- 我的消息 -->
        <template v-if="ItemCount > 0">
          <ul class="uk-list" v-if="ItemTitle.type == 'msg'">
            <template v-for="(item, index) in ItemList" :key="index">
              <li class="uk-flex" @click="OpenMsg(item)">
                <div>
                  <img src="@/assets/icons/portal/icon-msg.png" />
                </div>
                <div :title="item.Title" v-text="item.Title"></div>
                <div>
                  <hr class="uk-divider-vertical" />
                </div>
                <div>
                  <ul class="uk-list-disc">
                    <li
                      ><span style="width: 50px; margin-right: 20px">发送人</span>
                      <span>{{ item.SenderName }}</span>
                    </li>
                    <li
                      ><span style="width: 50px; margin-right: 20px">时间</span>
                      <span v-if="item && item.SendTime">{{ item.SendTime.split(' ')[0] }}</span>
                    </li>
                  </ul>
                </div>
              </li>
            </template>
          </ul>

          <ul class="uk-list" v-if="ItemTitle.type == 'myfile'">
            <!-- 我的文件 -->
            <template v-for="(item, index) in ItemList" :key="index">
              <li class="uk-flex">
                <div>
                  <img :src="getFullUrl(item.Icon)" />
                </div>
                <div :title="item.FileName.substring(item.FileName.indexOf('_') + 1)"
                  >{{ item.FileName.substring(item.FileName.indexOf('_') + 1) }}
                </div>
                <div>
                  <hr class="uk-divider-vertical" />
                </div>
                <div>
                  <img
                    @click.stop="DownLoadAndPreviewFile(item, 0)"
                    title="下载"
                    src="@/assets/icons/portal/icon-download.png"
                  />
                  <img @click.stop="CancelSc(item)" title="取消收藏" src="@/assets/imgs/sc_a.png" />
                </div>
              </li>
            </template>
          </ul>
        </template>

        <div class="emptybox" style="margin-top: 50px" v-if="show_loading">
          <div class="line-scale loading_icon">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>

        <template v-if="ItemCount <= 0 && !show_loading">
          <div class="empty-img">
            <img src="@/assets/imgs/nodata.png" />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as portal from '@/api/portal'
import { DownLoadAndPreviewFile, getFullUrl, tabMore } from '@/layout/portal/admin'
import emitter from '@/utils/mitt'
import { messageInfo } from '@/utils/websocket'

defineOptions({
  name: 'MsgAndMyFile'
})
interface itemType {
  titleList: {
    type: ''
    title: ''
    isShowNum: boolean
  }
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {}
}
const props = defineProps<{ itemJson: itemType }>()

const ItemList = ref()
const ItemCount = ref()
const ItemNewCount = ref()
const show_loading = ref(false)

const list = ref([
  {
    type: 'msg',
    title: '我的消息',
    isShowNum: false
  },
  {
    type: 'myfile',
    title: '我的文件',
    isShowNum: false
  }
])

// 加载对象类型
const ItemTitle = ref({
  type: 'msg',
  title: '我的消息',
  isShowNum: false
})

const itemStyle = ref({
  modelStyle: '',
  titleStyle: '',
  spanStyle: ''
})

//监听属性
emitter.on('msg', (obj: itemType) => {
  init(obj)
})

//解除绑定事件
onUnmounted(() => {
  emitter.off('msg')
})

// 监听socket消息变化
watch(messageInfo, () => {
  handleSocket(messageInfo.value)
})

const handleSocket = (obj) => {
  // console.log(obj.type)
  if ('msg' == obj.type) {
    loadMsgAndFile('msg') //更新消息
  }

  if ('myfile' == obj.type) {
    loadMsgAndFile('myfile') //更新我的文件
  }
}

// 加载列表数据
const loadMsgAndFile = (type) => {
  show_loading.value = true
  ItemList.value = []

  //我的待办
  portal
    .wdgz_list({
      type: type,
      page: 1,
      pageSize: 10
    })
    .then((ret) => {
      show_loading.value = false
      ret = ret.data

      ItemList.value = ret.records
      ItemCount.value = ret.records.length
      ItemNewCount.value = ret.total
      if (ret.Count > 99) {
        ItemNewCount.value = '99+'
      }
    })
    .catch(() => {
      console.log('数据加载异常')
    })
    .finally(() => {
      show_loading.value = false
    })
}

//打开消息
const OpenMsg = (msg) => {
  window.open('/Portal/MsgList')
}

// 图标
const getIcon = (type: string) => {
  if (type == 'msg') {
    return '/src/assets/icons/portal/icon-msg.png'
  } else if (type == 'myfile') {
    return '/src/assets/icons/portal/icon-wjj.png'
  }
}

//取消收藏
const CancelSc = (file) => {}

const init = (itemJson: itemType) => {
  if (itemJson) {
    ItemTitle.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
  }
  if (ItemTitle) {
    loadMsgAndFile(ItemTitle.value.type)
  }
}

onMounted(() => {
  init(props.itemJson)
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');
@import url('@/assets/css/admin.css');

.empty-img {
  text-align: center;
  margin-top: 5%;
  img {
    width: 100px;
  }
  div {
    color: rgb(153, 153, 153);
    margin-top: 10px;
    text-align: center;
  }
}
</style>
