<template>
    <el-input
      v-model="query"
      style="width: 240px"
      placeholder="名称"
      @input="onQueryChanged"
    />
    <el-button @click="toggleExpandAll">
      {{ isAllExpanded ? '收起' : '展开' }}
    </el-button>
    <el-tree-v2
      ref="treeRef"
      style="max-width: 600px;"
      :data="treeData"
      :props="treeProps"
      :highlight-current="true"
      node-key="id"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="checkedKeys"
      :check-strictly="checkStrictly"
      :show-checkbox="showCheckbox"
      :check-on-click-node="true"
      :expand-on-click-node="true"
      :filter-method="filterMethod"
      :item-size="44"
      :height="600"
      @node-click="clickNode"
      @node-contextmenu="contextmenuNode"
      @check-change="handleCheckChange"
    />

</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { ElTreeV2 } from 'element-plus'
import type {TreeNode, TreeNodeData} from 'element-plus/es/components/tree-v2/src/types'
import {array} from "vue-types";
const emit = defineEmits(['nodeClicked','nodeContextmenu']);

const getKey = (prefix: string, id: number) => {
  return `${prefix}-${id}`
}
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  showCheckbox: {
    type: Boolean,
    default: false,
  },
  checkStrictly: {
    type: Boolean,
    default: false,
  },
  expandedKeys: {
    type: Array,
    default: () => [],
  },
});

const treeProps = {
  children: 'children',
  label: 'name',
};
const isAllExpanded = ref(false)
const query = ref('')
const treeRef = ref<InstanceType<typeof ElTreeV2>>()

const checkedKeys = ref([])
const treeData = props.data
const onQueryChanged = (query: string) => {
  treeRef.value!.filter(query)
}
const filterMethod = (query: string, node: TreeNode) => {
  return node.name?.toString()?.includes(query) ?? false;
}

const toggleExpandAll = () => {
  isAllExpanded.value = !isAllExpanded.value;
  if (isAllExpanded.value) {
    expandAll(treeData);
  } else {
    collapseAll();
  }
};

// 展开所有行
const expandAll = (rows) => {
  const keys = getAllIds(rows);
  treeRef.value?.setExpandedKeys(keys)
};

//遍历所有的节点获取id
const getAllIds = (tree) => {
  return tree.reduce((acc, node) => {
    acc.push(node.id);
    if (node.children) {
      acc.push(...getAllIds(node.children));
    }
    return acc;
  }, []);
}

// 收起所有行
const collapseAll = () => {
  treeRef.value?.setExpandedKeys([])
};
//鼠标单击事件
const clickNode = (data: TreeNodeData, node: TreeNode, e: MouseEvent) => {
  emit('nodeClicked', node);
}
const contextmenuNode = (e: Event, data: TreeNodeData, node: TreeNode) => {
  emit('nodeContextmenu', node);
}
const getCheckedNodes = () => {
  return treeRef.value?.getCheckedNodes();
};
/**
 * 特殊联动操作
 * @param data
 * @param checked
 * @param indeterminate
 */
const handleCheckChange = (data, checked, indeterminate) => {
  //获取ref所有被选中的值
  const allCheckedKeys = treeRef.value.getCheckedKeys();
  //获取当前节点的所有子孙节点的key
  const allChildKeys = getAllChildKeys(data);
  //获取当前节点的父节点的key
  const parentKeys = getParentNodes(treeData, data);
  if (checked) {
    //当前节点被选中，则将其所有父类节点和子类节点都设置为选中状态
    let combinedArray = [...new Set([...allCheckedKeys])];
    //设置整个树选中的key
    treeRef.value.setCheckedKeys(combinedArray);
  }else{
    //取消的时候，只取消其子节点
    if(allCheckedKeys.length>0){
      //从allCheckedKeys中删除allChildKeys
      const leave = allCheckedKeys.filter((key) => !allChildKeys.includes(key));
      treeRef.value.setCheckedKeys(leave);
    }
  }
};

//获取子节点
const getAllChildKeys = (node) => {
  const result = [];
  const traverse = (nodes) => {
    if (!nodes) return;
    nodes.forEach((n) => {
      result.push(n.id);
      if (n.children && n.children.length > 0) {
        traverse(n.children);
      }
    });
  };
  traverse(node.children);
  return result;
};
//获取父节点
const getParentNodes = (tree, node) => {
  const path = [];
  const findNode = (nodes, target) => {
    for (const n of nodes) {
      if (n.id === target.id) {
        return true;
      }
      if (n.children && n.children.length > 0) {
        path.push(n.id);
        if (findNode(n.children, target)) {
          return true;
        }
        path.pop();
      }
    }
    return false;
  };
  findNode(tree, node);
  return path;
};
defineExpose({ getCheckedNodes });
</script>
