<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-form-item label="标题显示" />
        </el-col>
        <el-col :span="18">
          <el-form-item>
            <el-select
              v-model="value"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择工作列表"
              @change="titleChange"
            >
              <el-option
                v-for="item in option"
                :key="item.id"
                :label="item.title"
                :value="item.sign"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-checkbox
            label="任务数量显示"
            v-model="form.isShow.showCount"
            :value="!form.isShow.showCount"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.listStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">模块样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.modelStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">日期格式</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="form.itemStyle.dateFormat" placeholder="请选择" clearable>
            <el-option
              v-for="item in dateOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { dateOption } from '@/utils/formatTime'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'WorkListForm'
})
const props = defineProps<{ itemJson: any }>()

const value = ref(['newtask', 'completetask', 'focus'])

const option = [
  { id: '0', sign: 'newtask', title: '我的待办' },
  { id: '1', sign: 'completetask', title: '我的已办' },
  { id: '2', sign: 'focus', title: '我的关注' }
]

const form = ref({
  titleList: option,
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    listStyle: '',
    dateFormat: 'YYYY-MM-DD'
  },
  isShow: {
    showCount: true
  }
})

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('workList', form.value)
  },
  { immediate: true, deep: true }
)

defineExpose({ form })

const titleChange = () => {
  const label = option.filter((item) => value.value.indexOf(item.sign) !== -1)
  form.value.titleList = label
}
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue()
})
const setValue = () => {
  value.value = form.value.titleList.map((item) => item.sign)
}
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
