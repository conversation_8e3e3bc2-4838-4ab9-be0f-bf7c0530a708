<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题显示</el-text>
        </el-col>
        <el-col :span="18">
          <el-form-item>
            <el-select
              v-model="value"
              multiple
              placeholder="请选择"
              clearable
              @change="titleChange"
            >
              <el-option
                v-for="item in titleList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.titleStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.spanStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'LinkForm'
})

// 标题列表
const titleList = ref([
  { id: 'ZTZL', title: '专题专栏', name: '专题专栏' },
  { id: 'KSRK', title: '快速入口', name: '快速入口' },
  { id: 'YQLJ', title: '友情链接', name: '友情链接' }
])
const value = ref(['ZTZL', 'KSRK', 'YQLJ'])

const form = ref({
  titleList: titleList.value,
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
  isShow: {}
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('link', form.value)
  },
  { immediate: true, deep: true }
)

const titleChange = () => {
  let data = titleList.value.filter((item) => value.value.indexOf(item.id) !== -1)
  form.value.titleList = data
}

const setValue = () => {
  value.value = form.value.titleList.map((item) => item.id)
}
const props = defineProps<{ itemJson: any }>()
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue
})

</script>

<style scoped>
.link_header {
  margin-left: 100px;
  font-size: 14px;
}
</style>
