/**********************  common ***************/

a:focus {
    outline: none;
}

ul {
    list-style: none;
}

.clear {
    clear: both;
}

@font-face {
    font-family: "simsun"; /* 这个名字可以自己定义 */
    src: url("/WebResource/Master/fonts/simsun.eot"); /* IE9 Compat Modes */ /*这里以及下面的src后面的地址填的都是自己本地的相对地址*/
    src: url("/WebResource/Master/fonts/simsun.eot?#iefix") format("embedded-opentype"), url("/WebResource/Master/fonts/simsun.woff") format("woff"),
    /* IE6-IE8 */ url("/WebResource/Master/fonts/simsun.ttf") format("truetype"),
    /* Modern Browsers */ url("/WebResource/Master/fonts/simsun.svg#simsun"); /* Legacy iOS */ /* Safari, Android, iOS */
}

@font-face {
    font-family: "MicrosoftYaHei"; /* 这个名字可以自己定义 */
    src: url("/WebResource/Master/fonts/MicrosoftYaHei.eot"); /* IE9 Compat Modes */ /*这里以及下面的src后面的地址填的都是自己本地的相对地址*/
    src: url("/WebResource/Master/fonts/MicrosoftYaHei.eot?#iefix") format("embedded-opentype"), url("/WebResource/Master/fonts/MicrosoftYaHei.woff") format("woff"),
    /* IE6-IE8 */ url("/WebResource/Master/fonts/MicrosoftYaHei.ttf") format("truetype"),
    /* Modern Browsers */
    url("/WebResource/Master/fonts/MicrosoftYaHei.svg#MicrosoftYaHei"); /* Legacy iOS */ /* Safari, Android, iOS */
}

@font-face {
    font-family: "sourcehansans"; /* 这个名字可以自己定义 */
    src: url("/WebResource/Master/fonts/sourcehansans.eot"); /* IE9 Compat Modes */ /*这里以及下面的src后面的地址填的都是自己本地的相对地址*/
    src: url("/WebResource/Master/fonts/sourcehansans.eot?#iefix") format("embedded-opentype"), url("/WebResource/Master/fonts/sourcehansans.woff") format("woff"),
    /* IE6-IE8 */ url("/WebResource/Master/fonts/sourcehansans.ttf") format("truetype"),
    /* Modern Browsers */
    url("/WebResource/Master/fonts/sourcehansans.svg#sourcehansans"); /* Legacy iOS */ /* Safari, Android, iOS */
}

@font-face {
    font-family: "pingfangsc"; /* 这个名字可以自己定义 */
    src: url("/WebResource/Master/fonts/pingfangsc.eot"); /* IE9 Compat Modes */ /*这里以及下面的src后面的地址填的都是自己本地的相对地址*/
    src: url("/WebResource/Master/fonts/pingfangsc.eot?#iefix") format("embedded-opentype"), url("/WebResource/Master/fonts/pingfangsc.woff") format("woff"),
    /* IE6-IE8 */ url("/WebResource/Master/fonts/pingfangsc.ttf") format("truetype"),
    /* Modern Browsers */
    url("/WebResource/Master/fonts/pingfangsc.svg#pingfangsc"); /* Legacy iOS */ /* Safari, Android, iOS */
}

.scrollbar {
    /*三角箭头的颜色*/
    scrollbar-arrow-color: #fff;
    /*滚动条滑块按钮的颜色*/
    scrollbar-face-color: #d9d9d9;
    /*滚动条整体颜色*/
    scrollbar-highlight-color: #ededed;
    /*滚动条阴影*/
    scrollbar-shadow-color: #ededed;
    /*滚动条轨道颜色*/
    scrollbar-track-color: #ededed;
    /*滚动条3d亮色阴影边框的外观颜色——左边和上边的阴影色*/
    scrollbar-3dlight-color: #ededed;
    /*滚动条3d暗色阴影边框的外观颜色——右边和下边的阴影色*/
    scrollbar-darkshadow-color: #ededed;
    /*滚动条基准颜色*/
    scrollbar-base-color: #ededed;
    scrollbar-width: 3px;
}

.body-index-right::-webkit-scrollbar,
body::-webkit-scrollbar {
    /*滚动条整体样式*/

    width: 3px; /*高宽分别对应横竖滚动条的尺寸*/

    height: 3px;
}

.body-left::-webkit-scrollbar {
    /*滚动条整体样式*/

    width: 3px; /*高宽分别对应横竖滚动条的尺寸*/

    height: 8px;
}

.body-index-right::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb,
.body-left::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/

    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #535353;
}

.body-index-right::-webkit-scrollbar-track,
body::-webkit-scrollbar-track,
.body-left::-webkit-scrollbar-track {
    /*滚动条里面轨道*/

    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    background: #ededed;
}

table {
    /*border-color:red;*/
}

div {
    /*border-color:red !important;*/
}

/******************* common end *******************/
.cont {
    margin: 0 auto;
    height: 60px;
    width: 100%;
    min-width: 1300px;
    /*background-color: #FFB800;*/
    position: fixed;
    z-index: 999999;
}

.page:before {
    content: "";
    display: table;
    clear: both;
}

.page .body {
    /*margin-top: 60px;*/
}

.cont:before {
    content: "";
    display: table;
    clear: both;
}

header {
    height: 60px;
    width: 100%;
    position: fixed;
    border-bottom: 1px solid #f0f0f0;
    overflow: hidden;
    background: #fff url(../images/top_bg.jpg) no-repeat center center;
    background-size: cover;
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.1);
    z-index: 999999;
}

    header .logo {
        float: left;
        left: 0px;
        height: 39px;
        position: relative;
    }

        header .logo img {
            /*background-color: #FD482C;*/
            height: 30px;
            margin-top: 16px;
            margin-left: 32px;
        }

    header .master-ctrl {
        /*background:url(images/icojl.png) -40px 0;*/
        position: absolute;
        bottom: 5px;
        cursor: pointer;
        transition: left 0.3s;
        z-index: 1;
        color: #999;
        /*background-color: #FFFF00;*/
        height: 21px;
    }

.master-ctrl-out {
    left: 0px;
}

.master-ctrl-in {
    left: 278px;
}

header .master-ctrl:hover {
    color: #1cb2f5;
}

header .master-ctrl i:before {
    font-size: 20px;
    font-weight: 100;
}

header .header-control {
    max-width: 470px;
    height: 35px;
    line-height: 35px;
    /*background-color:#FD482C;*/
    float: right;
    margin-top: 8px;
    font-size: 14px;
    text-align: right;
}

    header .header-control ul {
        list-style: none;
        margin: 0 auto;
        padding: 0;
        /*background-color: #14f386;*/
        height: 100%;
        display: inline-block;
    }

        header .header-control ul li {
            float: left;
            margin-right: 20px;
            /*background-color: #FF00FF;*/
            border: 1px solid transparent;
            cursor: pointer;
        }

            header .header-control ul li.ctrl_tasks,
            header .header-control ul li.ctrl_message {
                position: relative !important;
            }

            header .header-control ul li span {
                display: block;
                font-size: 12px;
            }

                header .header-control ul li span:nth-child(1) {
                    margin: 0 auto;
                    width: 27px;
                    height: 27px;
                }

                header .header-control ul li span.hbm_newtasks {
                    background: url("../images/daiban_icon.png") no-repeat center center;
                    background-size: cover;
                    margin-bottom: -2px;
                }

                header .header-control ul li span.hbm_newtasks_active {
                    background: url("../images/daiban_icon_active.png") no-repeat center center;
                    background-size: cover;
                    margin-bottom: -2px;
                }

                header .header-control ul li span:nth-child(2) {
                    height: 16px;
                    line-height: 16px;
                }

                header .header-control ul li span.hbm_msg {
                    background: url("../images/message_icon.png") no-repeat center center;
                    background-size: cover;
                    margin-bottom: -2px;
                }

                header .header-control ul li span.hbm_msg_active {
                    background: url("../images/message_icon_active.png") no-repeat center center;
                    background-size: cover;
                    margin-bottom: -2px;
                }

header a {
    text-decoration: none;
}

header .header-control a {
    margin-left: 10px;
    font-size: 22px;
}

header .header-control .ctrl_curinfo {
    display: inline-block;
}

    header .header-control .ctrl_curinfo a {
        font-size: 18px;
    }

header .header-control a:hover {
    color: #ffb800 !important;
}

header .header-control li.ctrl_home a {
    color: #079d0a;
}

header .header-control li.ctrl_kmyxxglxt a {
    color: #0000ff;
}

header .header-control li.ctrl_task a {
    color: #fd482c;
}

header .header-control li.ctrl_msg a {
    color: #d99d22;
}

header .header-control li.ctrl_kjtd a {
    color: #eb7350;
}

header .header-control li.ctrl_pcenter a {
    color: #d99d22;
}

header .header-control ul li span.gcs_taskcount {
    position: absolute;
    min-width: 16px;
    height: 16px;
    top: -4px;
    padding: 1px;
    right: -6px;
    border-radius: 5px;
    background-color: #fa7582;
    color: #fff;
    margin-left: 0px;
    line-height: 14px;
    font-size: 12px;
    text-align: center;
    overflow: hidden;
}

header .header-control ul li span.gcs_msgcount {
    position: absolute;
    min-width: 16px;
    height: 16px;
    top: -4px;
    right: -6px;
    padding: 1px;
    border-radius: 5px;
    background-color: #fa7582;
    color: #fff;
    margin-left: 0px;
    line-height: 14px;
    font-size: 12px;
    text-align: center;
    overflow: hidden;
}

header .sys_title {
    position: absolute;
    left: 35%;
    padding: 0;
    width: 30%;
    height: 30px;
    /*background-color: #1E9FFF;*/
    text-align: center;
    margin: 15px auto;
}

    header .sys_title h3 {
        background-color: #fff;
        display: inline-block;
        padding: 10px;
        font-size: 17px;
        color: #1d62ad;
        /*color:#21d32a;*/
    }

header .sys-tpmenu {
    position: absolute;
    bottom: 0px;
    left: 0px;
    height: 34px;
    background-color: #f0f0f0;
    width: 100%;
}

    header .sys-tpmenu .sys-tpmenu-left {
        width: 300px;
        float: left;
        /*background-color: #FF5722;*/
        height: 34px;
        transition: margin-left 0.3s;
    }

    header .sys-tpmenu .sys_tpmenu_right {
        height: 34px;
        /*background-color: #3C7FB1;*/
        line-height: 34px;
        text-align: center;
        overflow: hidden;
    }

    /*header .sys-tpmenu .sys-tpmenu-out*/
    /*{*/
    /*margin-left:-194px;*/
    /*}*/
    header .sys-tpmenu .sys-tpmenu-int {
        margin-left: 0px;
    }

    header .sys-tpmenu .sys_tpmenu_right ul {
        margin: 0;
        padding: 0;
        list-style: none;
        /*background-color: #F6F7F7;*/
        height: 30px;
        display: inline-block;
    }

        header .sys-tpmenu .sys_tpmenu_right ul li {
            float: left;
        }

            header .sys-tpmenu .sys_tpmenu_right ul li a {
                display: block;
                padding: 2px 10px;
                transition: all 0.4s;
                color: #555;
                font-weight: 400;
            }

                header .sys-tpmenu .sys_tpmenu_right ul li a:hover {
                    background-color: #187fee;
                    color: #fff;
                }

.cont header .header-curinfo {
    float: right;
    margin-top: 10px;
    margin-right: 40px;
    cursor: pointer;
    font-size: 13px;
}

    .cont header .header-curinfo span {
        display: inline-block;
        vertical-align: middle;
        line-height: 39px;
        font-size: inherit;
    }

        .cont header .header-curinfo span a {
            font-size: inherit;
        }

        .cont header .header-curinfo span:nth-child(2) {
            margin-left: 16px;
        }

        .cont header .header-curinfo span.ctrl_arrow {
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #fff;
        }

    .cont header .header-curinfo .ctrl_curinfo_ico {
        width: 48px;
        height: 48px;
        background: url("../imgs/yuanoaicos.png") no-repeat 0px -20px;
    }

.cont header .sys_tpmenu_right {
    height: 60px;
    line-height: 63px;
    margin: 0 auto;
}

    .cont header .sys_tpmenu_right ul {
        margin: 0;
        padding: 0;
        list-style: none;
        height: 60px;
        display: flex;
        align-items: center;
        margin-left: 24%;
    }

        .cont header .sys_tpmenu_right ul li {
            margin: 0 15px;
            float: left;
            font-size: 16px;
            border-bottom: 5px solid transparent;
            height: 60px;
            position: relative;
        }

            .cont header .sys_tpmenu_right ul li a {
                color: #373737;
                color: #000;
                font-weight: bold;
                font-size: inherit;
                font-size: 16px;
                text-decoration: none;
                /* font-family: "MicrosoftYaHei"; */
            }

            .cont header .sys_tpmenu_right ul li.gcs_cur {
                width: 80px;
                height: 60px;
                text-indent: -999px;
                cursor: pointer;
                border-bottom: 4px solid #0070ff;
            }

            .cont header .sys_tpmenu_right ul li.gcs_cur a{
                text-indent: -99999px;
            }


            .cont
            header
            .sys_tpmenu_right
            ul
            li.gcs_cur
            .b06f00ea-e1d8-4106-a292-ab073b72b2af {
                display: block;
                width: 80px;
                height: 60px;
                background: url("/WebResource/Master/images/index_icon_fgbm.png") no-repeat center center;
                background-size: 100% auto;
                background-position: 0px 23px;
            }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-c8fa-46be-933b-58512a434a68 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_bgzx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-b1de-43d2-9eb0-c01b09ecfe52 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_xmzx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-bf5d-49d4-a11c-15239cd47d55 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_fwzx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-97e2-4b8a-9767-74f0246ed37a {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_jczx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-b70d-46b1-9db4-9ff6e26e72f4 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_zxzx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad86013c-c665-4d10-b202-70f64adb7b07 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_cxzx.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .ad8800b2-cbbe-420c-8d29-ce5144356ed0 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_xtgl.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont
                header
                .sys_tpmenu_right
                ul
                li.gcs_cur
                .b00600c6-1ede-47fc-b36e-bd3bb030a076 {
                    display: block;
                    width: 80px;
                    height: 60px;
                    background: url("/WebResource/Master/images/index_icon_wdbm.png") no-repeat center center;
                    background-size: 100% auto;
                    background-position: 0px 23px;
                }

                .cont header .sys_tpmenu_right ul li.gcs_cur:after {
                    content: "";
                    border: 5px solid;
                    border-color: transparent transparent #0070ff transparent;
                    position: absolute;
                    left: 45%;
                    bottom: 0px;
                }

.cont header .sys_tp_search {
    width: 200px;
    height: 36px;
    line-height: 36px;
    float: right;
    border-radius: 5px;
    margin-top: 12px;
    margin-right: 24px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .cont header .sys_tp_search input {
        width: 147px;
        height: 30px;
        line-height: 30px;
        vertical-align: middle;
        border: 0px;
        outline: none;
        background-color: transparent;
        text-align: left;
        margin-right: 10px;
    }

    .cont header .sys_tp_search .hbm-searchpanel {
        background: url(../images/search_icon.png) no-repeat center center;
        background-size: cover;
        width: 18px;
        height: 18px;
        display: block;
        cursor: pointer;
    }

.fabig {
    display: inline-block;
    font: normal normal normal 42px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}

.page {
    background-color: #f9fafd;
}

    .page .body {
        /*min-height: 1902px;*/
        background-color: #f9fafd;
    }

        .page .body:before {
            content: "";
            display: table;
            clear: both;
        }

        .page .body:after {
            content: "";
            display: table;
            clear: both;
        }

        .page .body .body-inner {
            width: 1378px;
            /*margin-left: 262px;*/
            margin: 0 auto;
            height: 100%;
            background-color: transparent;
            margin-top: 74px;
        }

/*.cont .body:after{*/
/*context:"";*/
/*display:table;*/
/*clear:both;*/
/*}*/

.cont .body .body-left {
    width: 230px;
    float: left;
    height: 100%;
    /*background-color: #42485B;*/
    background-color: #f0f0f0;
    /*min-height:555px;*/
    /*height:30px;*/
    transition: margin-left 0.3s;
    /*box-shadow: #0C0C0C 0px 0px 3px;*/
    border-right: 1px solid #bbbbbb;
    overflow: auto;
    margin-right: 10px;
    /*display:none;*/
}

    .cont .body .body-left .master-ctrl {
        height: 30px;
        background-color: #f0f0f0;
        text-align: center;
        cursor: pointer;
    }

        .cont .body .body-left .master-ctrl > i {
            display: inline-block;
            width: 30px;
            font-size: 18px;
            color: #1e9fff;
            vertical-align: middle;
        }

.cont .body .body-left-hide {
    width: 55px;
}

    .cont .body .body-left-hide .left-menu-item span.left-menu-link,
    .cont .body .body-left-hide .left-menu-item span.left-menu-space,
    .cont .body .body-left-hide .left-menu-item span.left-menu-expandico,
    .cont .body .body-left-hide .left-menu-item span.left-menu-noexpandico {
        display: none !important;
    }

    .cont .body .body-left .body-left-menu .left-menu-item .left-menu-space,
    .cont .body .body-left .body-left-menu .left-menu-item span.left-menu-ico,
    .cont .body .body-left-hide .left-menu-item span.left-menu-link {
        overflow: hidden;
    }

    .cont .body .body-left-hide .left-menu-item {
        text-align: left;
    }

        .cont .body .body-left-hide .left-menu-item .left-menu-ico {
            margin-left: 18px !important;
        }

.cont .body .body-left .body-left-menu {
    /*background-color: #42485B;*/
}

    .cont .body .body-left .body-left-menu .left-menu-item {
        width: 100%;
        height: 30px;
        /*line-height:30px;*/
        background-color: #fafafa;
        /*padding:0 5px 0 5px;*/
        border-bottom: 1px solid #f3f3f3;
        cursor: pointer;
        overflow: hidden;
        position: relative;
    }

        .cont .body .body-left .body-left-menu .left-menu-item .left-menu-item-inner {
            max-width: 1000px;
            width: 800px;
            position: static;
        }

    .cont .body .body-left .body-left-menu ul {
        margin: 0;
        padding: 0;
    }

    .cont .body .body-left .body-left-menu .left-menu-item .left-menu-space {
        width: 10px;
        height: 100%;
        /*background-color: #FF00FF;*/
    }

    .cont .body .body-left .body-left-menu .left-menu-item:hover {
        background-color: #1e9fff;
    }

        .cont .body .body-left .body-left-menu .left-menu-item:hover * {
            color: #fff;
        }

    .cont .body .body-left .body-left-menu .left-menu-item.hbm-cur {
        background-color: #1e9fff;
    }

        .cont .body .body-left .body-left-menu .left-menu-item.hbm-cur * {
            color: #fff;
        }

    .cont .body .body-left .body-left-menu .menu-item-j span.left-menu-ico {
        /*background-color: #fafafa;*/
        color: #21d32a;
    }

    .cont .body .body-left .body-left-menu .menu-item-o span {
        /*background-color: #EAEAEA;*/
    }

    .cont .body .body-left .body-left-menu .left-menu-item span {
        display: inline-block;
        color: #666;
        vertical-align: middle;
    }

    .cont .body .body-left .body-left-menu .left-menu-title span.left-menu-link {
        font-weight: 600;
        height: 100%;
        vertical-align: top;
    }

    .cont .body .body-left .body-left-menu span.left-menu-link {
        margin: 0 10px 0 10px;
        vertical-align: top;
        overflow: hidden;
    }

        .cont .body .body-left .body-left-menu span.left-menu-link a {
            display: block;
            width: 130px;
            overflow: hidden;
            height: 100%;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

    .cont
    .body
    .body-left
    .body-left-menu
    .left-menu-item
    span.left-menu-expandico {
        /*background-color: #444;*/
        width: 0px;
        height: 0px;
        border: 5px solid #aaa;
        border-color: #999 transparent transparent transparent;
        position: absolute;
        left: 197px;
        top: 0px;
        margin: 12px 10px 0px 0px;
    }

    .cont
    .body
    .body-left
    .body-left-menu
    .left-menu-item
    span.left-menu-noexpandico {
        /*background-color: #444;*/
        width: 0px;
        height: 0px;
        border: 5px solid #aaa;
        border-color: transparent transparent transparent #666;
        position: absolute;
        left: 200px;
        top: 0px;
        margin: 9px 7.5px 0px 0px;
    }

    .cont .body .body-left .body-left-menu .left-menu-subitems {
        display: none;
    }

    .cont .body .body-left .body-left-menu .left-menu-item span.left-menu-ico {
        /*background-color: #1E9FFF;*/
        font-size: 14px;
        width: 15px;
        height: 14px;
        margin-left: 10px;
        text-align: center;
        margin-top: -2px;
    }

.cont .body .body-content {
    /*background-color: #f7ddc2;*/
    /*min-height:555px;*/
    transition: margin-left 0.3s;
    margin-left: 300px;
    height: 100%;
    display: none;
}

    .cont .body .body-content .body-content-main {
        position: relative;
        height: 80%;
    }

        .cont .body .body-content .body-content-main .body-page-tab {
            height: 100%;
        }

        .cont .body .body-content .body-content-main .body-page-tab-hd {
            border-bottom: 1px solid #1e9fff;
        }

            .cont .body .body-content .body-content-main .body-page-tab-hd ul {
                margin: 0;
                padding: 0;
                min-height: 30px;
                overflow: hidden;
            }

                .cont .body .body-content .body-content-main .body-page-tab-hd ul li {
                    float: left;
                    margin-right: 10px;
                    height: 25px;
                    line-height: 25px;
                    margin-top: 5px;
                    border: 1px solid transparent;
                    border-radius: 0.25rem 0.25rem 0 0;
                    border-bottom-color: transparent;
                    background-color: #f5f5f5;
                    cursor: pointer;
                    min-width: 120px;
                    text-align: center;
                    font-size: 14px;
                    box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.5);
                }

                    .cont
                    .body
                    .body-content
                    .body-content-main
                    .body-page-tab-hd
                    ul
                    li:first-child {
                        margin-left: 10px;
                    }

                    .cont .body .body-content .body-content-main .body-page-tab-hd ul li span {
                        color: inherit;
                    }

        .cont .body .body-content .body-content-main .body-page-tab-bd {
            width: 100%;
        }

            .cont
            .body
            .body-content
            .body-content-main
            .body-page-tab-bd
            .body-page-content {
                display: none;
                /*min-height:525px;*/
                height: 100%;
                /*background-color: #FD482C;*/
                overflow: hidden;
            }

                .cont
                .body
                .body-content
                .body-content-main
                .body-page-tab-bd
                .body-page-content
                iframe {
                    width: 100%;
                    /*min-height:510px;*/
                    outline: none;
                    border: 0;
                    overflow: auto;
                    height: 90%;
                }

            .cont .body .body-content .body-content-main .body-page-tab-bd .page-cur {
                display: block;
            }

        .cont .body .body-content .body-content-main .body-page-tab-hd .page-cur {
            border-bottom: 1px solid #1e9fff;
            background-color: #1e9fff;
            color: #fff;
            box-shadow: 0 1px 0px 0px #1e9fff;
        }

            .cont
            .body
            .body-content
            .body-content-main
            .body-page-tab-hd
            .page-cur
            div:nth-child(1),
            .cont
            .body
            .body-content
            .body-content-main
            .body-page-tab-hd
            .page-cur
            span.tab-close {
                color: #fff !important;
            }

        .cont .body .body-content .body-content-main .body-page-tab-hd .page-hd {
            position: relative;
            border-radius: 0.25rem 0.25rem 0 0;
            /*background-color: #FFFF00;*/
            width: 100%;
            color: #666;
            height: 25px;
            line-height: 25px;
        }

            .cont
            .body
            .body-content
            .body-content-main
            .body-page-tab-hd
            .page-hd
            span.itempage-title {
                display: inline-block;
                max-width: 100px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                vertical-align: top;
                /*background-color: #FE752F;*/
            }

        .cont .body .body-content .body-content-main .body-page-tab-hd .page-ico {
            color: #5df756;
            /*margin: -5px 5px 0 0;*/
            margin-right: 5px;
            padding: 0 3px 0 3px;
            margin-top: 6px;
            overflow: hidden;
            width: 15px;
            height: 15px;
            /*height:100%;*/
        }

        .cont
        .body
        .body-content
        .body-content-main
        .body-page-tab-hd
        .page-hd
        .page-ico,
        .cont
        .body
        .body-content
        .body-content-main
        .body-page-tab-hd
        .page-hd
        .itempage-title {
            /*vertical-align: middle;*/
        }

        .cont .body .body-content .body-content-main .body-page-tab-hd .tab-close {
            position: absolute;
            /*background-color: #FF00FF;*/
            width: 10px;
            height: 10px;
            right: 2px;
            top: 0px;
            border-radius: 0 5px 0 0;
            font-size: 12px;
            color: #999;
        }

            .cont
            .body
            .body-content
            .body-content-main
            .body-page-tab-hd
            .tab-close:hover {
                width: 18px;
                height: 18px;
                font-size: 15px;
                color: #fff;
                border-radius: 0;
                background-color: #ff5722;
                right: -3px;
                top: -3px;
            }

.cont .body .body-content-dft {
    margin-left: 230px;
}

.cont .body .body-content-fill {
    margin-left: 55px;
}

.cont .body .body-index {
    /*background-color: #00FF00;*/
}

    .cont .body .body-index:before {
        content: "";
        display: table;
    }

    .cont .body .body-index .body-index-head {
        height: 130px;
        width: 100%;
        /*background-color: #FFB800;*/
        /*overflow:hidden;*/
    }

        .cont .body .body-index .body-index-head .body-index-head-inner {
            height: 100%;
            margin: 10px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .cont .body .body-index .body-index-head .body-index-head-block {
            height: 130px;
            background-color: #f0f0f0;
            width: 24%;
            /*margin:0 10px 0 10px;*/
            position: relative;
            cursor: pointer;
        }

        .cont .body .body-index .body-index-head .hbm-filemanage {
            background-color: #00a65a;
        }

        .cont .body .body-index .body-index-head .hbm-addressbook {
            background-color: #f79c10;
        }

        .cont .body .body-index .body-index-head .hbm-xiujiashengyu {
            background-color: #00c0ef;
        }

        .cont .body .body-index .body-index-head .hbm-daibanshixiang {
            background-color: #de4b39;
        }

        .cont .body .body-index .body-index-head .body-index-head-block .hbm-val {
            font-size: 30px;
            font-weight: 700;
            color: #fff;
            padding: 10px 20px 10px 20px;
        }

        .cont .body .body-index .body-index-head .body-index-head-block .hbm-title {
            padding: 10px 20px 10px 20px;
            font-size: 16px;
            color: #fff;
            font-weight: 700;
            height: 38px;
            background-color: rgba(150, 150, 150, 0.1);
            width: 100%;
            position: absolute;
            bottom: 30px;
            left: 0px;
        }

        .cont .body .body-index .body-index-head .body-index-head-block .hbm-more span {
            display: inline-block;
            /*background-color: #FFB800;*/
            vertical-align: top;
        }

        .cont .body .body-index .body-index-head .body-index-head-block .hbm-more {
            position: absolute;
            bottom: 0px;
            left: 0px;
            text-align: center;
            height: 30px;
            line-height: 30px;
            width: 100%;
            vertical-align: top;
            background-color: rgba(150, 150, 150, 0.1);
            color: #fff;
            cursor: pointer;
        }

            .cont
            .body
            .body-index
            .body-index-head
            .body-index-head-block
            .hbm-more:hover
            span {
                font-weight: 700;
            }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block:hover
        .hbm-more {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block:hover
        .hbm-title {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block
        .hbm-more
        .hbm-more-ico {
            /*background-color: #FF5722;*/
            background: url("images/more.png") no-repeat;
            -webkit-background-size: 18px 18px;
            background-size: 18px 18px;
            width: 18px;
            height: 18px;
            margin: 6px 5px 0px 5px;
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block.hbm-filemanage
        .hbm-ico-logo {
            background: url("images/masterimg.png") no-repeat -258px -9px;
            position: absolute;
            right: 10px;
            top: 20px;
            width: 72px;
            height: 62px;
            /*background-color: #FE752F;*/
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block.hbm-addressbook
        .hbm-ico-logo {
            background: url("images/masterimg.png") no-repeat -85px -9px;
            position: absolute;
            right: 10px;
            top: 20px;
            width: 72px;
            height: 62px;
            /*background-color: #FE752F;*/
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block.hbm-xiujiashengyu
        .hbm-ico-logo {
            background: url("images/masterimg.png") no-repeat -3px -10px;
            position: absolute;
            right: 10px;
            top: 16px;
            width: 72px;
            height: 64px;
            /*background-color: #FE752F;*/
        }

        .cont
        .body
        .body-index
        .body-index-head
        .body-index-head-block.hbm-daibanshixiang
        .hbm-ico-logo {
            background: url("images/masterimg.png") no-repeat -168px -10px;
            position: absolute;
            right: 10px;
            top: 16px;
            width: 76px;
            height: 62px;
            /*background-color: #FE752F;*/
        }

    .cont .body .body-index .body-index-content {
        /*background-color: #D0D0D0;*/
        display: flex;
        flex-direction: row;
        margin-left: 10px;
    }

        .cont .body .body-index .body-index-content .body-index-content-left {
            width: 60%;
            /*background-color: #FD61AB;*/
            margin: 10px 10px 10px 0px;
        }

            .cont
            .body
            .body-index
            .body-index-content
            .body-index-content-left
            .body-index-left-mainblock.hbm-warning {
                height: 230px;
                width: 100%;
                overflow: hidden;
                border-radius: 0.25rem;
                border: 1px solid #f0f0f0;
            }

                .cont
                .body
                .body-index
                .body-index-content
                .body-index-content-left
                .body-index-left-mainblock.hbm-warning
                .body-index-mainblock-innerhead {
                    height: 45px;
                    line-height: 45px;
                    border-bottom: 1px solid #f0f0f0;
                }

                    .cont
                    .body
                    .body-index
                    .body-index-content
                    .body-index-content-left
                    .body-index-left-mainblock.hbm-warning
                    .body-index-mainblock-innerhead
                    .hbm-title {
                        margin-left: 20px;
                        font-size: 18px;
                        font-weight: 700;
                        color: #999;
                    }

                .cont
                .body
                .body-index
                .body-index-content
                .body-index-content-left
                .body-index-left-mainblock.hbm-warning
                .body-index-mainblock-innercontent {
                    height: 200px;
                    width: 100%;
                    overflow: hidden;
                }

                .cont
                .body
                .body-index
                .body-index-content
                .body-index-content-left
                .body-index-left-mainblock.hbm-warning
                dl {
                    width: 80px;
                    height: 95px;
                    float: left;
                    margin: 10px;
                    text-align: center;
                }

            .cont
            .body
            .body-index
            .body-index-content
            .body-index-content-left
            .body-index-left-mainblock
            .body-index-mainblock-innercontent {
                padding: 45px 10px 10px 10px;
            }

            .cont
            .body
            .body-index
            .body-index-content
            .body-index-content-left
            .body-index-left-mainblock.hbm-warning
            dl {
                cursor: pointer;
            }

                .cont
                .body
                .body-index
                .body-index-content
                .body-index-content-left
                .body-index-left-mainblock.hbm-warning
                dl:hover {
                    color: #fd482c;
                }

                .cont
                .body
                .body-index
                .body-index-content
                .body-index-content-left
                .body-index-left-mainblock.hbm-warning
                dl
                dt {
                    font-weight: 700;
                    font-size: 15px;
                    border-radius: 0.25rem;
                    width: 60px;
                    height: 60px;
                    line-height: 60px;
                    color: #fff;
                    text-align: center;
                    margin: 0 auto;
                }

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-bmqb dt {
    background-color: #febb2f;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-xrzygs dt {
    background-color: #01c7c8;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-dqht dt {
    background-color: #fe752f;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-lzrys dt {
    background-color: #a092f1;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-dspwj dt {
    background-color: #5dd34b;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-mqlv dt {
    background-color: #3bb4fd;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-cqlv dt {
    background-color: #fd60ad;
}

.body-index-left-mainblock.hbm-warning dl.hbm-warningitem-htdqrs dt {
    background-color: #df92f2;
}

.body-index-content .body-index-left-mainblock.hbm-reports {
    height: 352px;
    border: 1px solid #f0f0f0;
    border-radius: 0.25rem;
    margin-top: 10px;
}

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innerhead {
        height: 45px;
        line-height: 45px;
        border-bottom: 1px solid #f0f0f0;
    }

        .body-index-content
        .body-index-left-mainblock.hbm-reports
        .body-index-mainblock-innerhead
        .hbm-title {
            font-size: 18px;
            font-weight: 700;
            color: #999;
            margin-left: 20px;
        }

.body-index-content
.body-index-left-mainblock
.body-index-mainblock-innercontent {
    width: 100%;
    height: 307px;
    overflow: hidden;
}

.body-index-content
.body-index-left-mainblock.hbm-reports
.body-index-mainblock-innercontent
dl:hover {
    color: #fd482c;
}

.body-index-content
.body-index-left-mainblock.hbm-reports
.body-index-mainblock-innercontent
dl
dd {
    line-height: 18px;
}

.body-index-content
.body-index-left-mainblock.hbm-reports
.body-index-mainblock-innercontent
dl
dt {
    width: 70px;
    height: 53px;
}

.body-index-content
.body-index-left-mainblock.hbm-reports
.body-index-mainblock-innercontent
dl {
    /*background-color: #FFFF00;*/
    cursor: pointer;
    margin: 10px 20px;
    float: left;
    width: 80px;
    height: 95px;
    text-align: center;
}

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl
    dt {
        margin-left: 5px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-rsjcxxtjb
    dt {
        background: url("images/masterimg.png") no-repeat -7px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-cbzxxzbl
    dt {
        background: url("images/masterimg.png") no-repeat -90px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-mrrjjbqdtjb
    dt {
        background: url("images/masterimg.png") no-repeat -173px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-rmkctjb
    dt {
        background: url("images/masterimg.png") no-repeat -254px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-ygnlfbb
    dt {
        background: url("images/masterimg.png") no-repeat -336px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-zpfytjb
    dt {
        background: url("images/masterimg.png") no-repeat -418px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-gsfxb
    dt {
        background: url("images/masterimg.png") no-repeat -500px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-rsydtjb
    dt {
        background: url("images/masterimg.png") no-repeat -582px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-myrjxzcbtjb
    dt {
        background: url("images/masterimg.png") no-repeat -664px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-yggnfbb
    dt {
        background: url("images/masterimg.png") no-repeat -746px -101px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-bmkgtstjfxb
    dt {
        background: url("images/masterimg.png") no-repeat -7px -183px;
    }

    .body-index-content
    .body-index-left-mainblock.hbm-reports
    .body-index-mainblock-innercontent
    dl.body-index-rpt-bmrylzlv
    dt {
        background: url("images/masterimg.png") no-repeat -90px -183px;
    }

.cont .body .body-index .body-index-content .body-index-content-right {
    width: 40%;
}

    .cont
    .body
    .body-index
    .body-index-content
    .body-index-content-right
    .body-index-right-block {
        margin-top: 10px;
        margin-right: 10px !important;
    }

        .cont
        .body
        .body-index
        .body-index-content
        .body-index-content-right
        .body-index-right-block.hbm-wfmanage {
            /*display:none;*/
        }

        .cont
        .body
        .body-index
        .body-index-content
        .body-index-content-right
        .body-index-right-block.hbm-mymessage {
            /*display:none;*/
        }

        .cont
        .body
        .body-index
        .body-index-content
        .body-index-content-right
        .body-index-right-block
        .body-index-right-block-innerhead {
            height: 45px;
            line-height: 45px;
            border: 1px solid #e6e6e6;
            border-radius: 0.25rem 0.25rem 0 0;
            padding-left: 20px;
            border-top: 3px solid #e6e6e6;
        }

.body-index-right-block.hbm-notes .body-index-right-block-innerhead {
    border-top: 3px solid #00a65a !important;
}

.cont
.body
.body-index
.body-index-content
.body-index-content-right
.body-index-right-block.hbm-index-calendar {
    /*width:100%;*/
    height: 285px;
    margin-top: 10px;
}

    .cont
    .body
    .body-index
    .body-index-content
    .body-index-content-right
    .body-index-right-block.hbm-index-calendar
    .body-index-right-block-innerhead {
        border-bottom: 0;
        /*background-color: #E6E6E6;*/
        background-color: #00a559;
        border: 1px solid #00a559;
        border-top: 3px solid #00a559;
    }

.body-index-right-block .body-index-right-block-innerhead span {
    font-size: 18px;
    margin-right: 10px;
}

.cont
.body
.body-index
.body-index-content
.body-index-content-right
.body-index-right-block.hbm-index-calendar
.body-index-right-block-innercontent {
    height: 240px;
}

.cont
.body
.body-index
.body-index-content
.body-index-content-right
.body-index-right-block.hbm-notes
.body-index-right-block-innercontent {
    height: 200px;
    border: 1px solid #f0f0f0;
    border-top: 0px;
}

    .cont
    .body
    .body-index
    .body-index-content
    .body-index-content-right
    .body-index-right-block.hbm-notes
    .body-index-right-block-innercontent
    .body-hbm-notes-new {
        width: 100%;
        height: 35px;
        padding: 10px;
    }

    .cont
    .body
    .body-index
    .body-index-content
    .body-index-content-right
    .body-index-right-block.hbm-notes
    .body-index-right-block-innercontent
    .hbm-notes-newinput {
        display: inline-block;
        border: 1px solid #f0f0f0;
        width: 100%;
        border-radius: 0.25rem 0.25rem 0.25rem 0.25rem;
    }

.body-index-right-block.hbm-notes
.body-index-right-block-innercontent
.hbm-notes-newinput
input {
    width: 90%;
    /*background-color: #FF5722;*/
    border: 0;
    outline: none;
    height: 30px;
    margin-top: 2.5px;
    padding-left: 5px;
}

.body-index-right-block.hbm-notes
.body-index-right-block-innercontent
.hbm-notes-newinput
span.hbm-button {
    display: inline-block;
    float: right;
    width: 35px;
    height: 35px;
    line-height: 35px;
    /*background-color: #FFB800;*/
    border-radius: 0 0.25rem 0.25rem 0;
    cursor: pointer;
    background-color: #00a559;
    font-size: 18px;
    color: #fff;
    padding-left: 10px;
}

    .body-index-right-block.hbm-notes
    .body-index-right-block-innercontent
    .hbm-notes-newinput
    span.hbm-button:hover {
        background-color: #01c86d;
    }

.body-index-right-block.hbm-wfmanage .body-index-right-block-innercontent {
    height: 200px;
    border: 1px solid #f0f0f0;
    border-top: 0px;
}

.body-index-right-block.hbm-officeplan .body-index-right-block-innercontent {
    height: 200px;
    border: 1px solid #f0f0f0;
    border-top: 0px;
}

.body-index-right-block.hbm-mymessage {
    margin-bottom: 10px;
}

    .body-index-right-block.hbm-mymessage .body-index-right-block-innercontent {
        height: 200px;
        border: 1px solid #f0f0f0;
        border-top: 0px;
    }

/*.cont .body .body-index .body-index-cont*/
/*{*/
/*margin-left:230px;*/
/*background-color: #FD482C;*/
/*height:100%;*/
/*}*/
.cont .body .body-index .body-index-left {
    height: 100%;
    /*background-color: #5FB878;*/
    width: 60%;
    margin-right: 20px;
}

    .cont .body .body-index .body-index-left .body-index-datagztabhd {
        height: 30px;
        width: 100%;
        /*background-color: #00F7DE;*/
        border-bottom: 1px solid #6bc363;
    }

    .cont .body .body-index .body-index-left .body-index-tabtitle,
    .cont .body .body-index .body-index-left .body-index-tabul {
        display: inline-block;
        vertical-align: middle;
    }

    .cont .body .body-index .body-index-left .body-index-ntcmsgtabhd .body-more {
        float: right;
        mragin-right: 10px;
    }

    .cont .body .body-index .body-index-left .body-index-tabtitle {
        /*background-color: #FF5722;*/
        margin-left: 20px;
        font-size: 18px;
        font-weight: 500;
    }

    .cont .body .body-index .body-index-left .body-index-tabul {
        margin: 0;
        padding: 0;
    }

        .cont .body .body-index .body-index-left .body-index-tabul li {
            float: left;
            margin-left: 5px;
            cursor: pointer;
            border: 1px solid #2a82e4;
            min-width: 70px;
            text-align: center;
            broder-radius: 0.25rem 0.25rem 0 0;
            height: 23px;
            line-height: 23px;
            background-color: #2a82e4;
            margin-top: 5px;
            color: #fff;
        }

            .cont .body .body-index .body-index-left .body-index-tabul li:hover {
                background-color: #6bc363;
                border-color: #6bc363;
            }

        .cont .body .body-index .body-index-left .body-index-tabul:first-child {
            margin-left: 20px;
        }

.hbm-tabbd .hbm-tabbditem {
    display: none;
}

    .hbm-tabbd .hbm-tabbditem.hbm-cur {
        display: block;
    }

.cont .body .body-index .body-index-left .body-index-ntcmsgtabbody-item {
    display: none;
    padding-left: 10px;
    padding-top: 10px;
}

    .cont .body .body-index .body-index-left .body-index-ntcmsgtabbody-item ul {
        margin: 0;
        padding: 0;
    }

        .cont .body .body-index .body-index-left .body-index-ntcmsgtabbody-item ul li {
            width: 100%;
            height: 30px;
            border-bottom: 1px solid #f0f0f0;
        }

            .cont
            .body
            .body-index
            .body-index-left
            .body-index-ntcmsgtabbody-item
            ul
            li:nth-child(2n) {
                background-color: #f9f9f9;
            }

.cont .body .body-index .body-index-left .body-index-datagztabbody {
    height: 250px;
    padding-left: 10px;
    overflow: hidden;
}

.cont .body .body-index .body-index-left .body-index-tabul .hbm-hdcur {
    background-color: #6bc363;
    border-color: #6bc363;
}

.cont .body .body-index .body-index-left .hbm-cur {
    display: block;
    width: 100%;
    height: 100%;
    /*background-color: #FFB800;*/
}

.cont .body .body-index .item-title {
    display: inline-block;
    /*background-color: #FFFF00;*/
}

.cont .body .body-index .body-index-datagztabbody dl {
    width: 56px;
    margin-top: 25px;
    float: left;
    text-align: center;
    margin-right: 30px;
    cursor: pointer;
}

    .cont .body .body-index .body-index-datagztabbody dl dd {
        height: 46px;
        border-radius: 0.25rem;
        color: #fff;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
    }

    .cont .body .body-index .body-index-datagztabbody dl:hover * {
        color: #666;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-A dd {
        background-color: #f8b92c;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-B dd {
        background-color: #21d32a;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-C dd {
        background-color: #fe752f;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-D dd {
        background-color: #a092f1;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-E dd {
        background-color: #5dd34b;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-F dd {
        background-color: #5dd34b;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-G dd {
        background-color: #fd61ab;
    }

    .cont .body .body-index .body-index-datagztabbody dl.item-H dd {
        background-color: #dd94f0;
    }

    .cont .body .body-index .body-index-datagztabbody dl dd {
        /*background-color: #FD482C;*/
    }

/***************  bu chong **************/
.cont .body .body-index .body-index-left .body-index-ntcmsgtabhd {
    height: 30px;
    width: 100%;
    /*background-color: #00F7DE;*/
    border-bottom: 1px solid #6bc363;
}

.cont .body .body-index .body-index-right {
    height: 100%;
    /*background-color: #00FF00;*/
    width: 40%;
    overflow: auto;
}

    .cont .body .body-index .body-index-right .body-index-right-zxdt-hd {
        height: 30px;
        border-bottom: 1px solid #ffb800;
        overflow: hidden;
    }

        .cont .body .body-index .body-index-right .body-index-right-zxdt-hd .body-more {
            float: right;
            margin-right: 10px;
            /*background-color: #FD482C;*/
            height: 20px;
            line-height: 20px;
            margin-top: 5px;
        }

    .cont
    .body
    .body-index
    .body-index-right
    .body-index-right-zxdt
    .body-index-tabtitle {
        margin-left: 20px;
        font-size: 18px;
        font-weight: 500;
    }

    .cont
    .body
    .body-index
    .body-index-right
    .body-index-right-zxdt
    .body-index-tabtitle,
    .cont
    .body
    .body-index
    .body-index-right
    .body-index-right-zxdt
    .body-index-tabul {
        display: inline-block;
        vertical-align: middle;
    }

    .cont
    .body
    .body-index
    .body-index-right
    .body-index-right-zxdt
    .body-index-tabul {
        /*background-color: #FE752F;*/
        max-width: 330px;
        overflow: hidden;
        vertical-align: top;
    }

        .cont
        .body
        .body-index
        .body-index-right
        .body-index-right-zxdt
        .body-index-tabul
        li {
            float: left;
            margin-right: 10px;
            margin-left: 5px;
            cursor: pointer;
            margin-top: 1px;
            /*background-color: #FFB800;*/
        }

            .cont
            .body
            .body-index
            .body-index-right
            .body-index-right-zxdt
            .body-index-tabul
            li:hover {
                color: #fd482c;
            }

            .cont
            .body
            .body-index
            .body-index-right
            .body-index-right-zxdt
            .body-index-tabul
            li
            .hbm-tabhd-item-inner {
                color: inherit;
                font-weight: 500;
            }

            .cont
            .body
            .body-index
            .body-index-right
            .body-index-right-zxdt
            .body-index-tabul
            li.hbm-hdcur
            .hbm-tabhd-item-inner {
                position: relative;
                color: #fd482c;
            }

                .cont
                .body
                .body-index
                .body-index-right
                .body-index-right-zxdt
                .body-index-tabul
                li.hbm-hdcur
                .hbm-tabhd-item-inner:after {
                    content: "";
                    border: 3px solid;
                    border-color: transparent transparent #fd482c transparent;
                    position: absolute;
                    left: 45%;
                    bottom: 0px;
                }

.cont .body .body-index .body-index-datagztabhd ul li {
    display: none;
}

.cont .body .body-index .body-index-right .body-index-right-zxdt-bd-item {
    height: 250px;
    width: 100%;
}

    .cont
    .body
    .body-index
    .body-index-right
    .body-index-right-zxdt-bd-item
    .hbm-imgplay {
        height: 200px;
        width: 400px;
        margin: 10px auto;
    }

    .cont .body .body-index .body-index-right .body-index-right-zxdt-bd-item ul {
        margin: 0;
        padding: 0;
    }

        .cont .body .body-index .body-index-right .body-index-right-zxdt-bd-item ul li {
            width: 100%;
            height: 30px;
            border-bottom: 1px solid #f0f0f0;
        }

            .cont
            .body
            .body-index
            .body-index-right
            .body-index-right-zxdt-bd-item
            ul
            li:nth-child(2n + 1) {
                background-color: #f9f9f9;
            }

.cont .body .body-index .body-index-right .body-index-right-calendar {
    height: 320px;
    border: 0px solid #f0f0f0;
    color: #999;
    text-align: center;
    margin: 15px 0 15px 0;
    overflow: auto;
}

.cont .body .body-index .body-index-right .body-index-right-personalplan {
    height: 150px;
    border: 1px solid #f0f0f0;
    color: #999;
    text-align: center;
    margin: 15px 0 15px 0;
}

.cont .body .body-index .body-index-right .body-index-right-reports {
    height: 150px;
    border: 1px solid #f0f0f0;
    color: #999;
    text-align: center;
    margin: 15px 0 15px 0;
}

footer .hbm-copyright {
    height: 100%;
    text-align: center;
    line-height: 50px;
}

.cont .hbm-ad-docker {
    height: 227px;
    width: 72px;
    position: fixed;
    right: 192px;
    bottom: 250px;
    background-color: transparent;
    z-index: 999998;
}

    .cont .hbm-ad-docker .hbm-ad-docker-panel:last-child {
        margin-top: 10px;
    }

    .cont .hbm-ad-docker .hbm-ad-docker-item {
        width: 70px;
        height: 72px;
        text-align: center;
        padding-top: 13px;
        cursor: pointer;
    }

        .cont .hbm-ad-docker .hbm-ad-docker-item[atr="lxyw"]:hover .hbm-ad-yw {
            background: url(../imgs/yuanoaicos.png) no-repeat -69px -92px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item[atr="yjfk"]:hover .hbm-ad-fk {
            background: url(../imgs/yuanoaicos.png) no-repeat -92px -92px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item:hover {
            color: #0070ff;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item > span {
            display: block;
            height: 36px;
            line-height: 36px;
            color: inherit;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item .hbm-ad-img {
            width: 23px;
            height: 21px;
            margin: 0 auto;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item .hbm-ad-yw {
            background: url("../imgs/yuanoaicos.png") no-repeat 0 -92px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item .hbm-ad-fk {
            background: url("../imgs/yuanoaicos.png") no-repeat -24px -92px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item .hbm-ad-backtop {
            background: url("../imgs/yuanoaicos.png") no-repeat 0px -116px;
            width: 14px;
            height: 9px;
            display: inline-block;
            margin-top: 18px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item .hbm-ad-backtop-title {
            display: none;
            line-height: 25px;
        }

        .cont .hbm-ad-docker .hbm-ad-docker-item[atr="totop"]:hover {
            background-color: #88a0c7;
        }

            .cont
            .hbm-ad-docker
            .hbm-ad-docker-item[atr="totop"]:hover
            .hbm-ad-backtop-title {
                display: inline-block;
                color: #eee;
                width: 40px;
            }

            .cont .hbm-ad-docker .hbm-ad-docker-item[atr="totop"]:hover .hbm-ad-backtop {
                display: none;
            }

    .cont .hbm-ad-docker .hbm-ad-docker-panel {
        border: 1px solid #e5e5e5;
        background-color: #ecf2fc;
    }

#topanchor {
    background-color: #b7cae9;
}

.cont .hbm-ad-docker-ywlist {
    height: 403px;
    width: 160px;
    position: fixed;
    right: 31px;
    top: 580px;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 0.25rem;
    box-shadow: 0 0 6px 0 rgba(7, 0, 2, 0.05);
    padding-top: 13px;
    display: none;
    z-index: 999998;
}

    .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item {
        height: 89px;
        width: 130px;
        margin: 6px auto;
        padding: 4px;
    }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item span {
            display: inline-block;
            margin-right: 6px;
            vertical-align: middle;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-ad-docker-yw-row {
            height: 23px;
            line-heigh: 23px;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-title {
            font-size: 12px;
            font-weight: bold;
            color: #404a56;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-userico {
            width: 12px;
            height: 12px;
            background: url("../imgs/yuanoaicos.png") no-repeat -44px 0;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-username {
            font-size: 12px;
            font-weight: bold;
            color: #999999;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-tel {
            font-size: 12px;
            font-weight: 500;
            color: #999999;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-telico {
            width: 11px;
            height: 11px;
            background: url("../imgs/yuanoaicos.png") no-repeat -72px 0;
        }

        .cont .hbm-ad-docker-ywlist .hbm-ad-docker-yw-item .hbm-yw-item-msgtouserico {
            width: 16px;
            height: 14px;
            background: url("../imgs/yuanoaicos.png") no-repeat -56px 0;
            cursor: pointer;
        }
/*******************  image play *******************/
.nivoSlider {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
}

    .nivoSlider img {
        position: absolute;
        top: 0px;
        left: 0px;
        /*display: block;*/
        /*object-fit:cover;*/
        height: auto;
        max-width: 100%;
    }

    /* If an image is wrapped in a link */
    .nivoSlider a.nivo-imageLink {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        border: 0;
        padding: 0;
        margin: 0;
        z-index: 60;
        display: none;
    }

/* The slices in the Slider */
.nivo-slice {
    display: block;
    position: absolute;
    z-index: 50;
    height: 100%;
    background-color: #fff;
}

/* Caption styles */
.nivo-caption {
    position: absolute;
    left: 0px;
    bottom: 0px;
    background: transparent;
    color: #fff;
    /*opacity:0.5; !* Overridden by captionOpacity setting *!*/
    width: 530px;
    z-index: 89;
    text-shadow: none;
    /* font-family: Helvetica, Arial, sans-serif; */
    font-size: 16px;
    padding-left: 27px;
    background-color: rgba(100, 100, 100, 0.5);
}

    .nivo-caption p {
        padding: 0 5px 0 5px;
        margin: 0;
        height: 28px;
        line-height: 28px;
        opacity: 1 !important;
        color: #fff;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .nivo-caption a {
        display: inline !important;
    }

.nivo-html-caption {
    display: none;
}

/* Direction nav styles (e.g. Next & Prev) */
.nivo-directionNav a {
    position: absolute;
    top: 45%;
    z-index: 99;
    cursor: pointer;
}

.nivo-prevNav {
    left: 0px;
}

.nivo-nextNav {
    right: 0px;
}

/* Control nav styles (e.g. 1,2,3...) */
.nivo-controlNav a {
    position: relative;
    z-index: 99;
    cursor: pointer;
}

    .nivo-controlNav a.active {
        font-weight: bold;
    }

/**********xxxxxxxxxxxxx***************/

/*#dev7link {*/
/*position:absolute;*/
/*top:0;*/
/*left:50px;*/
/*background:url(images/dev7logo.png) no-repeat;*/
/*width:60px;*/
/*height:67px;*/
/*border:0;*/
/*display:block;*/
/*text-indent:-9999px;*/
/*}*/

/*#slider-wrapper {*/
.hbm-sliderwrapper {
    /*background:url(images/slider.png) no-repeat;*/
    width: 100%;
    height: 338px;
    margin: 0 auto;
}

/*#slider {*/
/*position:relative;*/
/*width:100%;*/
/*height:100%;*/
/*margin-left:190px;*/
/*background:url(images/loading.gif) no-repeat 50% 50%;*/
/*}*/
/*#slider img {*/
/*position:absolute;*/
/*top:0px;*/
/*left:0px;*/
/*display:none;*/
/*}*/
/*#slider a {*/
/*border:0;*/
/*display:block;*/
/*}*/

.nivo-controlNav {
    position: absolute;
    right: 0px;
    bottom: 0px;
    overflow: hidden;
    width: 120px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background-color: rgba(100, 100, 100, 0.5);
    padding-right: 24px;
    opacity: 1 !important;
    z-index: 999;
}

    .nivo-controlNav a {
        display: inline-block;
        width: 10px;
        height: 10px;
        line-height: 10px;
        /*background:url(images/bullets.png) no-repeat -6px -6px;*/
        text-indent: -9999px;
        border: 1px solid #ffffff;
        background-color: transparent;
        border-radius: 50%;
        margin-right: 8px;
        opacity: 1 !important;
    }

        .nivo-controlNav a.active {
            border: 1px solid #ffffff;
            background-color: #ffffff;
        }

.nivo-directionNav a {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../images/arrows.png) no-repeat;
    text-indent: -9999px;
    border: 0;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

a.nivo-nextNav {
    background-position: -30px 0;
    right: 15px;
}

a.nivo-prevNav {
    left: 15px;
}

.nivo-caption a {
    color: #efe9d1;
    text-decoration: underline;
}

/****************** image play end *****************/

footer {
    height: 50px;
    width: 100%;
    background-color: #f0f0f0;
    border-top: 1px solid #b0b0b0;
    display: none;
}

/******* user center menus start ******/
.hbm-user-center {
    width: 200px;
    display: none;
}

    .hbm-user-center .hbm-pop-menus .hbm-menu-item {
        height: 23px;
        line-height: 23px;
        cursor: pointer;
        border-bottom: 1px dashed #e9e9e9;
    }

        .hbm-user-center .hbm-pop-menus .hbm-menu-item:hover {
            background-color: #f9f9f9;
        }

        .hbm-user-center .hbm-pop-menus .hbm-menu-item .hbm-ico {
            margin-right: 5px;
        }

/******* user center menus end ********/

/*********  balloon start  ************/
.hbm-balloon {
    background: #ffffff;
    border: #f9f9f9 1px solid;
    border-radius: 3px;
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
    position: absolute;
    padding: 5px;
    z-index: 999;
    width: 140px;
}

    .hbm-balloon.hbm-pop-up {
        position: fixed;
    }

        .hbm-balloon.hbm-pop-up .hbm-pop-triangle:before {
            position: absolute;
            content: "";
            width: 10px;
            height: 10px;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            background: #ffffff;
            border-top: #f9f9f9 1px solid;
            border-left: #f9f9f9 1px solid;
            top: -6px;
            left: 50%;
            margin-left: -5px;
            z-index: 1;
        }

    .hbm-balloon.hbm-pop-down .hbm-pop-triangle:before {
        position: absolute;
        content: "";
        width: 10px;
        height: 10px;
        -webkit-transform: rotate(45deg);
        background: #f2f2f2;
        border-bottom: #bcbcbc 1px solid;
        border-right: #bcbcbc 1px solid;
        bottom: -6px;
        left: 50%;
        margin-left: -5px;
        z-index: 1;
    }

    .hbm-balloon.hbm-pop-left .hbm-pop-triangle:before {
        position: absolute;
        content: "";
        width: 10px;
        height: 10px;
        -webkit-transform: rotate(45deg);
        background: #f2f2f2;
        border-bottom: #bcbcbc 1px solid;
        border-left: #bcbcbc 1px solid;
        top: 45%;
        left: -1px;
        margin-left: -5px;
        z-index: 1;
    }

    .hbm-balloon.hbm-pop-right .hbm-pop-triangle:before {
        position: absolute;
        content: "";
        width: 10px;
        height: 10px;
        -webkit-transform: rotate(45deg);
        background: #f2f2f2;
        border-top: #bcbcbc 1px solid;
        border-right: #bcbcbc 1px solid;
        top: 45%;
        right: -6px;
        margin-left: -5px;
        z-index: 1;
    }

    .hbm-balloon .hbm-menus {
        border: 0;
    }

/*********   balloon  end  ************/

/*********** hbmmenu start ************/
#hbm-cutsom-menustrip1 {
    position: absolute;
    width: 150px;
}

#hbm-cutsom-menustrip2 {
    position: absolute;
    width: 150px;
}

.hbm-menus {
    z-index: 888;
    background-color: #fff;
    min-width: 100px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 0 1px 0 #e0e0e0;
}

    .hbm-menus.hbm-pop-menus {
        z-index: 888;
        background-color: #fff;
        min-width: 100px;
        border: 0px solid #e0e0e0;
        box-shadow: 0 0 0px 0 #e0e0e0;
    }

    .hbm-menus .hbm-menu-item {
        height: 35px;
        line-height: 35px;
        cursor: pointer;
        padding: 0px 5px 0 5px;
        font-size: 13px;
    }

        .hbm-menus .hbm-menu-item span {
            font-size: inherit;
            display: inline-block;
            line-height: 35px;
            vertical-align: middle;
        }

        .hbm-menus .hbm-menu-item:hover {
            background-color: #f9f9f9;
        }

        .hbm-menus .hbm-menu-item .hbm-ico {
            margin-right: 15px;
            display: inline-block;
            width: 16px;
            height: 16px;
        }

            .hbm-menus .hbm-menu-item .hbm-ico.hbm-item-usercenter {
                background: url("../imgs/yuanoaicos.png") no-repeat 0 -72px;
            }

            .hbm-menus .hbm-menu-item .hbm-ico.hbm-item-changepwd {
                background: url("../imgs/yuanoaicos.png") no-repeat -20px -72px;
            }

            .hbm-menus .hbm-menu-item .hbm-ico.hbm-item-logout {
                background: url("../imgs/yuanoaicos.png") no-repeat -40px -72px;
            }

/*********** hbmmenu  end  ************/

/*********** hbmwindow start **********/
.hbm-window-mask {
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.05);
    position: absolute;
    left: 0;
    top: 0;
}

.hbm-window {
    position: absolute;
    width: 800px;
    height: 450px;
    z-index: 11;
    background-color: #fff;
    border: 1px solid #dedede;
    box-shadow: 0px 0px 1px 0px #d0d0d0;
    display: none;
}

    .hbm-window .hbm-head {
        height: 28px;
        line-height: 28px;
        border: 0px;
        border-bottom: 1px solid #f0f0f0;
        cursor: move;
        overflow: hidden;
        background: #fafafa;
        border: 0px solid #bbb;
    }

        .hbm-window .hbm-head .hbm-title {
            font-weight: 700;
            font-size: 15px;
            margin-left: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: keep-all;
            color: #888;
            /*background-color: #FF5722;*/
            display: block;
            text-align: center;
        }

        .hbm-window .hbm-head .hbm-controls {
            float: right;
            height: 100%;
            width: 90px;
            /*background-color: #FE752F;*/
            text-align: right;
        }

            .hbm-window .hbm-head .hbm-controls span {
                display: inline-block;
                width: 16px;
                height: 16px;
                line-height: 16px;
                margin: 0 2px 0 2px;
                /*background-color: #bce8f1;*/
                font-size: 16px;
                padding-right: 1px;
                cursor: pointer;
            }

                .hbm-window .hbm-head .hbm-controls span:hover {
                    background-color: #187fee;
                    color: #fff;
                }

                .hbm-window .hbm-head .hbm-controls span.hbm-min {
                    display: none;
                }

    .hbm-window .hbm-body {
        /*height: 90%;*/
        overflow: auto;
        /*display: flex;
    flex-flow: column;*/
        margin-bottom: 30px;
    }

        .hbm-window .hbm-body iframe {
            width: 100%;
            height: 95%;
            outline: none;
            border: 0;
        }

    .hbm-window .hbm-footer {
        height: 30px;
        line-height: 30px;
        border-top: 1px solid #f0f0f0;
        position: absolute;
        bottom: 0;
        left: 0;
        text-align: center;
        width: 100%;
        background-color: #e9e9e9;
    }

        .hbm-window .hbm-footer button {
            border-radius: 0.25rem;
            outline: none;
            border: 1px solid #e5e5e6;
            cursor: pointer;
            margin-right: 10px;
            margin-left: 10px;
            margin-top: 2px;
            height: 23px;
            line-height: 23px;
            min-width: 80px;
            background-color: #d0d0d0;
        }

            .hbm-window .hbm-footer button:hover {
                background-color: #187fee;
                color: #fff;
                border-color: #187fee;
            }

.hbm-wmove {
    position: absolute;
    display: none;
    background-color: rgba(7, 83, 165, 0.1);
    z-index: 999;
    cursor: move;
}

/*********** hbmwindow end  ***********/

/*********** hbmwindow alert start ********/
.hbm-window-alert {
    position: fixed;
    width: 800px;
    height: 450px;
    z-index: 11;
    background-color: #fff;
    border: 1px solid #b02a37;
    box-shadow: 0px 0px 1px 0px #d0d0d0;
    display: none;
    overflow: hidden;
}

    .hbm-window-alert .hbm-head {
        height: 28px;
        line-height: 28px;
        border: 0px;
        border-bottom: 1px solid #f0f0f0;
        cursor: move;
        overflow: hidden;
        background: #b02a37;
        border: 0px solid #bbb;
    }

        .hbm-window-alert .hbm-head .hbm-title {
            font-weight: 700;
            font-size: 15px;
            margin-left: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: keep-all;
            color: #fff;
            font-weight: 700;
            /*background-color: #FF5722;*/
            display: block;
            text-align: center;
        }

        .hbm-window-alert .hbm-head .hbm-controls {
            float: right;
            height: 100%;
            /*width:30px;*/
            /*background-color: #FE752F;*/
            text-align: right;
        }

            .hbm-window-alert .hbm-head .hbm-controls span {
                display: inline-block;
                width: 16px;
                height: 16px;
                line-height: 16px;
                margin: 0 2px 0 2px;
                /*background-color: #bce8f1;*/
                font-size: 16px;
                padding-right: 1px;
                cursor: pointer;
            }

                .hbm-window-alert .hbm-head .hbm-controls span:hover {
                    background-color: #187fee;
                    color: #fff;
                }

                .hbm-window-alert .hbm-head .hbm-controls span.hbm-min {
                    display: none;
                }

    .hbm-window-alert .hbm-body {
        /*height: 90%;*/
        overflow: hidden;
        /*display: flex;
    flex-flow: column;*/
        margin-bottom: 30px;
    }

        .hbm-window-alert .hbm-body iframe {
            width: 100%;
            height: 95%;
            outline: none;
            border: 0;
        }

    .hbm-window-alert .hbm-footer {
        height: 0px;
        line-height: 0px;
        position: absolute;
        bottom: 0;
        left: 0;
        text-align: center;
        width: 100%;
        background-color: #e9e9e9;
        border-bottom: 1px solid #b02a37;
    }

        .hbm-window-alert .hbm-footer button {
            border-radius: 0.25rem;
            outline: none;
            border: 1px solid #e5e5e6;
            cursor: pointer;
            margin-right: 10px;
            margin-left: 10px;
            margin-top: 2px;
            height: 23px;
            line-height: 23px;
            min-width: 80px;
            background-color: #d0d0d0;
        }

            .hbm-window-alert .hbm-footer button:hover {
                background-color: #187fee;
                color: #fff;
                border-color: #187fee;
            }

    .hbm-window-alert .hbm-body-inner {
        margin: 20px 10px 10px 10px;
        width: 100%;
    }

        .hbm-window-alert .hbm-body-inner span {
            display: block;
            width: 100%;
            line-height: 45px;
            margin-left: 20px;
            color: #0a53be;
        }

    .hbm-window-alert .hbm-window-ico {
        float: left;
        width: 18px;
        height: 18px;
        margin-left: 5px;
        margin-top: 4px;
        color: #0a53be;
    }

    .hbm-window-alert .hbm-body-inner .hbm-body-block {
        width: 240px;
        margin: 0px auto;
    }

/*********** hbmwindow alert end **********/

/*********  gcs loading start **********/
/*自定义新loading*/
.gcs_loading {
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 9999999999;
    /* background-color: rgba(0, 0, 0, 0.3); */
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

    .gcs_loading .gcs_loading_log {
        font-size: 10px;
        text-indent: -9999em;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        /* 线性渐变，从左到右，从白色到透明，0％代表起点和100％是终点 */
        background: linear-gradient( to right, #ffffff 10%, rgba(255, 255, 255, 0) 42% );
        position: relative;
        animation: load3 1.4s infinite linear;
    }

        .gcs_loading .gcs_loading_log:before,
        .gcs_loading .gcs_loading_log:after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
        }

        .gcs_loading .gcs_loading_log:before {
            width: 50%;
            height: 50%;
            background: #fff;
            border-radius: 100% 0 0 0;
        }

        .gcs_loading .gcs_loading_log:after {
            background: #56b4ab;
            width: 75%;
            height: 75%;
            border-radius: 50%;
            margin: auto;
            bottom: 0;
            right: 0;
        }

@keyframes load3 {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/*原loading*/
/*
.gcs_loading {
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background: url("../../images/loading.gif") no-repeat;
    background-position: 50% 50%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.3);

    -webkit-animation: rotating 2s linear infinite;
    animation: rotating 2s linear infinite;
}

.gcs_loading .gcs_loading_log {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 2;
    background-color: rgba(255, 255, 255, 0.5);
    padding-top: 20%;
}

.gcs_loading .gcs_loading_log > p {
    width: 30%;
    height: 30%;
    background-color: #ffffff;
    color: #ff0000;
    margin: auto;
    padding: 10px;
} */

/*************  gcs loading end ********/

/**********  mini css attach start *****/
.hbm-index-calendar .mini-calendar-header,
.hbm-index-calendar .mini-calendar-headerInner {
    border-radius: 0 !important;
}

.hbm-index-calendar .mini-calendar {
    border-radius: 0 0 0.25rem 0.25rem !important;
    border: 1px solid #00a559;
    border-top: 0px;
}

    .hbm-index-calendar .mini-calendar > tbody tr:nth-child(2) {
        display: none;
    }

.hbm-index-calendar .mini-calendar-prev,
.hbm-index-calendar .mini-calendar-next {
    display: none;
}

.hbm-index-calendar .mini-calendar-headerInner {
    background-color: #f0f0f0;
}

/********** mini css attach end ********/

/**************  h5 upload control css start **************/
.hbm-uploadlist {
    width: 498px;
    margin: 0 auto;
}

    .hbm-uploadlist .mini-grid-rows {
        min-height: 180px;
    }

    .hbm-uploadlist progress {
        -webkit-appearance: none;
    }

    .hbm-uploadlist ::-webkit-progress-inner-element {
    }

    .hbm-uploadlist ::-webkit-progress-bar {
        border: 1px solid #e0e0e0;
    }

    .hbm-uploadlist ::-webkit-progress-value {
        background: #8fd6e1;
    }

    .hbm-uploadlist ::-moz-progress-bar {
        background: #e0e0e0;
    }

    .hbm-uploadlist ::-ms-fill {
        background: #34538b;
    }

    .hbm-uploadlist .hbm-h5file {
        display: none;
    }

    .hbm-uploadlist .hbm-controlpanel {
        height: 60px;
    }

        .hbm-uploadlist .hbm-controlpanel .hbm-upload-infos {
            height: 30px;
            background-color: #e0e0e0;
        }

        .hbm-uploadlist .hbm-controlpanel .hbm-filecounts {
            float: left;
            width: 120px;
            padding-left: 30px;
        }

            .hbm-uploadlist .hbm-controlpanel .hbm-filecounts > span {
                margin-right: 5px;
            }

        .hbm-uploadlist .hbm-controlpanel .hbm-upload-buttons {
            height: 30px;
            background-color: #e9e9e9;
            line-height: 30px;
            text-align: center;
        }

            .hbm-uploadlist .hbm-controlpanel .hbm-upload-buttons input {
                margin-right: 10px;
                width: 80px;
                letter-spacing: 5px;
                text-align: center;
                outline: none;
                border: 1px solid #d0d0d0;
                padding: 2px;
                cursor: pointer;
                border-radius: 0.25rem;
            }

                .hbm-uploadlist .hbm-controlpanel .hbm-upload-buttons input:hover {
                    background-color: #f6f6f6;
                }

        .hbm-uploadlist .hbm-controlpanel .hbm-fileuploadprocess {
            float: right;
            margin-right: 30px;
        }

            .hbm-uploadlist .hbm-controlpanel .hbm-fileuploadprocess > span {
                margin-right: 5px;
            }

            .hbm-uploadlist .hbm-controlpanel .hbm-fileuploadprocess progress {
                width: 120px;
            }

    .hbm-uploadlist .hbm-upload-btndrop {
        color: #ce1212;
        cursor: pointer;
    }

        .hbm-uploadlist .hbm-upload-btndrop:hover {
            color: #ff0000;
        }

.gcs_uploadmsk {
    background-color: transparent;
    width: 500px;
    height: 100%;
    z-index: 999;
    /*min-width:500px;*/
    /*min-height:500px;*/
    position: absolute;
    left: 0px;
    top: 0px;
}

    .gcs_uploadmsk .gcs_upload_huge_info {
        display: block;
        position: absolute;
        left: 90px;
        bottom: 2px;
        color: #b02a37;
        display: none;
        font-size: 15px;
        font-weight: 700;
        background-color: transparent;
    }

/*************** h5 upload control css end ****************/

/***************************  zxzx   css  start  ***************************/
.hbm-zxzx-sec {
    width: 100%;
}

    .hbm-zxzx-sec.hbm-zxzx-sec1 {
        height: 338px;
    }

    .hbm-zxzx-sec.hbm-zxzx-sec2 {
        height: 300px;
        margin-top: 15px;
    }

    .hbm-zxzx-sec.hbm-zxzx-sec3 {
        height: 300px;
        margin-top: 14px;
    }

    .hbm-zxzx-sec.hbm-zxzx-sec4 {
        height: 318px;
        margin-top: 14px;
    }

    .hbm-zxzx-sec.hbm-zxzx-sec5 {
        height: 512px;
        margin-top: 14px;
    }

.hbm-zxzx-row {
    width: 100%;
    display: flex;
    height: 100%;
}

    .hbm-zxzx-row .hbm-zxzx-cell {
        background-color: #ffffff;
        box-shadow: 0 0 6px 0 rgba(7, 0, 2, 0.05);
        height: 100%;
        margin-left: 14px;
        border-radius: 0.25rem;
    }

        .hbm-zxzx-row .hbm-zxzx-cell:first-child {
            margin-left: 0px;
        }

.hbm-zxzx-body-leftbar1 {
    float: left;
    width: 1178px;
    margin-bottom: 13px;
}

.hbm-zxzx-body-rightbar1 {
    float: right;
    width: 190px;
    height: 100%;
}

    .hbm-zxzx-body-rightbar1 .hbm-zxzx-body-rightbaritem {
        margin-top: 14px;
    }

        .hbm-zxzx-body-rightbar1 .hbm-zxzx-body-rightbaritem:first-child {
            margin-top: 0px;
        }

/*****  集团要闻  时政新闻   ******/
.gcs_tab_jtywszxw {
    width: 514px;
    height: 100%;
}

    .gcs_tab_jtywszxw > .gcs_tabhd {
        background-color: #fff;
        height: 52px;
        border-bottom: 1px solid #eaeaea;
        width: 100%;
        position: relative;
    }

        .gcs_tab_jtywszxw > .gcs_tabhd ul {
            list-style: none;
            margin: 0;
            padding: 0;
            height: 52px;
        }

            .gcs_tab_jtywszxw > .gcs_tabhd ul li {
                text-align: center;
                width: 81px;
                height: 50px;
                line-height: 50px;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                background-color: transparent;
                float: left;
                margin-left: 10px;
                margin-top: 2px;
                font-size: 13px;
                margin-left: 23px;
            }

                .gcs_tab_jtywszxw > .gcs_tabhd ul li.gcs_cur {
                    border-bottom-color: #0d5ffe;
                }

    .gcs_tab_jtywszxw > .gcs_tabcont {
        padding: 10px 10px 10px 10px;
    }

    .gcs_tab_jtywszxw .gcs_tabhd span {
        color: #ff6666;
        font-size: 10px;
        /* font-family: MStiffHei PRC; */
        margin-left: 100px;
        display: block;
        position: absolute;
        bottom: 15px;
        left: 6px;
    }

        .gcs_tab_jtywszxw .gcs_tabhd span b,
        .gcs_tab_jtywszxw .gcs_tabhd span b i {
            color: inherit;
            font-size: inherit;
            /* font-family: inherit; */
        }

    .gcs_tab_jtywszxw .gcs_tabhd_item[atr="jtyw"] {
        background: url("../imgs/zxzx/jtyw.png") center center no-repeat;
    }

        .gcs_tab_jtywszxw .gcs_tabhd_item[atr="jtyw"].gcs_cur {
            background: url("../imgs/zxzx/jtyw_a.png") center center no-repeat;
        }

    .gcs_tab_jtywszxw .gcs_tabhd_item[atr="szxw"] {
        background: url("../imgs/zxzx/szxw.png") center center no-repeat;
    }

        .gcs_tab_jtywszxw .gcs_tabhd_item[atr="szxw"].gcs_cur {
            background: url("../imgs/zxzx/szxw_a.png") center center no-repeat;
        }

    .gcs_tab_jtywszxw .hbm-moreico {
        top: 20px;
        right: 20px;
        position: absolute;
        font-size: 13px;
        color: #afafaf;
        cursor: pointer;
    }

        .gcs_tab_jtywszxw .hbm-moreico:hover {
            color: #363636;
        }

/**********************************/

/*****  院发文  部门发文 人员任免   ******/
.gcs_tab_yfwbmfwryrm {
    width: 383px;
    height: 100%;
}

    .gcs_tab_yfwbmfwryrm > .gcs_tabhd {
        background-color: #fff;
        height: 40px;
        border-bottom: 1px solid #eaeaea;
        width: 100%;
        position: relative;
    }

        .gcs_tab_yfwbmfwryrm > .gcs_tabhd ul {
            list-style: none;
            margin: 0;
            padding: 0;
            height: 40px;
        }

            .gcs_tab_yfwbmfwryrm > .gcs_tabhd ul li {
                text-align: center;
                height: 38px;
                line-height: 38px;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                background-color: transparent;
                float: left;
                margin-left: 10px;
                margin-top: 2px;
                font-size: 13px;
                margin-left: 23px;
                font-size: 16px;
                /* font-family: Microsoft YaHei; */
                font-weight: bold;
            }

                .gcs_tab_yfwbmfwryrm > .gcs_tabhd ul li.gcs_cur {
                    border-bottom-color: #0d5ffe;
                    color: #0070ff;
                }

    .gcs_tab_yfwbmfwryrm > .gcs_tabcont {
        padding: 10px 10px 10px 10px;
    }

    .gcs_tab_yfwbmfwryrm .hbm-moreico {
        top: 14px;
        right: 19px;
        position: absolute;
        font-size: 13px;
        color: #afafaf;
        cursor: pointer;
    }

        .gcs_tab_yfwbmfwryrm .hbm-moreico:hover {
            color: #363636;
        }

/**********************************/

/*****  single tab   ******/
.gcs_tab_zxzx_single {
    width: 100%;
    height: 100%;
}

    .gcs_tab_zxzx_single > .gcs_tabhd {
        background-color: #fff;
        height: 40px;
        border-bottom: 1px solid #eaeaea;
        width: 100%;
        position: relative;
    }

    .gcs_tab_zxzx_single > .gcs_tabbd[grp="zxzx_wlaqzthd"] {
        /* border-left: 1px solid #eaeaea; */
        /* border-bottom: 1px solid #eaeaea; */
    }

    .gcs_tab_zxzx_single > .gcs_tabhd ul {
        list-style: none;
        margin: 0;
        padding: 0;
        height: 40px;
    }

        .gcs_tab_zxzx_single > .gcs_tabhd ul li {
            text-align: center;
            height: 38px;
            line-height: 38px;
            border: 0px solid transparent;
            cursor: pointer;
            float: left;
            margin-left: 10px;
            /* margin-top: 2px; */
            font-size: 13px;
            margin-left: 23px;
            font-size: 16px;
            /* font-family: Microsoft YaHei; */
            font-weight: bold;
            color: #404a56;
            position: relative;
        }

            .gcs_tab_zxzx_single > .gcs_tabhd ul li.gcs_cur .gcs_cur_inner {
                position: absolute;
                width: 68px;
                height: 4px;
                background: #0d5ffe;
                box-shadow: 0px 2px 3px 0px rgba(0, 112, 255, 0.2);
                border-radius: 0px 0px 4px 3px;
                top: 36px;
                left: 0px;
            }

    .gcs_tab_zxzx_single > .gcs_tabcont {
        padding: 10px 10px 10px 10px;
    }

    .gcs_tab_zxzx_single .hbm-moreico {
        top: 14px;
        right: 19px;
        position: absolute;
        font-size: 13px;
        color: #afafaf;
        cursor: pointer;
    }

        .gcs_tab_zxzx_single .hbm-moreico:hover {
            color: #363636;
        }

/**********************************/

/*****  kc childs tab   ******/
.gcs_tab_kjcxyd_childs {
    width: 100%;
    height: 100%;
}

    .gcs_tab_kjcxyd_childs > .gcs_tabhd {
        background-color: #fff;
        height: 471px;
        border-right: 1px solid #eaeaea;
        width: 240px;
        position: relative;
        float: left;
    }

        .gcs_tab_kjcxyd_childs > .gcs_tabhd ul {
            margin: 0;
            padding: 0;
            height: 40px;
        }

            .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li {
                text-align: left;
                height: 50px;
                line-height: 38px;
                border: 0px solid transparent;
                cursor: pointer;
                margin-left: 10px;
                margin-top: 2px;
                font-size: 16px;
                margin-left: 20px;
                /* font-family: Microsoft YaHei; */
                color: #404a56;
                position: relative;
                border: 4px solid transparent;
            }

                .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li span {
                    display: inline-block;
                    vertical-align: middle;
                }

                    .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li span:first-child {
                        margin-right: 14px;
                    }

                    .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li span.gcs_tabliico {
                        width: 6px;
                        height: 6px;
                        background: linear-gradient(220deg, #f4f7fa 0%, #c0c6df 100%);
                        border-radius: 50%;
                    }

                .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li.gcs_cur span.gcs_tabliico {
                    width: 6px;
                    height: 6px;
                    background: linear-gradient(220deg, #cfe4fe 0%, #3f93fe 100%);
                    border-radius: 50%;
                }

                .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li.gcs_cur {
                    border-right: 4px solid #0070ff;
                    font-size: 16px;
                    color: #0070ff;
                    font-weight: bold;
                }

                    .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li.gcs_cur span,
                    .gcs_tab_kjcxyd_childs > .gcs_tabhd ul li span {
                        font-size: inherit;
                        color: inherit;
                        font-weight: inherit;
                    }

    .gcs_tab_kjcxyd_childs > .gcs_tabcont {
        padding: 10px 10px 10px 10px;
    }

    .gcs_tab_kjcxyd_childs .hbm-moreico {
        top: 14px;
        right: 19px;
        position: absolute;
        font-size: 13px;
        color: #afafaf;
        cursor: pointer;
    }

        .gcs_tab_kjcxyd_childs .hbm-moreico:hover {
            color: #363636;
        }

/**********************************/

/*********** 网络安全主题活动 css *********/
.gcs_zxzx_wlaqzthd_childs_cont {
    display: flex;
    width: 100%;
    height: 100%;
    /* border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea; */
}

    .gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem {
        width: 33.33%;
        border-right: 1px solid #eaeaea;
        height: 278px;
        padding: 15px;
    }

        .gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem:nth-child(3n) {
            border-right: 0;
        }

        .gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head {
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            color: #0070ff;
            font-weight: bold;
            background-image: linear-gradient(to right, #f6faff, #cce1ff);
        }

            .gcs_zxzx_wlaqzthd_childs_cont
            .gcs_zxzx_wlaqzthd_listitem
            .gcs_zxzx_ss_head
            span {
                font-size: inherit;
                color: inherit;
                font-weight: inherit;
                display: inline-block;
                vertical-align: middle;
                margin-left: 27px;
            }

                .gcs_zxzx_wlaqzthd_childs_cont
                .gcs_zxzx_wlaqzthd_listitem
                .gcs_zxzx_ss_head
                span.gcs_zxzx_ss_head_ico {
                    margin-left: 25px;
                }

                .gcs_zxzx_wlaqzthd_childs_cont
                .gcs_zxzx_wlaqzthd_listitem
                .gcs_zxzx_ss_head
                span:gcs_zxzx_ss_head_lastimg {
                    margin-left: 40px;
                }

            .gcs_zxzx_wlaqzthd_childs_cont
            .gcs_zxzx_wlaqzthd_listitem
            .gcs_zxzx_ss_head
            .gcs_zxzx_ss_head_lastimg {
                width: 145px;
                height: 10px;
                background: url("../imgs/yuanoaicos.png") no-repeat 0 -160px;
            }

        .gcs_zxzx_wlaqzthd_childs_cont
        .gcs_zxzx_wlaqzthd_listitem
        .gcs_zxzx_wlaqzthd_zstz
        .gcs_zxzx_ss_head_ico {
            width: 24px;
            height: 25px;
            background: url("../imgs/yuanoaicos.png") no-repeat 0 -132px;
        }

        .gcs_zxzx_wlaqzthd_childs_cont
        .gcs_zxzx_wlaqzthd_listitem
        .gcs_zxzx_wlaqzthd_wkt
        .gcs_zxzx_ss_head_ico {
            width: 31px;
            height: 24px;
            background: url("../imgs/yuanoaicos.png") no-repeat -28px -132px;
        }

        .gcs_zxzx_wlaqzthd_childs_cont
        .gcs_zxzx_wlaqzthd_listitem
        .gcs_zxzx_wlaqzthd_wlaqsj
        .gcs_zxzx_ss_head_ico {
            width: 24px;
            height: 25px;
            background: url("../imgs/yuanoaicos.png") no-repeat -60px -132px;
        }
/******************************************/

.gcs_tab_ztzl .gcs_tabhd[grp="zxzx_ztzl"] {
    height: 52px;
}

    .gcs_tab_ztzl .gcs_tabhd[grp="zxzx_ztzl"] ul {
        height: 52px;
    }

        .gcs_tab_ztzl .gcs_tabhd[grp="zxzx_ztzl"] ul li {
            height: 52px;
        }

.gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] ul:before {
    content: "";
    display: table;
    clear: both;
}

.gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li {
    width: 180px;
    height: 56px;
    line-height: 56px;
    margin-top: 10px;
    text-align: left;
    font-size: 16px;
    /* font-family: Microsoft YaHei; */
    font-weight: 400;
    color: #ffffff;
    padding-left: 63px;
    margin-left: 5px;
}

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li a {
        font-size: inherit;
        /* font-family: inherit; */
        font-weight: inherit;
        color: inherit;
        text-decoration: none;
    }

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li.gcs_ztzl_zlgh {
        background: url("../imgs/zxzx/zlgh.png") no-repeat;
    }

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li.gcs_ztzl_szkmy {
        background: url("../imgs/zxzx/szkmy.png") no-repeat;
    }

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li.gcs_ztzl_szyxt {
        background: url("../imgs/zxzx/szyxt.png") no-repeat;
    }

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li.gcs_ztzl_yzxx {
        background: url("../imgs/zxzx/yzxx.png") no-repeat;
    }

    .gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp="zxzx_ztzl"] li.gcs_ztzl_jlbgt {
        background: url("../imgs/zxzx/jlbgt.png") no-repeat;
    }

.gcs_tabbd .gcs_tabitem[grp="zxzx_ksrk"] {
    padding-bottom: 24px;
}

.gcs_tabbd .gcs_tabitem div.gcs_rightlink_item {
    height: 35px;
    line-height: 35px;
    color: #3c8cf1;
    font-size: 14px;
    width: 190px;
    padding: 12px;
}

    .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a {
        width: 164px;
        height: 35px;
        line-height: 35px;
    }

        .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a div {
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: #3c8cf1;
            border-radius: 50%;
            margin-right: 4px;
            vertical-align: top;
            margin-top: 15px;
        }

        .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a,
        .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a span {
            text-decoration: none;
            color: inherit;
            font-size: inherit;
            display: inline-block;
        }

            .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a > span.gcs_title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                word-break: keep-all;
                width: 114px !important;
            }

            .gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a span:nth-child(3) {
                float: right;
            }

.gcs_newslist_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px !important;
    box-sizing: border-box;
    padding-right: 20px;
    height: 40px;
    line-height: 40px;
}

.toper .gcs_newslist_item {
    position: relative;
}

    .toper .gcs_newslist_item:hover::before {
        content: "";
        display: block;
        width: 4px;
        height: 40px;
        background: #0d5ffe;
        border-radius: 0px 8px 8px 0;
        position: absolute;
        left: 0;
        top: 0;
    }

.gcs_newslist_item:hover {
    background-color: #f4faff;
}

.gcs_newslist_item.gcs_cur {
    background-color: #f4faff;
}

    .gcs_newslist_item.gcs_cur::before {
        content: "";
        display: block;
        width: 4px;
        height: 40px;
        background: #0d5ffe;
        border-radius: 0px 8px 8px 0;
        position: absolute;
        left: 0;
        top: 0;
    }

.gcs_newslist_item span {
    display: inline-block;
}

    .gcs_newslist_item span.gcs_dt {
        float: right;
        color: #80848d;
    }

.gcs_newslist_item a {
    text-decoration: none;
    color: #555;
}

.gcs_newslist_item span.gcs_title {
    color: #404a56;
    font-size: 14px;
}

.gcs_newslist_jtyw .gcs_newslist_item span.gcs_title,
.gcs_newslist_szxw .gcs_newslist_item span.gcs_title {
    flex: 1;
    margin-right: 10px;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: none;
}

.gcs_newslist_yfw .gcs_newslist_item span.gcs_title,
.gcs_newslist_ryrm .gcs_newslist_item span.gcs_title,
.gcs_newslist_yntz .gcs_newslist_item span.gcs_title,
.gcs_newslist_yngg .gcs_newslist_item span.gcs_title,
.gcs_newslist_zdbd .gcs_newslist_item span.gcs_title,
.gcs_newslist_bmfw .gcs_newslist_item span.gcs_title {
    flex: 1;
    margin-right: 10px;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: none;
}

.gcs_newslist_wlaq_zstz .gcs_newslist_item span.gcs_title,
.gcs_newslist_wlaq_wkt .gcs_newslist_item span.gcs_title,
.gcs_newslist_wlaq_wlaqsj .gcs_newslist_item span.gcs_title {
    width: 255px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-decoration: none;
}

.gcs_tab_kjcxyd_childs .gcs_tabbd {
    /* margin-left: 240px; */
}

.gcs_tab_kjcxyd_childs .gcs_tabhd_item span:last-child {
    width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px !important;
}

.gcs_tab_kjcxyd_childs .gcs_newslist span.gcs_title {
    width: 780px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
/***************************  zxzx   css  end    ***************************/

/***************************  attach control css start  **************************/
.gcs_fbgs_table {
    border: 0px solid #e0e0e0;
    /*max-width:1000px;*/
    /*width: 100%;*/
    overflow: auto;
    /* width: 741px; */
    margin: 0 auto;
}

    .gcs_fbgs_table .gcs_head {
        height: 40px;
        background-color: #f4faff;
        overflow: hidden;
    }

        .gcs_fbgs_table .gcs_head .gcs_head_inner {
            height: 40px;
        }

            .gcs_fbgs_table .gcs_head .gcs_head_inner table {
                border: 0;
            }

                .gcs_fbgs_table .gcs_head .gcs_head_inner table th {
                    /*background-color: #f68322;*/
                    height: 40px;
                    line-height: 40px;
                    padding-left: 5px;
                    padding-right: 5px;
                    text-align: center;
                }

                    .gcs_fbgs_table .gcs_head .gcs_head_inner table th span {
                        display: inline-block;
                        /*background-color: #f67a7a;*/
                        margin-right: 4px;
                    }

                    .gcs_fbgs_table .gcs_head .gcs_head_inner table th .gcs_order {
                        height: 15px;
                        width: 10px;
                        position: relative;
                        /*background-color: #f67a7a;*/
                        vertical-align: top;
                        margin-top: 3px;
                    }

                        .gcs_fbgs_table .gcs_head .gcs_head_inner table th .gcs_order i {
                            position: absolute;
                            cursor: pointer;
                        }

                            .gcs_fbgs_table .gcs_head .gcs_head_inner table th .gcs_order i.gcs_up.gcs_cur {
                                border-bottom-color: #444;
                            }

                            .gcs_fbgs_table
                            .gcs_head
                            .gcs_head_inner
                            table
                            th
                            .gcs_order
                            i.gcs_down.gcs_cur {
                                border-top-color: #444;
                            }

                            .gcs_fbgs_table .gcs_head .gcs_head_inner table th .gcs_order i.gcs_up {
                                border: 4px solid;
                                border-color: transparent transparent #999 transparent;
                                /*background-color: #19A15F;*/
                                left: 0px;
                                top: 0px;
                            }

                            .gcs_fbgs_table .gcs_head .gcs_head_inner table th .gcs_order i.gcs_down {
                                border: 4px solid;
                                border-color: #999 transparent transparent transparent;
                                left: 0px;
                                top: 11px;
                            }

    .gcs_fbgs_table .gcs_body {
        width: 100%;
        /*background-color: #a14c09;*/
        /* height: 220px; */
    }

        .gcs_fbgs_table .gcs_body .gcs_body_inner {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

            .gcs_fbgs_table .gcs_body .gcs_body_inner table {
                border: 0;
            }

                .gcs_fbgs_table .gcs_body .gcs_body_inner table tr {
                    height: 40px;
                }

                    .gcs_fbgs_table .gcs_body .gcs_body_inner table tr:hover {
                        background-color: #f4faff !important;
                        color: #fff;
                    }

                    .gcs_fbgs_table .gcs_body .gcs_body_inner table tr td {
                        border-right: 0px solid transparent;
                        border-bottom: 1px solid #f0f0f0;
                    }

                        .gcs_fbgs_table
                        .gcs_body
                        .gcs_body_inner
                        table
                        tr
                        td[fld="SubcontractPlanName"]
                        .gcs_tablecell {
                            cursor: pointer;
                        }

                        .gcs_fbgs_table .gcs_body .gcs_body_inner table tr td .gcs_tablecell {
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            padding-left: 10px;
                        }

                            .gcs_fbgs_table .gcs_body .gcs_body_inner table tr td .gcs_tablecell input {
                                outline: none;
                                border: 1px solid #f0f0f0;
                            }

                        .gcs_fbgs_table
                        .gcs_body
                        .gcs_body_inner
                        table
                        tr
                        td[fld="filename"] > div > span {
                            text-decoration: underline;
                            cursor: pointer;
                        }

                    .gcs_fbgs_table .gcs_body .gcs_body_inner table tr td {
                        padding-left: 5px;
                        padding-right: 5px;
                    }

    .gcs_fbgs_table .gcs_footer {
        height: 30px;
        line-height: 30px;
        border-top: 1px solid #dedede;
    }

    .gcs_fbgs_table td[fld="op"] .gcs_btn {
        margin-right: 5px;
        background-color: #fbeed5;
        padding: 2px;
        cursor: pointer;
        border-radius: 0.25rem;
    }

        .gcs_fbgs_table td[fld="op"] .gcs_btn:hover {
            background-color: #0087ee;
            color: #fff;
        }

    .gcs_fbgs_table .gcs_body .gcs_body_inner table tr.gcs_cur {
        background-color: #ffffff;
    }
/***************************  attach control css  end  ***************************/

/***************************   xmzx  css  start   ****************************/
.hbm-xmzx {
    width: 1380px;
    /*height: 1233px;*/
    margin: 13px auto;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px rgba(7, 0, 2, 0.05);
}

.gcs_tab_xmzx {
    width: 100%;
    height: 100%;
}

    .gcs_tab_xmzx > .gcs_tabhd {
        background-color: #fff;
        height: 52px;
        border-bottom: 1px solid #eaeaea;
        width: 100%;
        position: relative;
    }

        .gcs_tab_xmzx > .gcs_tabhd ul {
            list-style: none;
            margin: 0 auto;
            padding: 0;
            height: 52px;
            width: 270px !important;
        }

            .gcs_tab_xmzx > .gcs_tabhd ul li {
                text-align: center;
                height: 50px;
                line-height: 50px;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                background-color: transparent;
                float: left;
                margin-left: 10px;
                margin-top: 2px;
                font-size: 16px;
                margin-left: 21px;
                font-weight: bold;
            }

                .gcs_tab_xmzx > .gcs_tabhd ul li.gcs_cur {
                    border-bottom-color: #0d5ffe;
                    color: #0d5ffe;
                }

    .gcs_tab_xmzx > .gcs_tabcont {
        padding: 10px 10px 10px 10px;
    }

    .gcs_tab_xmzx .gcs_tabhd span {
        color: #ff6666;
        font-size: 10px;
        /* font-family: MStiffHei PRC; */
        margin-left: 100px;
        display: block;
        position: absolute;
        bottom: 15px;
        left: 6px;
    }

        .gcs_tab_xmzx .gcs_tabhd span b,
        .gcs_tab_jtywszxw .gcs_tabhd span b i {
            color: inherit;
            font-size: inherit;
            /* font-family: inherit; */
        }

    .gcs_tab_xmzx .gcs_tabhd_item[atr="jtyw"] {
        background: url("../imgs/zxzx/jtyw.png") center center no-repeat;
    }

        .gcs_tab_xmzx .gcs_tabhd_item[atr="jtyw"].gcs_cur {
            background: url("../imgs/zxzx/jtyw_a.png") center center no-repeat;
        }

    .gcs_tab_xmzx .gcs_tabhd_item[atr="szxw"] {
        background: url("../imgs/zxzx/szxw.png") center center no-repeat;
    }

        .gcs_tab_xmzx .gcs_tabhd_item[atr="szxw"].gcs_cur {
            background: url("../imgs/zxzx/szxw_a.png") center center no-repeat;
        }

    .gcs_tab_xmzx .gcs_tabbd .gcs_tabitem {
        height: 100%;
    }

    .gcs_tab_xmzx .hbm-moreico {
        top: 20px;
        right: 33px;
        position: absolute;
        font-size: 13px;
        color: #afafaf;
        cursor: pointer;
    }

        .gcs_tab_xmzx .hbm-moreico:hover {
            color: #363636;
        }

.gcs_xmzx_graphic {
    padding: 30px;
    height: 100%;
}

    .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk {
        height: 100%;
        min-height: 600px;
    }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevelfwk_item {
            /*border-right: 1px solid #0070FF;*/
            /*height: 100%;*/
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_head_row {
            display: flex;
            height: 60px;
        }

            .gcs_xmzx_graphic
            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_xmlevel_head_row
            .gcs_xmzx_xmlevel_head_cell {
                height: 60px;
                line-height: 60px;
                text-align: center;
                font-size: 18px;
                color: #0975ff;
                font-weight: bold;
                border-bottom: 1px solid #0070ff;
            }

            .gcs_xmzx_graphic
            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_xmlevel_head_row
            .gcs_xmzx_xmlevel_headcell_a:nth-child(1) {
                width: 120px;
            }

            .gcs_xmzx_graphic
            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_xmlevel_head_row
            .gcs_xmzx_xmlevel_headcell_a:nth-child(2) {
                width: 260px;
            }

            .gcs_xmzx_graphic
            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_xmlevel_head_row
            .gcs_xmzx_xmlevel_headcell_a:nth-child(3) {
                width: 220px;
            }

            .gcs_xmzx_graphic
            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_xmlevel_head_row
            .gcs_xmzx_xmlevel_headcell_b {
                width: 758px;
            }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_cell_a {
            width: 260px;
            border-right: 1px solid #0070ff;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_cell_a1 {
            width: 120px;
            border-right: 1px solid #0070ff;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_cell_p {
            width: 940px;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_cell_b {
            width: 758px;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel2_row {
            border-bottom: 1px solid #0070ff;
            display: flex;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel3_row {
            display: flex;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_node_l3 .gcs_xmzx_node_name {
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: keep-all;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel2_row:last-child {
            border-bottom: 0px;
            border-bottom: 0px;
        }

        .gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel1_row {
            display: flex;
            border-bottom: 1px solid #0070ff;
            min-height: 100px;
        }

.gcs_xmzx_cell {
    min-height: 98px;
}

.gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel1_row:last-child {
    border-bottom: 0px solid #0070ff;
}

.gcs_xmzx_node_l1 {
    width: 64px;
    height: 160px;
    background: linear-gradient(90deg, #4898ff 0%, #0975ff 100%);
    border-radius: 30px;
    /*margin-left: 45px;*/
    line-height: 64px;
    -webkit-writing-mode: vertical-rl;
    writing-mode: vertical-rl;
}

    .gcs_xmzx_node_l1 .gcs_xmzx_node_ico {
        margin-right: 3px !important;
    }

.gcs_xmzx_node_l2 {
    width: 200px;
    height: 36px;
    background: linear-gradient(90deg, #4898ff 0%, #0975ff 100%);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0px 3px 6px 0px rgba(0, 112, 255, 0.1);
    border-radius: 18px;
    line-height: 32px;
}

.gcs_xmzx_node_l3 {
    width: 160px;
    height: 36px;
    background: linear-gradient(90deg, #6dabff 0%, #5590ff 100%);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0px 3px 6px 0px rgba(0, 112, 255, 0.1);
    border-radius: 18px;
    line-height: 36px;
}

.gcs_xmzx_node.gcs_nonechld {
    background: linear-gradient(90deg, #c3ddff 0%, #9ec7ff 100%);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0px 3px 6px 0px rgba(0, 112, 255, 0.1);
}

.gcs_xmzx_node {
    text-align: center;
    cursor: pointer;
}

    .gcs_xmzx_node span {
        display: inline-block;
        color: #ffffff;
        vertical-align: top;
        /*overflow: hidden;*/
    }

.gcs_xmzx_node_l1 span {
    font-size: 16px;
    font-weight: 400;
}

.gcs_xmzx_node_l2 span {
    font-size: 16px;
    font-weight: 400;
}

.gcs_xmzx_node_l3 span {
    font-size: 16px;
    font-weight: 400;
}

.gcs_xmzx_xmlevelfwk .gcs_xmzx_module {
    background: #ffffff;
    border: 1px solid #c9e2ff;
    box-shadow: 0px 3px 6px 0px rgba(0, 112, 255, 0.1);
    border-radius: 3px;
    width: 646px;
    padding: 19px 32px;
    margin-bottom: 12px;
}

.gcs_xmzx_xmlevel2_row
.gcs_xmzx_xmlevel3_row:first-child
.gcs_xmzx_xmlevel4_cell
.gcs_xmzx_module {
    margin-top: 31px;
}

.gcs_xmzx_xmlevel2_row
.gcs_xmzx_xmlevel3_row:last-child
.gcs_xmzx_xmlevel4_cell
.gcs_xmzx_module {
    margin-bottom: 28px;
}

.gcs_xmzx_xmlevel4_cell .gcs_xmzx_module {
    margin-left: 62px;
}

.gcs_xmzx_xmlevel2_row .gcs_xmzx_xmlevel3_row:first-child .gcs_xmzx_node {
    margin-top: 31px;
}

.gcs_xmzx_xmlevel2_row .gcs_xmzx_xmlevel3_row:last-child .gcs_xmzx_node {
    margin-bottom: 28px;
}

.gcs_xmzx_xmlevel1_row .gcs_xmzx_xmlevel2_row .gcs_xmzx_node {
    margin-top: 31px;
    margin-bottom: 28px;
}

.gcs_xmzx_xmlevel1_row
.gcs_xmzx_xmlevel2_row
.gcs_xmzx_node_l2
.gcs_xmzx_node_name {
    width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
    text-align: center;
    height: 32px;
    line-height: 32px;
}

.gcs_xmzx_xmlevel1_row .gcs_xmzx_node {
    margin-top: 31px;
    margin-bottom: 28px;
}

.gcs_xmzx_xmlevelfwk .gcs_xmzx_module .gcs_xmzx_module_singleline {
    display: flex;
    margin-top: 12px;
}

    .gcs_xmzx_xmlevelfwk .gcs_xmzx_module .gcs_xmzx_module_singleline:first-child {
        margin-top: 0px;
    }

.gcs_xmzx_xmlevel1_cell .gcs_xmzx_node,
.gcs_xmzx_xmlevel2_cell .gcs_xmzx_node,
.gcs_xmzx_xmlevel3_cell .gcs_xmzx_node,
.gcs_xmzx_xmlevel4_cell .gcs_xmzx_module {
    margin: 0 auto;
}

.gcs_xmzx_xmlevelfwk .gcs_xmzx_module .gcs_xmzx_module_singleline_item {
    width: 160px;
    cursor: pointer;
    color: #0070ff;
    margin-right: 50px;
}

    .gcs_xmzx_xmlevelfwk
    .gcs_xmzx_module
    .gcs_xmzx_module_singleline_item.gcs_search_rs,
    .gcs_xmzx_xmlevelfwk .gcs_xmzx_node.gcs_search_rs {
        background-color: rgba(255, 255, 0, 0.3) !important;
        background: linear-gradient( 90deg, rgba(255, 255, 0, 0.3) 0%, rgba(255, 255, 0, 0.3) 100% ) !important;
    }

    .gcs_xmzx_xmlevelfwk
    .gcs_xmzx_module
    .gcs_xmzx_module_singleline_item.gcs_search_focus,
    .gcs_xmzx_xmlevelfwk .gcs_xmzx_node.gcs_search_focus {
        background-color: rgba(255, 255, 0, 0.9) !important;
        background: linear-gradient( 90deg, rgba(255, 255, 0, 0.9) 0%, rgba(255, 255, 0, 0.9) 100% ) !important;
    }

        .gcs_xmzx_xmlevelfwk .gcs_xmzx_node.gcs_search_focus *,
        .gcs_xmzx_xmlevelfwk .gcs_xmzx_node.gcs_search_rs * {
            color: #444 !important;
        }

    .gcs_xmzx_xmlevelfwk
    .gcs_xmzx_module
    .gcs_xmzx_module_singleline_item:last-child {
        margin-right: 0px;
    }

    .gcs_xmzx_xmlevelfwk .gcs_xmzx_module .gcs_xmzx_module_singleline_item span {
        color: inherit;
        display: inline-block;
        vertical-align: middle;
    }

        .gcs_xmzx_xmlevelfwk
        .gcs_xmzx_module
        .gcs_xmzx_module_singleline_item
        span:first-child {
            width: 130px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: keep-all;
        }

            .gcs_xmzx_xmlevelfwk
            .gcs_xmzx_module
            .gcs_xmzx_module_singleline_item
            span:first-child:before {
                content: ".";
                display: inline-block;
                height: 14px;
                line-height: 9px;
                vertical-align: top;
                margin-right: 10px;
            }

        .gcs_xmzx_xmlevelfwk
        .gcs_xmzx_module
        .gcs_xmzx_module_singleline_item
        span:last-child {
            margin-left: 0px;
        }

    .gcs_xmzx_xmlevelfwk
    .gcs_xmzx_module
    .gcs_xmzx_module_singleline_item.gcs_xmzx_module_urlnone
    span {
        color: #999;
        cursor: default;
    }

.gcs_xmzx_node span:first-child {
    margin-right: 11px;
}

.gcs_xmzx_node .gcs_xmzx_node_ico {
    width: 27px;
    height: 27px;
    background: url("../images/xmzx/xmzx_dft.png") no-repeat;
    vertical-align: middle;
}

.gcs_xmzx_node_l2 .gcs_xmzx_node_ico {
    width: 20px;
    height: 20px;
    background: url("../images/xmzx/xmzx_dft.png") no-repeat;
    vertical-align: top;
    margin-top: 6px;
}

.gcs_xmzx_node_l2 span:first-child {
    margin-right: 4px;
}

.gcs_xmzx_node .gcs_xmzx_node_ico img {
    width: 27px;
    height: 27px;
}

.gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevelfwk_item:last-child {
    border-right: 0px;
}

.gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk {
    border: 1px solid #0070ff;
    position: relative;
}

.gcs_xmzx_xmlevel_body_ct {
    z-index: 999;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 60px;
    left: 0px;
}

.gcs_xmzx_graphic .gcs_xmzx_xmlevelfwk svg {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    display: block;
}

.gcs_hchld_nurl {
    cursor: default;
}
/***************************   xmzx  css  end  *******************************/
.hbm-filepanel {
    min-height: 27px;
    line-height: 27px;
    text-align: left !important;
}

    .hbm-filepanel span,
    .hbm-filepanel a {
        display: inline-block;
        vertical-align: middle;
        text-indent: 0em;
    }

#divContent .hbm-file-sc,
#detail .hbm-file-sc {
    display: inline-block;
}

#divContent > img {
    margin: 0 auto;
    display: block !important;
}

.hbm-file-sc,
.hbm-miniupload-file-sc {
    width: 16px;
    height: 16px;
    background: url("../images/sc.png") no-repeat;
    background-size: 16px 16px;
    cursor: pointer;
    margin-right: 10px;
}

    .hbm-file-sc.hbm-file-sced {
        width: 16px;
        height: 16px;
        background: url("../images/sc_a.png") no-repeat;
        background-size: 16px 16px;
        cursor: pointer;
    }

    .hbm-miniupload-file-sc.hbm-miniupload-file-sced {
        width: 16px;
        height: 16px;
        background: url("../images/sc_a.png") no-repeat;
        background-size: 16px 16px;
        cursor: pointer;
    }

.hbm-singlefile-sc {
    width: 25px;
    height: 25px;
    background: url("../images/sc.png") no-repeat center center;
    background-size: 16px 16px;
    cursor: pointer;
    margin-right: 10px;
    display: inline-block;
    vertical-align: top;
}

.hbm-singlefile-sc.hbm-singlefile-sced {
    width: 25px;
    height: 25px;
    background: url("../images/sc_a.png") no-repeat center center;
    background-size: 16px 16px;
    cursor: pointer;
}

.mini-multifile .multiFileContent td input {
    vertical-align: middle;
}

.mini-multifile .multiFileContent td span {
    display: inline-block;
    vertical-align: middle;
    height: 22px;
    line-height: 22px;
}

    .mini-multifile .multiFileContent td span.hbm-miniupload-file-sc {
        margin-left: 32px;
        margin-top: 4px;
    }
/***************************  attach others css start   ******************************/
.page-footer {
    box-shadow: 0 0 6px 0 rgba(7, 0, 2, 0.05);
    border-top: 1px solid #f0f0f0;
}
/***************************  attach others css  end  ********************************/

/*--------- 帮助文档 - 侧边栏 start ---------*/
#help_slidbar {
    position: fixed;
    right: 10px;
    bottom: 337px;
    width: 72px;
    height: 72px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    background: #fff;
}

    #help_slidbar .text {
        margin-top: auto;
        margin-bottom: 10px;
    }

    #help_slidbar .icon {
        width: 26px;
        height: 26px;
        margin-bottom: 4px;
        background: url("../images/help_icon.png") no-repeat center center;
        background-size: cover;
        margin-bottom: auto;
        margin-top: 10px;
        transition: all 0.4s;
    }

    #help_slidbar:hover .icon {
        background: url("../images/help_icon_active.png") no-repeat center center;
        background-size: cover;
        background-position: 0 -2px;
        /* animation: bounce 1s ease-in-out; */
    }

    #help_slidbar:hover .text {
        color: #0070ff;
    }
/*--------- 帮助文档 end ---------*/

/*--------- 温馨提示 start ---------*/
.wxts_dialog {
    position: fixed;
    bottom: 50%;
    right: 50%;
    margin-right: -200px;
    margin-bottom: -175px;
    z-index: 9999999;
    width: 400px;
    height: 350px;
    border-radius: 10px !important;
    transition: all 0.8s;
    overflow: hidden;
    display: none;
}

.wxts_dialog_close {
    position: fixed;
    right: 10px;
    bottom: 440px;
    width: 0px;
    height: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    overflow: hidden;
}

.wxts_dialog .uk-card-header {
    padding: 0;
    margin: 0;
    height: 50px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wxts_dialog .refresh {
    margin-left: 10px;
    cursor: pointer;
}

    .wxts_dialog .refresh:hover .el-icon-refresh {
        animation: rotate 1s linear infinite;
    }

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

.wxts_dialog .more {
    margin-right: auto;
    margin-left: 232px;
}

.wxts_dialog .close {
    margin-left: auto;
    width: 26px;
    height: 26px;
    border-radius: 30px;
    line-height: 20px;
    border: 1px solid rgb(201, 201, 201);
    font-size: 24px;
    text-align: center;
    transform: rotate(45deg);
}

.wxts_dialog .has_tip {
    position: relative;
}

.wxts_dialog .tip {
    position: absolute;
    background: #000;
    opacity: 0.5;
    color: #fff;
    width: 120px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    text-align: center;
    left: -50px;
    top: 24px;
    display: none;
    transition: all 0.3s;
    z-index: 9999998;
}

    .wxts_dialog .tip::after {
        content: "";
        width: 0;
        height: 0;
        position: absolute;
        top: -12px;
        left: 50px;
        border-width: 7px;
        border-style: solid;
        border-color: transparent transparent #000 transparent;
    }

.wxts_dialog .has_tip:hover .tip {
    display: block;
}

/*温馨提示 - 半透明遮照*/
#wxts_dialog_mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999998;
    display: none;
}

/*温馨提示 - 侧边栏*/
#wxts_slidbar {
    position: fixed;
    right: 10px;
    bottom: 419px;
    width: 72px;
    height: 72px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    background: #fff;
}

    #wxts_slidbar .text {
        margin-top: auto;
        margin-bottom: 10px;
    }

    #wxts_slidbar .icon {
        width: 26px;
        height: 26px;
        margin-bottom: 4px;
        background: url("../images/wxts_icon.png") no-repeat center center;
        background-size: cover;
        margin-bottom: auto;
        margin-top: 10px;
        transition: all 0.3s;
    }

    #wxts_slidbar:hover .icon {
        background: url("../images/wxts_icon_active.png") no-repeat center center;
        background-size: cover;
        animation: bounce 1s ease-in-out;
    }

    #wxts_slidbar:hover .text {
        color: #0070ff;
    }

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: translate3d(0, 0, 0);
    }

    40%, 43% {
        transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -13px, 0);
    }

    70% {
        transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -8px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.page-work .uk-card-body {
    padding: 0;
}

.el-carousel__container {
    position: relative;
    height: 300px;
}

.warm-prompt-box-item {
    padding: 8px 14px;
}

    .warm-prompt-box-item:hover {
        cursor: pointer;
    }

.warm-prompt-box-title {
    font-size: 14px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: 1; /* 控制最多显示几行 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-box-img {
    width: 100%;
    height: 220px; /*180px*/
    margin: 5px 0;
}

.warm-prompt-box-img1 {
    width: 100%;
    height: 220px; /*260px*/
    margin: 5px 0;
}

.warm-prompt-box-content {
    width: 100%;
    max-height: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: 4; /* 控制最多显示几行 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-more-label {
    cursor: pointer;
    text-decoration: none;
    color: #333;
}

    .warm-prompt-more-label:hover {
        text-decoration: none;
        color: #333;
    }
/*--------- 温馨提示 end ---------*/

/*-----资讯中心banner start-----*/

.news_banner .text {
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 14px;
    box-sizing: border-box;
    padding-left: 10px;
    text-align: left;
    z-index: 9;
}

.news_banner .text .title {
    display: inline-block;
    color: #fff;
    font-size: 14px;
    width: 71%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.news_banner .more {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 40px;
    line-height: 40px;
    width: 50px;
    text-align: center;
    color: #fff;
    z-index: 999;
    text-decoration: none;
    font-size: 14px;
}

.news_banner .swiper-pagination {
    text-align: right;
    padding-right: 60px;
}

.news_banner .swiper-pagination-bullet {
    box-sizing: border-box;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: none;
    text-indent: 100%;
    overflow: hidden;
    white-space: nowrap;
    border: 1px solid #fff;
    transition: 0.2s ease-in-out;
    opacity: 1;
}

.news_banner .swiper-pagination-bullet-active {
    background: #fff;
    opacity: 1;
}

.news_banner .more {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 40px;
    line-height: 40px;
    width: 50px;
}

.swiper-button-next {
    background: #fff url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E") no-repeat center center;
    background-size: 30% auto !important;
}

.swiper-button-prev {
    background: #fff url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E") no-repeat center center;
    background-size: 30% auto !important;
}

.swiper-button-next,
.swiper-button-prev {
    width: 40px !important;
    height: 40px !important;
    border-radius: 40px !important;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    opacity: 0.1;
}

.swiper-container:hover .swiper-button-next,
.swiper-container:hover .swiper-button-prev {
    opacity: 0.8;
}


/*-----资讯中心banner end-----*/

/*********** 审批 Tab 滚动条 css start **************/
#tabs1 {
    height: 100%;
}

#AuditFormLayout .mini-fit {
    /*overflow: hidden !important;*/
}

#tabs1 .mini-tabs-body-top {
   /* height: 100% !important;*/
}

/*#tabs1 .mini-tabs-body {
    overflow: auto !important;
    height: 900px !important;
}*/

#dataForm .mini-button-info {
    background-color: rgb(57, 123, 230) !important;
    border-color: rgb(57, 123, 230) !important;
}

    #dataForm .mini-button-info * {
        color: #fff !important;
        font-size: 18px;
        /* font-family: "Microsoft YaHei"; */
}

    #dataForm .mini-button-info span {
        height: 30px !important;
        line-height: 30px !important;
        vertical-align: top !important;
    }

#Flow_ExecuteButtonTR {
    height: 50px !important;
}
/*********** 审批 Tab 滚动条 css  end ***************/


/**************   h5 上传控件 start  ****************/
.gcs_h5fileupload
{
    position:relative;
    height:40px;
    line-height:40px;
    vertical-align:middle;
}
.gcs_h5fileupload .gcs_uploadintpuval
{
    min-width:345px;
    border:1px solid #CCC;

}
.gcs_h5fileupload .gcs_fileupload_buttonsp {
    width:50px;
    text-align:center;
    border:1px solid #CCC;
    border-left:0px;
    cursor:pointer;
}
.gcs_h5fileupload .gcs_fileupload_buttonsp:hover{
    color:#7d0808;
}
.gcs_uploadingpanel {
    position: absolute;
    left: 0px;
    top: 0px;
    background-color: rgba(200,200,200,0.5);
}

.gcs_uploadingpanel .gcs_uploadprogress
{
    height:20px;
    line-height:20px;
    width:100%;
    background-color:#F4F4F4;
}
.gcs_uploadingpanel .gcs_uploadprogresslabel
{
    position:absolute;
    left:45px;
    top:10px;
}

.gcs_uploadingpanel .gcs_uploadprogress .gcs_uploadprogressval {
    width: 0px;
    height: 18px;
    line-height: 18px;
    background-color: #7DCB3D;
    display: inline-block;
}
.gcs_h5fileupload .gcs_fileupload_ctinner {
    height: 40px;
    line-height: 40px;
}
.gcs_h5fileupload .gcs_fileupload_ctinner>span
{
    height:35px;
    line-height:35px;
    vertical-align:middle;
}
.gcs_h5fileupload .gcs_fileupload_ctinner span {
    display: inline-block;
}

.gcs_h5fileupload input[type='file'] {
    min-width: 345px;
    display: none;
}
.gcs_h5fileupload input[type='button'] {
    width: 74px;
    height: 33px;
    line-height: 30px;
    margin-right: 16px;
    outline: none;
    border: 1px solid #CCC;
    padding-left: 30px;
    background-position: 6px 50%;
    background-repeat: no-repeat;
    border-radius: .25rem;
}

/*********** 审批 Tab 滚动条 css  end ***************/


/**************   h5 上传控件 start  ****************/
.gcs_h5fileupload
{
    position:relative;
    height:40px;
    line-height:40px;
    vertical-align:middle;
}
.gcs_h5fileupload .gcs_uploadintpuval
{
    min-width:345px;
    border:1px solid #CCC;
    padding:0 5px;

}
.gcs_h5fileupload .gcs_fileupload_buttonsp {
    width:50px;
    text-align:center;
    border:1px solid #CCC;
    border-left:0px;
    cursor:pointer;
}
.gcs_h5fileupload .gcs_fileupload_buttonsp:hover{
    color:#7d0808;
}
.gcs_uploadingpanel {
    position: absolute;
    left: 0px;
    top: 0px;
    background-color: rgba(200,200,200,0.5);
}

.gcs_uploadingpanel .gcs_uploadprogress
{
    height:20px;
    line-height:20px;
    width:100%;
    background-color:#F4F4F4;
}
.gcs_uploadingpanel .gcs_uploadprogresslabel
{
    position:absolute;
    left:45px;
    top:10px;
}

.gcs_uploadingpanel .gcs_uploadprogress .gcs_uploadprogressval {
    width: 0px;
    height: 18px;
    line-height: 18px;
    background-color: #7DCB3D;
    display: inline-block;
}
.gcs_h5fileupload .gcs_fileupload_ctinner {
    height: 40px;
    line-height: 40px;
}
.gcs_h5fileupload .gcs_fileupload_ctinner>span
{
    height:35px;
    line-height:35px;
    vertical-align:middle;
}
.gcs_h5fileupload .gcs_fileupload_ctinner span {
    display: inline-block;
}
.gcs_h5fileupload input[type='file'] {
    min-width: 345px;
    display: none;
}
.gcs_h5fileupload input[type='button'] {
    width: 74px;
    height: 33px;
    line-height: 30px;
    margin-right: 16px;
    outline: none;
    border: 1px solid #CCC;
    padding-left: 30px;
    background-position: 6px 50%;
    background-repeat: no-repeat;
    border-radius: .25rem;
}
    /**************   h5 上传控件   end  ****************/
    /*loadingbox - 样式*/
    .emptybox {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 120px;
    }

    .emptybox .loading_icon div {
        background: #999;
    }

    .emptybox .text {
        font-size: 14px;
        margin-top: 10px;
    }
.emptybox .loading_icon div {
    background: #999;
}
.emptybox .text {
    font-size: 14px;
    margin-top: 10px;
}

/*文章置顶图标*/
.bgtitle {
    position: relative;
    padding-left: 20px;
}

    .bgtitle .top {
        content: "";
        width: 20px;
        height: 24px;
        position: absolute;
        background: url("/Portal/Images/is_top.png") no-repeat center center;
        background-size: cover;
        left: 0px;
        top: 8px;
    }

    .bgtitle .file {
        content: "";
        width: 12px;
        height: 15px;
        position: absolute;
        background: url("/WebResource/Master/images/index_icon_file.png") no-repeat center center;
        background-size: cover;
        left: 4px;
        top: 12px;
    }

.gcs_title {
    position: relative !important;
    padding-left: 20px !important;
}

.formDivTd .hbm-file-sc {
    margin-top: 13px;
}

/*页面底部*/
.bottomer p a::after {
    content: "";
    position: absolute;
    top: 0;
}

.bottomer {
    box-shadow: 0 0 6px 0 rgb(7 0 2 / 5%);
    background: #3d3e4a;
    padding: 30px 0 15px 0px;
    position: relative;
    z-index: 2;
}

    .bottomer .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 20px;
        background: url("../images/footer_border.png") no-repeat bottom center;
        background-size: auto 2px;
    }

        .bottomer .logo .icon {
            height: 50px;
            width: auto;
            padding-right: 20px;
        }

    .bottomer .bottomer-inner {
        padding-top: 15px;
        width: 800px;
        text-align: center;
        margin: 0 auto;
    }

        .bottomer .bottomer-inner > div {
            display: inline-block;
            font-size: 14px;
            color: #6b6c7d;
        }

/*--字体重新调整--*/
.work-top .uk-card .uk-list li,
.work-center .uk-card .uk-list li,
.gcs_newslist_item span.gcs_title a,
.gcs_newslist_item span.gcs_dt,
.gcs_fbgs_table .gcs_body .gcs_body_inner table tr td .gcs_tablecell span,
.gcs_tab_kjcxyd_childs .gcs_tabhd_item span:last-child,
.gcs_tab_yfwbmfwryrm > .gcs_tabhd ul li,
.gcs_fbgs_table .gcs_head .gcs_head_inner table th span,
.gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a > span.gcs_title,
.page-work .uk-grid .uk-width-1-5 ul li .uk-nav-sub li a,
.work-bottom .uk-card .uk-list li div,
.work-content .uk-card .uk-list li{
    font-size: 16px !important;
}

[v-cloak]{
    display: none;
}

.news_banner .more {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 40px;
    line-height: 40px;
    width: 50px;
    text-align: center;
    color: #fff;
    z-index: 999;
    text-decoration: none;
    font-size:14px;
}

.news_banner .text .title {
    display: inline-block;
    color: #fff;
    font-size: 14px;
    width: 64%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-panel .mini-panel-title {
    color:#FFF;
}
