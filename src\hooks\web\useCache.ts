/**
 * 配置浏览器本地存储的方式，可直接存储对象数组。
 */

import WebStorageCache from 'web-storage-cache'

import Cookies from 'js-cookie'

type CacheType = 'localStorage' | 'sessionStorage'

export const CACHE_KEY = {
  // 用户相关
  ROLE_ROUTERS: 'roleRouters',
  USER: 'user',
  // 系统设置
  IS_DARK: 'isDark',
  LANG: 'lang',
  THEME: 'theme',
  LAYOUT: 'layout',
  DICT_CACHE: 'dictCache',
  // 登录表单
  LoginForm: 'loginForm',
  TenantId: 'tenantId',
  // 4A认证
  login4A: 'login4A',
  //业务枚举
  enums: 'enums'
}

export const Storage = (type:CacheType = 'localStorage') => {
  const wsCache: WebStorageCache =new WebStorageCache({
    storage: type
  })
  return {
    wsCache
  }
}

export const useCache = () => {
  return {
    Cookies
  };
}

export const deleteUserCache = () => {
  const { wsCache } = Storage()
  wsCache.delete(CACHE_KEY.USER)
  wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  // wsCache.delete(CACHE_KEY.LoginForm)
  // 注意，不要清理 LoginForm 登录表单
}

//清理LoginForm 登录表单
export const deleteUserCacheAndLiginFrom = () => {
  const { wsCache } = Storage()
  wsCache.delete(CACHE_KEY.USER)
  wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  Cookies.remove(CACHE_KEY.LoginForm)
}
