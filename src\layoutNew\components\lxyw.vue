<script setup lang="ts">
const emit = defineEmits(['closeDialog'])
defineProps({
  showDialog: {
    type: Boolean,
    default: false
  }
})
const closeDialog = () => {
  emit('closeDialog', false)
}
</script>

<template>
  <div>
    <div
      class="uk-card uk-card-default work-card lxyw_dialog"
      :class="[showDialog? '' : 'lxyw_dialog_close']">
      <div class="uk-flex uk-card-header">
        <span class="uk-card-title">联系运维</span>
        <div class="close" @click="closeDialog()">+</div>
      </div>
      <div class="uk-card-body cygn-body" style="overflow: auto;">
        <div class="el-carousel el-carousel--horizontal">
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-title" title="综合办公系统">综合办公系统</span>
            </div>
            <div class="people-info">
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-userico"></span>
                <span class="hbm-yw-item-username">孟谦谦</span>
                <span class="hbm-yw-item-msgtouserico"></span>
              </div>
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-telico"></span>
                <span class="hbm-yw-item-tel">0871-63225022</span>
              </div>
            </div>
          </div>
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span
                class="hbm-yw-item-title"
                title="一卡通系统/员工卡办理">一卡通系统/员工卡办理</span>
            </div>
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-userico"></span>
              <span class="hbm-yw-item-username">赵中华</span>
              <span class="hbm-yw-item-msgtouserico"></span>
            </div>
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-telico"></span>
              <span class="hbm-yw-item-tel">18669084266、0871-63225007</span>
            </div>
          </div>
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-title" title="财务共享平台">财务共享平台</span>
            </div>
            <div class="people-info">
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-userico"></span>
                <span class="hbm-yw-item-username">方婧</span>
                <span class="hbm-yw-item-msgtouserico"></span>
              </div>
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-telico"></span>
                <span class="hbm-yw-item-tel">0871-63062933</span>
              </div>
            </div>
          </div>
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-title" title="财务信息发布系统">财务信息发布系统</span>
            </div>
            <div class="people-info">
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-userico"></span>
                <span class="hbm-yw-item-username">龚禹衡</span>
                <span class="hbm-yw-item-msgtouserico"></span>
              </div>
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-telico"></span>
                <span class="hbm-yw-item-tel">0871-63062267</span>
              </div>
            </div>
          </div>
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span
                class="hbm-yw-item-title"
                title="人力资源一站式服务平台">人力资源一站式服务平台</span>
            </div>
            <div class="people-info">
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-userico"></span>
                <span class="hbm-yw-item-username">邓砚兮</span>
                <span class="hbm-yw-item-msgtouserico"></span>
              </div>
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-telico"></span>
                <span class="hbm-yw-item-tel">0871-63062133</span>
              </div>
            </div>
          </div>
          <div class="hbm-ad-docker-yw-item">
            <div class="hbm-ad-docker-yw-row">
              <span class="hbm-yw-item-title" title="EPC项目管理系统">EPC项目管理系统</span>
            </div>
            <div class="people-info">
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-userico"></span>
                <span class="hbm-yw-item-username">卢云鹏</span>
                <span class="hbm-yw-item-msgtouserico"></span>
              </div>
              <div class="hbm-ad-docker-yw-row">
                <span class="hbm-yw-item-telico"></span>
                <span class="hbm-yw-item-tel">15313086230</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div v-if="showDialog" class="dialog_mask" @click="closeDialog()"></div>
    </transition>
  </div>
</template>

<style scoped lang="scss">
@import "@/assets/css/uikit.min.css";
@import "@/assets/css/bootstrap.min.css";

.cygn-body {
  padding: 8px;
  height: calc(100% - 50px);
}

.hbm-ad-docker-yw-item {
  padding: 6px 4px;

  span {
    display: inline-block;
    margin-right: 6px;
    vertical-align: middle;
  }

  .hbm-ad-docker-yw-row {
    height: auto;

    .hbm-yw-item-title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      /* 将对象作为弹性伸缩盒子模型显示 */
      -webkit-line-clamp: 1;
      /* 控制最多显示几行 */
      -webkit-box-orient: vertical;
      /* 设置或检索伸缩盒对象的子元素的排列方式 */
    }
  }

  .hbm-yw-item-title {
    font-size: 12px;
    font-weight: bold;
    color: #404a56;
  }

  .people-info {
    display: flex;
    margin-top: 4px;
  }

  .hbm-yw-item-userico {
    width: 12px;
    height: 12px;
    background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -44px 0;
  }

  .hbm-yw-item-username {
    font-size: 12px;
    font-weight: bold;
    color: #999999;
  }

  .hbm-yw-item-tel {
    font-size: 12px;
    font-weight: 500;
    color: #999999;
  }

  .hbm-yw-item-telico {
    width: 11px;
    height: 11px;
    background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -72px 0;
  }

  .hbm-yw-item-msgtouserico {
    width: 16px;
    height: 14px;
    background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -56px 0;
    cursor: pointer;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(30px);
  }
}

/* 淡出动画 */
.fade-height-leave-active {
  transition: all 0.5s ease;
  /* 初始高度设置为auto */
  height: auto;
  overflow: hidden;
}

.fade-height-leave-to {
  opacity: 0;
  /* 结束高度设置为0 */
  height: 0 !important;
  /* 移除内边距和外边距使效果更平滑 */
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.dialog_mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.lxyw_dialog {
  position: fixed;
  bottom: 50%;
  right: 50%;
  margin-right: -200px;
  margin-bottom: -175px;
  z-index: 9999999;
  width: 400px;
  height: 350px;
  border-radius: 10px !important;
  transition: all 0.8s;
  overflow: hidden;
}

.lxyw_dialog .uk-card-header {
  margin: 0;
  height: 50px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .uk-card-title {
    font-size: 14px;
    line-height: 1.4;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.lxyw_dialog_close {
  position: fixed;
  right: 10px;
  bottom: 440px;
  width: 0px;
  height: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  overflow: hidden;
  cursor: pointer;
}

.lxyw_dialog .close {
  margin-left: auto;
  width: 26px;
  height: 26px;
  border-radius: 30px;
  line-height: 20px;
  border: 1px solid rgb(201, 201, 201);
  font-size: 24px;
  text-align: center;
  transform: rotate(45deg);
}
</style>
