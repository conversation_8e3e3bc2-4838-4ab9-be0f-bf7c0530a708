<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="详情" width="90%" height="400px">
    <el-card>
      <template #header>
        <div style="text-align: right">
          <span>发送人: {{receiveData.ReceiverNames}}</span>
          <span style="margin-left: 30px">发送时间: {{receiveData.SendTime}}</span>
        </div>
      </template>
      <template #default>
        <div v-if="receiveData && receiveData.Content" v-html="receiveData.Content"></div>
        <el-divider />
        <el-table
          v-loading="loading"
          :data="readData"
          ref="tableRef"
        >
          <el-table-column type="index" width="55"/>
          <el-table-column label="接收人" align="center" prop="UserName"/>
          <el-table-column label="是否已读" align="center" prop="AlreadyRead">
            <template #default="{row}">
              <el-checkbox v-if="row.AlreadyRead === 1" type="success" :model-value="true"/>
              <el-checkbox v-else-if="row.AlreadyRead === 0" type="success" :model-value="false"/>
            </template>
          </el-table-column>
          <el-table-column label="阅读时间" align="center" prop="FirstViewTime"/>
          <el-table-column label="是否回复" align="center" prop="AlreadyReply">
            <template #default="{row}">
              <el-checkbox v-if="row.AlreadyReply === 1" type="success" :model-value="true"/>
              <el-checkbox v-else-if="row.AlreadyReply === 0" type="success" :model-value="false"/>
            </template>
          </el-table-column>
          <el-table-column label="回复时间" align="center" prop="ReplyTime"/>
        </el-table>
      </template>
    </el-card>
  </Dialog>
</template>
<script lang="ts" setup>
import Template from "@/views/template.vue";
import * as msgApi from "@/api/portal/msgList";
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false)
const receiveData = ref(); //消息数据
const readData = ref([]); //已读数据
const loading = ref(true) // 列表的加载中
const open = async (row) => {
  await init(row);
  dialogVisible.value = true;
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const init = async (row) => {
  loading.value = true
  try {
    let modelParams ={
      ID: row.ID,
      FuncType: 'Receive',
      ReceiveID: row.ReceiverID,
    };
    let readParams ={
      id: row.ID
    };
    let reData = await msgApi.getReceiveModel(modelParams);
    receiveData.value = reData;
    let read = await msgApi.getReceiveReadList(readParams);
    readData.value = read;
  } finally {
    loading.value = false
  }
}

</script>
