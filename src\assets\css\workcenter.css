.page {
  background-color: #f9fafd;
}

.page-work {
  /* min-height: 758px; */
  /*margin-top: 71px;*/
}

.page-work .uk-container-large {
  max-width: 1380px;
  min-width: 966px;
}

.page-work .uk-container-large .uk-grid {
  margin: 0;
  position: relative;
}

.page-work .uk-card {
  border-radius: 3px;
}

.page-work .uk-card-body {
  padding: 0;
}

.page-work [class*="uk-list"] > ::before {
  width: 0;
}

/*导航*/
.page-work .uk-width-1-5 {
  padding: 40px 0;
  z-index: 1000;
  width: 262px;
  position: absolute;
  left: 0;
  top: 0;
}

.page-work .uk-grid .uk-width-1-5 ul li a {
  padding: 0 40px;
}

.page-work .uk-grid .uk-width-1-5 ul li .uk-nav-sub {
  padding: 5px 0;
}

.page-work .uk-grid .uk-width-1-5 ul li ul li {
  height: 36px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-left: 2px solid #fff;
}

.page-work .uk-grid .uk-width-1-5 ul li .uk-nav-sub li a {
  padding-left: 65px;
  line-height: 36px;
}

.uk-nav-parent-icon > .uk-parent > a::after {
  transform: rotate(180deg);
  transition: transform 0.4s ease-out;
}

.page-work .uk-grid .uk-width-1-5 > ul > li > a {
  color: black;
  font-weight: 600;
  /* font-family: "MicrosoftYaHei"; */
}

.page-work .uk-grid .uk-width-1-5 > ul > li > a span {
  color: black;
  font-weight: 600;
  /* font-family: "MicrosoftYaHei"; */
}

.page-work .uk-grid .uk-width-1-5 > ul > li > a img {
  margin-right: 3px;
}

.page-work .uk-grid .uk-width-1-5 ul li .uk-active {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
}

.page-work .uk-grid .uk-width-1-5 ul li .uk-active > a {
  color: #0070ff;
  font-weight: 600;
}

.page-work .uk-width-4-5 {
  left: 19%;
  position: relative;
  padding-left: 14px;
}
/*图片新闻、院发文、院内通知、院内公告*/
.work-top,
.work-top .uk-card:last-child,
.work-top .uk-card .uk-slideshow-items {
  height: 330px;
}

.work-top .uk-card:first-child {
  width: 44%;
  max-width: 480px;
  min-width: 336px;
}

.work-top .uk-card:last-child {
  width: 56%;
  margin-left: 15px;
  max-width: 610px;
  min-width: 427px;
  overflow: hidden;
}

.work-top .uk-card .uk-slideshow-items .uk-position-bottom {
  text-align: center;
  background-color: rgba(0, 0, 0, 0.3);
  margin: 0;
  max-width: 100% !important;
  height: 30px;
  line-height: 30px;
}

.work-top .uk-card .uk-slideshow-items .uk-position-bottom .work-top-nva-title {
  width: 60%;
  margin-left: 10px;
  color: #ffffff;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-top .uk-card .work-top-nva-bottom {
  margin-bottom: 10px;
}

.work-top .uk-card .work-top-nva-bottom ul {
  margin-bottom: 0px;
  margin-top: 0px;
}

.work-top .uk-card .work-top-nva-bottom ul li {
  padding-left: 6px;
}

.work-top .uk-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 16px;
  line-height: 40px;
}

.work-top .uk-card .uk-card-header a {
  right: 26px;
  position: absolute;
}

.work-top .uk-card .uk-card-header > div {
  height: 40px;
  width: 60px;
  margin-right: 24px;
  cursor: pointer;
}

.work-top .uk-card .uk-card-header > .active {
  color: #0070ff;
  position: relative;
}

.work-top .uk-card .uk-card-header > .active::after {
  content: "";
  border-bottom: 2px solid #0070ff;
  width: 60px;
  height: 4px;
  background: #0d5ffe;
  border-radius: 8px 8px 0 0;
  position: absolute;
  bottom: 0;
  left: 0;
}

.work-top .uk-card .uk-card-header div > .active {
  color: #0070ff;
  width: 60px;
  height: 40px;
  line-height: 40px;
  display: block;
  text-align: center;
  text-indent: -9999px;
}

.work-top .uk-card .uk-card-header div > .active::before {
  content: "";
  width: 10px;
  height: 10px;
  border: 3px solid #0070ff;
  border-radius: 10px;
  background: #fff;
  position: absolute;
  top: 8px;
  left: -8px;
}

.work-top .uk-card .uk-card-header > div > span {
  font-weight: bold;
  font-size: 16px;
  color: #555;
  /* font-family: "MicrosoftYaHei"; */
}

.work-top .uk-card .uk-card-header > div > .uk-card-title {
  font-style: normal !important;
}

.work-top .uk-card .uk-list li {
  height: 40px;
  line-height: 40px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 2px solid #fff;
  overflow: hidden;
  box-sizing: border-box;
  padding-right: 20px;
}

.work-top .uk-card .uk-list li span {
  box-sizing: border-box;
}

.work-top .uk-card .uk-list li span:first-child {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: 10px;
  margin-left: 4px;
}

.work-top .uk-card .uk-list li span:last-child {
  width: 92px;
}

.work-top .uk-card .uk-list li:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

/*待办事项、常用功能*/
.work-center {
  margin-top: 14px;
}

.work-center .work-card:first-child {
  width: 63%;
  min-height: 340px;
  max-width: 684px;
  min-width: 479px;
}

.work-center .work-card:last-child {
  width: 37%;
  margin-left: 14px;
  max-width: 408px;
  min-width: 285px;
}

.work-center .uk-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

.work-center .uk-card .uk-card-header a {
  right: 26px;
  top: 0;
  position: absolute;
}

.work-center .uk-card .uk-card-header > div {
  height: 40px;
  margin-right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 2px solid #fff;
  display: flex;
  align-items: center;
}

.work-center .uk-card .uk-card-header > div:first-child::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_task.png") no-repeat
      center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(2)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_taskok.png")
      no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(3)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_msg.png") no-repeat
      center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(1)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_task_active.png")
      no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(2)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_taskok_active.png")
      no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(3)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("/WebResource/Master/images/index_icon_msg_active.png")
      no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > .active {
  color: #0070ff;
  border-bottom: 2px solid #0070ff;
  display: flex;
  align-items: center;
}

.work-center .uk-card .uk-card-header > .active .uk-card-title {
  font-style: italic !important;
}

.work-center .uk-card .uk-card-header div > .active {
  color: #0070ff;
  font-weight: bold;
}

/* .work-center .uk-card .uk-card-header .active > .uk-card-title {
  font-style: italic;
} */

.work-center .uk-card .uk-card-header > div > span {
  margin-right: 5px;
  /* font-family: "MicrosoftYaHei"; */
  margin-left: 2px;
  font-style: normal !important;
}

.work-center .uk-card .uk-list li {
  padding: 10px 20px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 2px solid #fff;
}

.work-center .uk-card .uk-list li .apply-flow {
  margin-left: 15px !important;
}

.work-center .uk-card .uk-list li .cate {
  width: 20% !important;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 20px;
}

.work-center .uk-card .uk-list li .cate span {
  width: 100%;
}

.work-center .uk-card .uk-list li .info {
  width: 36%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.work-center .uk-card .uk-list li .info .dept {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: auto;
  flex: 1;
  text-align: right;
}

.work-center .uk-card .uk-list li .info .name {
  width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-center .uk-card .uk-list li .info .time {
  width: 140px !important;
}

.work-center .uk-card .uk-list .uk-flex:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.work-center .uk-card .uk-list li div:nth-child(2) {
  width: 40%;
  max-width: 40%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: auto;
}

.work-center .uk-card .uk-list li div:nth-child(3) hr {
  margin: 13px 23px 11px 53px;
  height: 36px;
  border-color: #e3e9ee;
}

.work-center
  .uk-card
  .uk-list
  li
  div:nth-child(4)
  [class*="uk-list"]
  > ::before {
  left: -10px;
}

.work-center .uk-card .uk-list li div:nth-child(5) hr {
  margin: 13px 15px 11px 10px;
  height: 36px;
  border-color: #e3e9ee;
}

/* .work-center .uk-card .uk-list li div:last-child {
  padding-right: 12px;
  position: absolute;
  right: 0;
} */

.work-center .uk-card .uk-list li div ul li {
  height: 30px;
  line-height: 30px;
  margin: 0;
  padding-left: 0px;
}

.work-center .uk-card .uk-flex div img {
  vertical-align: middle;
  height: 14px;
  margin-right: 6px;
}

.focus-flow hr {
  margin: 13px 5px !important;
}

.work-center .uk-card .uk-list li .apply-flow {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 20px;
  border-left: 1px solid #eee;
}

.work-center .uk-card .uk-list li .apply-flow .dept,
.work-center .uk-card .uk-list li .apply-flow .name,
.work-center .uk-card .uk-list li .apply-flow .time {
  width: 100% !important;
  max-width: 100% !important;
  text-align: left;
}

.work-center .uk-card ul li .apply-flow > ul li > .uk-flex-disc {
  height: 30px;
  line-height: 30px;
  margin-top: 0;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:first-child
  > .uk-flex-disc:first-child {
  width: 50%;
}

.work-center .uk-card ul li .apply-flow > ul li > .uk-flex-disc li div {
  position: relative;
  display: flex;
  margin-right: 0;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li
  > .uk-flex-disc
  li
  div
  span:first-child {
  width: 84px;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:first-child
  > .uk-flex-disc:last-child
  li
  div {
  margin: 0;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:first-child
  > .uk-flex-disc:last-child
  li
  div
  span:first-child {
  width: 56px;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:first-child
  > .uk-flex-disc:first-child
  li
  div
  span:last-child {
  max-width: 42px;
  width: 42px;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:first-child
  > .uk-flex-disc:last-child
  li
  div
  span:last-child {
  max-width: 78px;
  width: 78px;
}

.work-center
  .uk-card
  ul
  li
  .apply-flow
  > ul
  li:last-child
  > .uk-flex-disc
  li
  div
  span:last-child {
  min-width: 126px;
  max-width: 180px;
}

.work-center .uk-card > .cygn-body .uk-slider .uk-slider-container {
  max-width: 360px;
  min-width: 252px;
  margin: 0 auto;
}

.work-center .uk-card > .cygn-body .cygn-dh a {
  padding: 0;
  color: #e4e4e4;
  background: #000000;
  width: 16px;
  height: 40px;
  opacity: 0.25;
}

.work-center .uk-card > .cygn-body .cygn-dh a:first-child {
  border-radius: 0 3px 3px 0;
}

.work-center .uk-card > .cygn-body .cygn-dh a:last-child {
  border-radius: 3px 0 0 3px;
}

.work-center .uk-card > .cygn-body .cygn-dh a svg {
  width: 9px;
  height: 13px;
  position: relative;
  top: 13px;
  left: 3px;
}

.work-center .uk-card > .cygn-body .cygn-dh a svg polyline {
  stroke-width: 6;
}

.work-center .uk-card > .cygn-body ul li {
  height: 100%;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover {
  height: 150px;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover .cygn-top {
  height: 47px;
  padding: 16px 20px;
  cursor: move;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover .cygn-top .directory {
  width: 60px;
  height: 16px;
  transform: scale(0.83);
  font-weight: 400;
  line-height: 14px;
  background: #f4f4f4;
  text-align: center;
  color: #32b66b;
  border: 1px solid #32b66b;
  cursor: pointer;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover .cygn-top .enable {
  color: #c2c2c2;
  border: 1px solid #d2d2d2;
  pointer-events: none;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover .cygn-center {
  height: 40px;
  text-align: center;
  cursor: pointer;
}

.work-center .uk-card > .cygn-body ul li .uk-card-hover .cygn-bottom {
  height: 63px;
  text-align: center;
  padding: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
  cursor: pointer;
}

/*我的消息、我的文件*/
.work-bottom {
  margin: 14px 0 !important;
}

.work-bottom .bottom-card {
  width: 100%;
  max-width: 546px;
  min-width: 382px;
  max-height: 281px;
  min-height: 197px;
}

.work-bottom .bottom-card:first-child {
  margin-right: 14px;
}

.work-bottom .bottom-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

.work-bottom .bottom-card .uk-card-header div {
  display: flex;
  align-items: center;
  height: 42px;
}

.work-bottom .bottom-card .uk-card-header div .uk-badge {
  margin-left: 10px;
}

.work-bottom .bottom-card .uk-card-header a {
  right: 22px;
  position: absolute;
}

.work-bottom .uk-card .uk-flex div img {
  vertical-align: middle;
  height: 16px;
  width: 15px;
  margin-right: 6px;
}

.work-bottom .uk-card .uk-flex div .task-more {
  color: #ff6666;
  cursor: pointer;
}

.work-bottom .uk-card .uk-list li {
  height: 40px;
  line-height: 40px;
  margin: 0;
  padding-left: 24px;
  display: flex;
  align-items: center;
  border-left: 2px solid #fff;
}

.work-bottom .uk-card .uk-list .uk-flex:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.work-bottom .uk-card .uk-list .uk-flex ul li {
  border: none;
}

.work-bottom .uk-card .uk-list li div:nth-child(2) {
  width: 55%;
  max-width: 60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-bottom .uk-card .uk-list li div:nth-child(3) hr {
  margin: 13px 23px 11px 22px;
  height: 36px;
  border-color: #e3e9ee;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) {
  right: 8px;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) ul li span:first-child {
  width: auto;
  text-align-last: justify;
  -moz-text-align-last: justify; /*兼容firefox*/
  text-align: justify;
  text-justify: distribute-all-lines; /*兼容ie*/
  display: block; /*ie下需设为block*/
  margin-right: 5px;
  color: #a9b4c7;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) ul li span:last-child {
  color: #80848d;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) ul li span:first-child > i {
  /* display: inline-block;
  width: 100%; */
}

.work-bottom
  .uk-card
  .uk-list
  li
  div:nth-child(4)
  [class*="uk-list"]
  > ::before {
  left: -10px;
}

.work-bottom .uk-card .uk-list li div ul {
  /* border: 1px solid blue; */
  width: 140px;
}

.work-bottom .uk-card .uk-list li div ul li {
  height: 20px;
  line-height: 20px;
  margin: 0;
  padding-left: 0px;
}

.work-bottom .uk-card .uk-list li div ul li::before {
  margin-bottom: 0;
}

.work-bottom
  .bottom-card:last-child
  .uk-card-body
  ul
  li
  div
  img:nth-child(n + 1) {
  width: 16px;
  height: 16px;
}

.work-bottom .bottom-card:last-child .uk-card-body ul li div:first-child img {
  width: 16px;
  height: 22px;
}

/*我的待办列表*/
.work-content {
  margin-bottom: 14px !important;
}

.work-content > .uk-card {
  width: 100%;
}

.work-content .uk-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

.work-content .uk-card .uk-card-header div:last-child {
  position: absolute;
  right: 0;
  height: 36px;
}

.work-content .uk-card .uk-card-header div:last-child input {
  height: 36px;
  margin: 2px 0;
  border-radius: 3px;
}

.work-content .uk-card .uk-card-header > div {
  height: 40px;
  margin-right: 24px;
  cursor: pointer;
}

.work-content .uk-card .uk-card-body {
  height: 100%;
  min-height: 600px;
}

/*办公中心-我的工作*/
.work-content .uk-card .uk-list li {
  margin: 0;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 2px solid #fff;
}

.work-content .uk-card .mcol li {
  padding: 10px 20px;
}

.work-content .uk-card .uk-list li:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.work-content .uk-card .uk-list li .icon {
  margin: 0;
  padding: 0;
  width: 16px;
  height: 15px;
  line-height: 15px;
  margin-right: 10px;
}

.work-content .uk-card .uk-list li .icon img {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.work-content .uk-card .uk-list li .title {
  width: 40%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: auto;
}

.work-content .uk-card .uk-list li .info {
  width: 40% !important;
  text-align: right !important;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.work-content .uk-card .uk-list li .info div {
  display: flex;
  width:100% !important;
}

.work-content .uk-card .uk-list li .info div span:nth-child(2) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 58%;
  text-align: left;
}

.work-content .uk-card .uk-list li .cate {
  width: 16% !important;
  text-align: center !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-content .uk-card .uk-list li .cate span {
  width: 100%;
}

.work-content .uk-card .uk-list li .info .dept {
  display: inline-block;
  width: 144px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-content .uk-card .uk-list li .info .name {
  margin-left: 10px;
  width: 70px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.work-content .uk-card .uk-list li .info .time {
  margin-left: 10px;
  width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-content .uk-card .mcol li .title {
  width: 67%;
  height: 32px;
  line-height: 32px;
}

.work-content .uk-card .mcol li .info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 20px;
  width: 30% !important;
  border-left: 1px solid rgb(229, 229, 229);
}

.work-content .uk-card .uk-list li .info img {
  width: 16px !important;
  height: 17px !important;
  vertical-align: middle;
}

.work-content .uk-card .mcol li .info span.k {
  color: #999;
}

.no-data {
  width: 100%;
  height: 100%;
  text-align: center;
  padding-top: 30px;
}

.no-data img {
  width: 100px;
  height: auto;
}

.seamless-warp {
  height: 280px;
  overflow: hidden;
}

.seamless-warp .seamless-warp-ul {
  margin: 0 !important;
  padding: 0 10px !important;
}

.seamless-warp .seamless-warp-ul li {
  padding: 5px 0;
}

.seamless-warp .seamless-warp-ul li:hover {
  cursor: pointer;
}

.seamless-warp .seamless-warp-ul li .title {
  display: inline-block;
  float: left;
  text-align: left;
  width: 75%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 1; /* 控制最多显示几行 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.seamless-warp .seamless-warp-ul li .date {
  display: inline-block;
  float: right;
  width: 25%;
  text-align: right;
}

.warm-prompt-box-item {
  padding: 8px 14px;
}

.warm-prompt-box-item:hover {
  cursor: pointer;
}

.warm-prompt-box-title {
  font-size: 14px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 1; /* 控制最多显示几行 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-box-img {
  width: 100%;
  height: 220px; /*180px*/
  margin: 5px 0;
}

.warm-prompt-box-img1 {
  width: 100%;
  height: 220px; /*260px*/
  margin: 5px 0;
}

.warm-prompt-box-content {
  width: 100%;
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 4; /* 控制最多显示几行 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-more-label {
  cursor: pointer;
  text-decoration: none;
  color: #333;
}

.warm-prompt-more-label:hover {
  text-decoration: none;
  color: #333;
}

.warm-prompt-box-item > div {
  height: 260px;
}

.uk-grid > * {
  margin: 10px 0 10px -10px;
  padding: 0 30px;
}

.cygn-bottom:hover {
  cursor: pointer;
}

.directory:hover {
  cursor: pointer;
}

.function-collect {
  padding: 0 26px;
  height: 40px;
  line-height: 40px;
}

.function-collect-img {
  vertical-align: middle;
  height: 14px;
  margin-top: -4px;
  margin-right: 6px;
}

.function-collect-more {
  position: absolute;
  right: 26px;
  top: 10px;
}

* + .uk-grid-margin,
.uk-grid + .uk-grid,
.uk-grid > .uk-grid-margin {
  margin: 10px 0 10px -10px;
}

.page-work .uk-card-body .uk-text-center {
  padding: 20px 0;
}

.function-collect-grid > div {
  margin: 0 !important;
  padding: 0 !important;
  height: 100px;
  width: 99px;
  float: left;
  margin-right: 15px !important;
  margin-bottom: 15px !important;
  border: 1px solid #eee;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
}

.function-collect-grid > div .uk-card {
  border-radius: 10px;
}

.function-collect-grid > div .uk-card:hover {
  box-shadow: none;
}

.function-collect-grid > div:hover {
  background: #fff;
  box-shadow: 0 0px 20px rgb(0 0 0 / 10%);
  /* margin-top: -2px; */
}

.function-collect-grid > div div {
  width: 100%;
  height: 100%;
  border-radius: 10px;
}

.function-collect-grid > div div div:first-child {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 10px;
}

.function-collect-grid div div {
  margin: 0;
  padding: 0;
}

.function-collect-grid > div div div:first-child .cygn-bottom {
  padding: 0 4px;
}

.function-collect-grid > div div div:first-child .cygn-center {
  margin-bottom: -4px;
}

.function-collect-grid > div div div:first-child .cygn-center img {
  width: auto;
  height: 24px;
}

/*----办公中心 - 功能收藏----*/
.func_fav .list {
  padding-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}
.func_fav .list .item {
  width: 100px;
  height: 100px;
  padding: 0 4px;
  border-radius: 10px;
  border: 1px solid #eee;
  float: left;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  margin-top: 15px;
  text-decoration: none;
  background: #fff;
  transition: all 0.1s;
  box-sizing: border-box;
}

.func_fav .list .item:hover {
  background: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  margin-top: 13px;
}

.func_fav .list .item:active {
  background: #f1f1f1;
}

.func_fav .list .item .icon {
  width: auto;
  height: 24px;
}

.func_fav .list .item .text {
  text-align: center;
  margin-top: 8px;
  flex-wrap: wrap;
  max-width: 100%;
}

.uk-card-default .uk-card-title {
  font-size: 16px !important;
  font-weight: bold;
}
