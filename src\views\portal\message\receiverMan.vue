<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="人员多选" width="95%" height="360px">
    <el-row>
      <el-col :span="6">
        <ContentWrap class="h-1/1">
          <Tree v-if="treeDataLoaded" :expandedKeys="expandedKeys" :data="treeData" @node-clicked="nodeClicked" @node-contextmenu="nodeContextmenu" />
        </ContentWrap>
      </el-col>
      <el-col :span="12">
        <ContentWrap class="h-1/1">
          <div style="display: flex;justify-content: space-between;">
            <el-button @click="allSelect()">全选</el-button>
            <el-input v-model="queryParams.param" style="width: 300px" clearable placeholder="请输入工号或者姓名">
              <template #append>
                <el-button @click="getList"><Icon icon="ep:search" class="mr-5px" /></el-button>
              </template>
            </el-input>
          </div>
            <el-table style="margin-top: 20px" v-loading="loading" :data="list" >
              <el-table-column align="center" label="工号" prop="workNo"/>
              <el-table-column align="center" label="姓名" prop="name"/>
              <el-table-column align="center" label="职务" prop="duties"/>
              <el-table-column align="center" label="收藏" prop="name"/>
              <el-table-column align="center" label="选择">
                <template #default="scope">
                  <el-button
                    link
                    type="danger"
                    @click="selectRow(scope.row)">
                    选择
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </ContentWrap>
      </el-col>
      <el-col :span="6">
        <ContentWrap class="h-1/1">
          <div>
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="dialogVisible = false">取 消</el-button>
          </div>
          <el-table style="margin-top: 20px" v-loading="loading" :data="addList">
            <el-table-column align="center" label="工号" prop="workNo" show-overflow-tooltip />
            <el-table-column align="center" label="姓名" prop="name" show-overflow-tooltip />
            <el-table-column align="center" label="移除">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  @click="removeRow(scope.row)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </ContentWrap>
      </el-col>

    </el-row>
  </Dialog>
</template>

<script lang="ts" setup>
import {Tree} from '@/components/tree';
import * as ByResourceApi from '@/api/system/byResource'
import {AUserApi} from '@/api/system/auser';
const emit = defineEmits(['custom-event']);
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
//选项卡默认选中
const activeName = ref('AllRole')
const dialogVisible = ref(false)
// 列表的总页数
const total = ref(0)
// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const expandedKeys = ref([])
const nodeId = ref('')
const resId = ref('')
// 列表的加载中
const loading = ref(false)
// 列表的数据
const list = ref()
//已绑定列表的数据
const addList = ref([])
const isAuthLower = ref()

//查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  param: ''
})
const open = async () => {
  addList.value = [];
  getTreeData();
  getList();
  dialogVisible.value = true;
}
defineExpose({ open })
const selectRow = (row) =>{
  if (!addList.value.includes(row)){
    addList.value.push(row)
  }
}
const allSelect = () =>{
  list.value.forEach(item => {
    if (!addList.value.includes(item)){
      addList.value.push(item)
    }
  })

}

const removeRow = (row) =>{
  addList.value.splice(addList.value.indexOf(row), 1);
}
/** 获取人员列表数据 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      deptID: nodeId.value,
      ...queryParams
    }
    const data = await AUserApi.getAUserPage(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 获取树数据 */
const getTreeData = async () => {
  try {

    const response = await ByResourceApi.getGroupTree({ id: null, groupType: "Org" });
    treeData.value = response;
    expandedKeys.value = treeData.value.map(item => item.id);
    treeDataLoaded.value = true;
  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
}

//树鼠标右键
const nodeContextmenu = (node) => {
  nodeId.value = node.key
  getList();
}
//树鼠标左键
const nodeClicked = (node) => {
  nodeId.value = node.key
  getList();
}
//确定
const submitForm = async () => {
  try {
    emit('custom-event', addList.value)
  } catch (e){

  } finally {
    dialogVisible.value = false
  }
}

</script>

