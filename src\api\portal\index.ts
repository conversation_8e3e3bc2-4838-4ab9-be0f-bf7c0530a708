
import request from '@/config/axios';

// export interface SocialClientVO {
//   id: number
//   name: string
//   socialType: number
//   userType: number
//   clientId: string
//   clientSecret: string
//   agentId: string
//   status: number
// }

// // 查询社交客户端列表
// export const getSocialClientPage = async (params) => {
//   return await request.get({ url: `/system/social-client/page`, params })
// }

// // 新增社交客户端
// export const createSocialClient = async (data: SocialClientVO) => {
//   return await request.post({ url: `/system/social-client/create`, data })
// }

//主菜单
export const main_menu = async (params) => {
  return await request.postOriginal({ url: `/Portal/Home/GetSysMenu`, params })
}

//院发文
export const yfw_list = async (params) => {
  return await request.postOriginal({ url: `/Portal/Home/getYnfw`, params })
}

//院通知
export const ytz_list = async (params) => {
  return await request.postOriginal({ url: `/Portal/Home/getGsxw`, params })
}

//院公告
export const ygg_list = async (params) => {
  return await request.postOriginal({ url: `/Portal/Home/getTzgg`, params })
}

//功能收藏
export const gnsc_list = async (params) => {
  return await request.postOriginal({ url: `/system/Portal/WorkCenter/WorkSysMenu`, params })
}

//我的待办
export const wdgz_list = async (params) => {
  return await request.get({ url: `/system/Portal/WorkCenter/GetMyWork`, params })
}
//我的已办
export const getCompleteTasks = async (params) => {
  return await request.post({ url: import.meta.env.VITE_TOURL_PREFIX+ '/TaskApi/getCompleteTasks', params})
}
//我的待办新接口
export const wddb_list = async (params) => {
   return await request.post({ url: import.meta.env.VITE_TOURL_PREFIX + `/TaskApi/getNewTasks?page=${params.page}&pageSize=${params.pageSize}&Activity=${params.activity ? params.activity : ''}&Type=newtask`})
   //return await request.post({ url:  `http://10.10.79.68:8080/TaskApi/getNewTasks?page=${params.page}&pageSize=${params.pageSize}&Activity=${params.activity ? params.activity : ''}&Type=newtask`})
}
//点击排行
export const djph_list = async (params) => {
  return await request.get({ url: `/system/Portal/WorkCenter/LeftTopMenu`, params })
}

//用户中心列表
export const yhzx_list = async (params) => {
  return await request.get({ url: `/system/Portal/UserCenter/LeftTopMenu`, params })
}

//获取用户信息
export const user_info = async () => {
  return await request.get({ url: "/system/Portal/UserCenter/info" })
}

//获取用户信息
export const update_user_info = async (data: any) => {
  return await request.post({ url: "/system/Portal/UserCenter/updateUserInfo", data })
}

//上传头像和签名
export const uploadImg = async (data: any) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('type', data.type);
  formData.append('name', data.name);
  return await request.upload({
    url: import.meta.env.VITE_TOURL_PREFIX + '/FileManager/WebUploader/UploadFile',
    data: formData,
  })
}

//更新用户信息
export const updateUserInfo = async (data: any) => {
  return await request.upload({ url: "/system/Portal/UserCenter/uploadImg", data })
}

//获取uuid通用功能
export const getGuid = async () => {
  return await request.get({ url: "/system/Portal/WorkCenter/GetGuid" })
}

//获得帮助文档
export const getHelpDoc = async (params) => {
  return await request.get({ url: "/Portal/helpDocument/getHelpDocument", params })
}

//修改帮助文档
export const updateHelpDoc = async (data: any) => {
  return await request.post({ url: "/Portal/helpDocument/updateHelpDocument", data })
}

//4A认证接口处理
export const Get4ALoginUrl = async () => {
  return await request.post({ url: "/Portal/Home/Get4ALoginUrl" })
}

//请求设置set4ACookie
export const set4ACookie = async () => {
  return await request.get({ url: "/Portal/Home/set4ACookie" })
}
export const Jsonp4ACookieis = async (url: string) => {
  return jsonp(url, "set4Acookie")
}

export const getZXZXHeaderImg = async ()=>{
  return await request.get({ url: "/Portal/Home/getZXZXHeaderImg" })
}


//批量同步用户头像和签名数据
export const setUserImgsSync = async (data) => {
  return await request.get({ url: "/MHBus/user/userImgsSync?token=" + data })
}


/**
 * 手动实现 JSONP
 * @param url 请求的 URL（包含回调函数名）
 * @param callbackName 回调函数名
 * @returns 返回 Promise
 */
const jsonp = (url: string, callbackName: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    // 创建全局回调函数
    (window as any)[callbackName] = (data: any) => {
      resolve(data); // 解析数据
      cleanup(); // 清理
    };

    // 创建 script 标签
    const script = document.createElement('script');
    script.src = url;
    script.onerror = () => {
      reject(new Error('JSONP request failed'));
      cleanup(); // 清理
    };

    // 将 script 标签添加到文档中
    document.body.appendChild(script);

    // 清理函数
    const cleanup = () => {
      document.body.removeChild(script);
      delete (window as any)[callbackName];
    };
  });
};
