<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-divider content-position="left"  ><a class="colored-underline">基本信息</a></el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工号" prop="workNo">
            <el-input v-model="formData.workNo" placeholder="请输入工号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="账号" prop="loginName">
            <el-input v-model="formData.loginName" placeholder="请输入登录名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否授权" prop="isAuth">
            <el-radio-group v-model="formData.isAuth" class="ml-4">
              <el-radio  v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN)"
                         :key="dict.value"
                         :label="dict.value"
                         size="large" >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="所在部门" prop="parentID">
            <el-tree-select
              v-model="formData.deptID"
              :data="aGroupTree"
              :props="defaultProps"
              check-strictly
              default-expand-all
              placeholder="所在部门"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="兼职部门" prop="parentID">
            <el-tree-select
              v-model="formData.parttimeDeptID"
              :data="aGroupTree"
              :props="defaultProps"
              check-strictly
              default-expand-all
              placeholder="兼职部门"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="职务" prop="duties">
            <el-input v-model="formData.duties" placeholder="请输入职务" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortIndex">
            <el-input v-model="formData.sortIndex" placeholder="请输入排序" />
          </el-form-item>
        </el-col>
      </el-row>


<!--
      <el-form-item label="兼职部门" prop="parttimeDeptName">
        <el-input v-model="formData.parttimeDeptName" placeholder="请输入兼职部门" />
      </el-form-item>
-->

      <el-divider content-position="left"  ><a class="colored-underline">个人信息</a></el-divider>

      <el-row>
        <el-col :span="12">
          <el-form-item label="出生年月" prop="birthday">
            <el-date-picker
              v-model="formData.birthday"
              type="date"
              value-format="x"
              placeholder="选择出生年月"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="formData.sex" placeholder="请选择性别">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="座机电话" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="移动电话" prop="mobilePhone">
            <el-input v-model="formData.mobilePhone" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="Emai" prop="email">
        <el-input v-model="formData.email" placeholder="请输入Emai" />
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <el-input v-model="formData.description" type="textarea" rows="3" placeholder="请输入备注" />
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AUserApi, AUserVO } from '@/api/system/auser'
import {AGroupApi} from '@/api/system/group'
import {defaultProps, handleTree} from '@/utils/tree'
const aGroupTree = ref() // 树形结构
/** 获得系统角色管理树 */
const getAGroupTree = async () => {
  aGroupTree.value = []
  const data = await AGroupApi.getAGroupList({groupType:"Org"})
  const root: Tree = {id: 0, name: '顶级系统角色管理', children: []}
  root.children = handleTree(data, 'id', 'parentID')
  aGroupTree.value.push(root)
}
onMounted(()=>{
   getAGroupTree();
})
/** 个人设置 表单 */
defineOptions({ name: 'AUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  loginName: undefined,
  workNo: undefined,
  password: undefined,
  sortIndex: undefined,
  deptID: undefined,
  deptName: undefined,
  deptFullID: undefined,
  parttimeDeptID: undefined,
  parttimeDeptName: undefined,
  isAuth: undefined,
  isDeleted: undefined,
  deleteTime: undefined,
  lastLoginTime: undefined,
  lastLoginIP: undefined,
  errorCount: undefined,
  errorTime: undefined,
  modifyTime: undefined,
  description: undefined,
  sex: undefined,
  inDate: undefined,
  outDate: undefined,
  phone: undefined,
  mobilePhone: undefined,
  email: undefined,
  address: undefined,
  duties: undefined,
  birthday: undefined,
  clientIp: undefined,
  signPwd: undefined,
  systemCode: undefined,
  sortIndex1: undefined,
  sortIndex2: undefined,
  sortIndex3: undefined,
  sortIndex4: undefined,
  sortIndex5: undefined,
  acceptMobileMsg: undefined,
  status: undefined,
  iDCardImg: undefined,
  userImg: undefined,
  iDCardImgF: undefined,
  ext1: undefined,
  officeId: undefined,
  officeName: undefined,
  ucmobile: undefined,
  uctitle: undefined,
  ucshort: undefined,
  ucmail: undefined,
  thirdPartMail: undefined,
  voipNumber: undefined,
  outKey: undefined,
  idCard: undefined,
  userType: undefined,
  isAuthority: undefined,
  nation: undefined,
  beforeName: undefined,
  politics: undefined,
  officeFullID: undefined,
})
const formRules = reactive({
  isDeleted: [{ required: true, message: '是否删除不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AUserApi.getAUser(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AUserVO
    if (formType.value === 'create') {
      await AUserApi.createAUser(data)
      message.success(t('common.createSuccess'))
    } else {
      await AUserApi.updateAUser(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    loginName: undefined,
    workNo: undefined,
    password: undefined,
    sortIndex: undefined,
    deptID: undefined,
    deptName: undefined,
    deptFullID: undefined,
    parttimeDeptID: undefined,
    parttimeDeptName: undefined,
    isAuth: undefined,
    isDeleted: undefined,
    deleteTime: undefined,
    lastLoginTime: undefined,
    lastLoginIP: undefined,
    errorCount: undefined,
    errorTime: undefined,
    modifyTime: undefined,
    description: undefined,
    sex: undefined,
    inDate: undefined,
    outDate: undefined,
    phone: undefined,
    mobilePhone: undefined,
    email: undefined,
    address: undefined,
    duties: undefined,
    birthday: undefined,
    clientIp: undefined,
    signPwd: undefined,
    systemCode: undefined,
    sortIndex1: undefined,
    sortIndex2: undefined,
    sortIndex3: undefined,
    sortIndex4: undefined,
    sortIndex5: undefined,
    acceptMobileMsg: undefined,
    status: undefined,
    iDCardImg: undefined,
    userImg: undefined,
    iDCardImgF: undefined,
    ext1: undefined,
    officeId: undefined,
    officeName: undefined,
    ucmobile: undefined,
    uctitle: undefined,
    ucshort: undefined,
    ucmail: undefined,
    thirdPartMail: undefined,
    voipNumber: undefined,
    outKey: undefined,
    idCard: undefined,
    userType: undefined,
    isAuthority: undefined,
    nation: undefined,
    beforeName: undefined,
    politics: undefined,
    officeFullID: undefined,
  }
  formRef.value?.resetFields()
}
</script>
<style>

.colored-underline {
  text-decoration: underline;
  text-decoration-color: #C50B4E; /* 你可以使用任何颜色 */
}
</style>
