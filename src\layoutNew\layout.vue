<template>
  <div style="background: #fff;">
    <portal_header/>
    <!-- 只有在header加载完成后才渲染router-view -->
    <div class="main-container">
      <router-view v-if="layoutStore.canShowRouterView" />
    </div>
    <portal_bottomer/>
  </div>
</template>
<script lang="ts" setup>
import portal_header from './components/header.vue'
import portal_bottomer from './components/footer.vue'

import { useLayoutStore } from '@/store/modules/layout'

const layoutStore = useLayoutStore()
</script>

<style scoped>
.main-container {
  min-height: calc(100vh - 120px);
}
</style>
