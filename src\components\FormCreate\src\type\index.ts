import { Rule } from '@form-create/element-ui' //左侧拖拽按钮

// 左侧拖拽按钮
export interface MenuItem {
  label: string
  name: string
  icon: string
}

// 左侧拖拽按钮分类
export interface Menu {
  title: string
  name: string
  list: MenuItem[]
}

export interface MenuList extends Array<Menu> {}

// 拖拽组件的规则
export interface DragRule {
  icon: string
  name: string
  label: string
  children?: string
  inside?: true
  drag?: true | String
  dragBtn?: false
  mask?: false

  rule(): Rule

  props(v: any, v1: any): Rule[]
}

// 通用下拉组件 Props 类型
export interface ApiSelectProps {
  name: string // 组件名称
  labelField?: string // 选项标签
  valueField?: string // 选项的值
  url?: string // url 接口
  isDict?: boolean // 是否字典选择器
}

// 选择组件规则配置类型
export interface SelectRuleOption {
  label: string // label 名称
  name: string // 组件名称
  icon: string // 组件图标
  props?: any[] // 组件规则
}

type FieldItem = {
  value?: string;
  label: string;
  //修改当前规则的必填,禁用和说明
  update?: {
      required?: Boolean;
      disabled?: Boolean;
      info?: string;
  };
  children?: FieldItem[];
}

//自定义变量
type VarItem = {
  id?: string;
  label: string;
  children?: VarItem[];
}

//设计器组件的config配置类型
export interface Config {
  //定义表单配置默认值
  formOptions?: Object;
  //配置field是否可以编辑
  fieldReadonly?: boolean;
  //field选择项,支持多级
  fieldList?: FieldItem[];
  //控制子表单组件字段是否和子表单字段联动, 默认开启
  relationField?: boolean;
  //自定义变量列表
  varList?: VarItem[];
  //隐藏拖拽操作按钮
  hiddenDragMenu?: boolean;
  //隐藏拖拽按钮
  hiddenDragBtn?: boolean;
  //隐藏部分组件
  hiddenItem?: string[];
  //隐藏组件的部分配置项
  hiddenItemConfig?: {
      //拖拽规则name: 隐藏的字段名
      [id: string]: string[];
  };
  //禁用组件的部分配置项
  disabledItemConfig?: {
      //拖拽规则name: 禁用的字段名
      [id: string]: string[];
  };
  //是否显示保存按钮
  showSaveBtn?: boolean;
  //是否显示右侧的配置界面
  showConfig?: boolean;
  //是否显示组件的基础配置表单
  showBaseForm?: boolean;
  //是否显示组件联动
  showControl?: boolean;
  //是否显示绑定变量
  showVariable?: boolean;
  //是否显示模块管理
  showPageManage?: boolean;
  //是否显示组件的高级配置表单
  showAdvancedForm?: boolean;
  //是否显示组件的属性配置表单
  showPropsForm?: boolean;
  //是否显示组件的样式配置表单
  showStyleForm?: boolean;
  //是否显示组件的事件配置表单
  showEventForm?: boolean;
  //是否显示组件的验证配置表单
  showValidateForm?: boolean;
  //是否显示表单配置
  showFormConfig?: boolean;
  //是否显示左侧的模板列表
  showTemplate?: boolean;
  //是否显示多端适配选项
  showDevice?: boolean;
  //定义渲染规则所需的formData
  appendConfigData?: string[] | ((rule: Rule) => Object);
}
