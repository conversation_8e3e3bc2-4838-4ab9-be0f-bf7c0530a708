<template>
  <div class="work-center uk-card-default work-card func_fav">
    <div class="uk-card uk-card-default function-collect">
      <div class="uk-flex uk-card-header">
        <div>
          <img src="@/assets/icons/portal/icon-cygn.png" />
          <span :style="itemStyle.titleStyle" class="uk-card-title">{{ ItemTitle.title }}</span>
        </div>
        <a @click="CYGNModify"><img src="@/assets/icons/portal/icon-more.png" /></a>
      </div>

      <div class="uk-card-body cygn-body" style="min-height: 134px">
        <div class="list" v-if="CYGNList && CYGNList.length > 0">
          <a
            href="javascript:;"
            class="item"
            v-for="(item, index) in CYGNList"
            :key="index"
            @click="OpenRes(item.Url, item.Name, item.ID, '常用功能')"
          >
            <img class="icon" :src="gnsc_img(item.IconUrl)" />
            <div :style="itemStyle.spanStyle" class="text" :id="item.ID" v-text="item.Alias"></div>
          </a>
        </div>
        <el-empty v-else description="数据为空" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as portal from '@/api/portal'
import * as PortalApi from '@/api/system/portal'
import { getFullUrl } from '@/layout/portal/admin'
//功能收藏图片
import gnsc_img1 from '@/assets/icons/portal/01.png'
import gnsc_img2 from '@/assets/icons/portal/02.png'
import gnsc_img3 from '@/assets/icons/portal/03.png'
import gnsc_img4 from '@/assets/icons/portal/04.png'
import gnsc_img5 from '@/assets/icons/portal/05.png'
import gnsc_img6 from '@/assets/icons/portal/06.png'
import gnsc_img7 from '@/assets/icons/portal/07.png'
import gnsc_img8 from '@/assets/icons/portal/08.png'
import gnsc_img9 from '@/assets/icons/portal/09.png'
import gnsc_img10 from '@/assets/icons/portal/10.png'
import { useUserStore } from '@/store/modules/user'
import { replaceUrl } from '@/assets/js/NK'
import { getAccessToken } from '@/utils/auth'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'CollectSc'
})

interface itemType {
  titleList: ''
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {}
}
const props = defineProps<{ itemJson: itemType }>()

const itemStyle = ref({
  modelStyle: '',
  titleStyle: '',
  spanStyle: ''
})

emitter.on('collect', (obj: itemType) => {
  init(obj)
})

//解除绑定事件
onUnmounted(() => {
  emitter.off('collect')
})

// 用户信息
const userStore = useUserStore()

//路径前缀
const baseUrl = import.meta.env.VITE_TOURL_PREFIX

const CYGNList = ref()

// 加载对象类型
const ItemTitle = ref({
  type: 'myfile',
  title: '功能收藏'
})
const defaultIconArray = [
  gnsc_img1,
  gnsc_img2,
  gnsc_img3,
  gnsc_img4,
  gnsc_img5,
  gnsc_img6,
  gnsc_img7,
  gnsc_img8,
  gnsc_img9,
  gnsc_img10
]

//常用入口
const loadCYGN = async () => {
  CYGNList.value = []
  const ret = await portal.gnsc_list({})

  CYGNList.value = ret

  CYGNList.value.forEach((item) => {
    var defaultIconIndex = 0
    //没有别名更改为名称
    if (item.Alias === null || item.Alias === '') {
      item.Alias = item.Name
    }
    //图标处理逻辑
    if (item.IconUrl === null) {
      if (defaultIconIndex === 10) {
        defaultIconIndex = 0
      }
      item.IconUrl = defaultIconArray[defaultIconIndex]
      defaultIconIndex += 1
    } else {
      item.IconUrl = baseUrl + '/BasicApplication/DownloadFile?FileID=' + item.IconUrl
    }
  })
  // console.log(CYGNList.value)
}

//打开菜单
const OpenRes = async (url, name, menuId, pageName) => {
  if (url.indexOf('?') > 0) url += '&'
  else url += '?'
  url = replaceUrl({ url: url })
  // 相对路径修改为绝对路径
  if (!url.startsWith('http')) {
    url = getFullUrl(url)
  }
  if (url.indexOf('http') == 0 && url.indexOf('token') == -1) {
    url += 'token=' + getAccessToken()
  }
  window.open(url)

  //记录功能日志
  const objdata = {
    workno: userStore.user.workNo,
    pagename: pageName,
    pageurl: window.location.href,
    funcname: name,
    funcurl: url,
    menuId: menuId
  }
  await PortalApi.FuncLog(objdata)
}
//功能收藏图片处理
const gnsc_img = (v) => {
  return v
}

//功能收藏更多
const CYGNModify = () => {
  let url =
    '/UIBuilder/UIViewer/EditerListViewer?TempletCode=EditerList_ad5d00e2e6d442b4a6bde149818a986e&token=' +
    getAccessToken()
  url = baseUrl + url
  window.open(url)
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    itemStyle.value = itemJson.itemStyle
  }
}

onMounted(() => {
  init(props.itemJson)
  loadCYGN()
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');
@import url('@/assets/css/admin.css');
</style>
