<script lang="ts" setup>
import {useAppStore} from '@/store/modules/app'
import {useDesign} from '@/hooks/web/useDesign'
import {CACHE_KEY, useCache} from '@/hooks/web/useCache'
import routerSearch from '@/components/RouterSearch/index.vue'

defineOptions({name: 'APP'})

const {getPrefixCls} = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const {wsCache} = useCache()

// 根据浏览器当前主题设置系统主题色
// const setDefaultTheme = () => {
//   let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
//   if (isDarkTheme === null) {
//     isDarkTheme = isDark()
//   }
//   appStore.setIsDark(isDarkTheme)
// }
// setDefaultTheme()
</script>
<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''"/>
    <routerSearch/>
  </ConfigGlobal>
</template>
<style lang="scss">
$prefix-cls: #{$namespace}-app;
html, body, b, ul, li, p, a, div, h1, h2, h3, h4, h5, h6, i {
  font-family: "Microsoft YaHei", "MicrosoftYaHei";
  color: #363636;
  text-decoration-style: soild;
  text-decoration-color: #363636;
  text-decoration-thickness: auto;
}
.size {
  width: 100%;
  height: 100%;
}

html,
body {
  @extend .size;

  padding: 0 !important;
  margin: 0;
  // overflow: hidden;

  ::-webkit-scrollbar {
    width: 5px;
  }

  ::-webkit-scrollbar-track {
    background: rgb(212, 212, 212);
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 6px;
  }

  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
</style>
