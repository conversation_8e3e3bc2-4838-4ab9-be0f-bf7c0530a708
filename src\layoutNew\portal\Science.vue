<template>
  <!--科技创新园地-->
  <div style="flex:1;">
    <my-panel height="auto" :title="'科技创新园地'" @more-click="moreClick">
      <template #default>
        <div class="gcs_tabitem" grp="zxzx_kjcxyd" atr="yfw">
          <div class="gcs_tab_kjcxyd_childs" cty="tab">
            <div class="gcs_tabhd">
              <ul v-cloak v-if="kjcxy && kjcxy.length > 0">
                <li
                  @click="tab3_index = index"
                  class="gcs_tabhd_item"
                  :class="index == tab3_index ? ' gcs_cur' : ''"
                  atr="item1"
                  v-for="(item, index) in kjcxy"
                  :key="index">
                  <span class="icon"></span>
                  <span class="active" v-text="item.CatalogName"></span>
                </li>
              </ul>
              <el-empty v-else description="数据为空"/>
            </div>
            <div class="gcs_tabbd">
              <div class="gcs_tabitem" v-if="kjcxy && kjcxy.length > 0" v-cloak>
                <div
                  class="gcs_newslist gcs_newslist_kjcx_dynamic"
                  v-for="(item, index) in kjcxy"
                  :key="index">
                  <templatete v-if="index == tab3_index">
                    <div
                      v-for="(i, idx) in item.list"
                      :key="idx"
                      class="gcs_newslist_item"
                      style="padding-right: 0;"
                      cid="77783"
                      :title="i.Title">
                      <div class="date">
                        <div class="day">{{
                            dateFormat(i.CreateTime, 'month')
                          }}-{{ dateFormat(i.CreateTime, 'day') }}
                        </div>
                        <div class="xgline">/</div>
                        <div class="year">
                          {{ dateFormat(i.CreateTime, 'year') }}
                        </div>
                      </div>
                      <span class="gcs_title bgtitle">
                            <a
                              :style="itemStyle.spanStyle"
                              :href="detail_url(item.Code, i.ID, item.CatalogName)"
                              target="_blank"
                              class="href1"
                              :title="i.Title"
                              v-text="i.Title">
                            </a>
                          </span>
                    </div>
                  </templatete>
                </div>
              </div>
              <el-empty v-else description="数据为空"/>
            </div>
          </div>
        </div>
      </template>
    </my-panel>
  </div>

</template>

<script setup lang="ts">
import {detail_url} from '@/layout/portal/admin'
import * as PortalApi from '@/api/system/portal'
import emitter from '@/utils/mitt'
import myPanel from "@/components/Panel/src/index.vue"

defineOptions({
  name: 'Science'
})

interface itemType {
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
    dateFormate: ''
  }
  isShow: {}
}

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {},
  dateFormate: 'YYYY-MM-DD'
})

const isShow = ref({})

const props = defineProps<{ itemJson: itemType }>()

const tab3_index = ref(0) //科技创新园tab切换索引

const kjcxy = ref()

emitter.on('science', (obj: itemType) => {
  init(obj)
})

//解除绑定事件
onUnmounted(() => {
  emitter.off('science')
})

//科技创新园地
const get_kjcxy = async () => {
  let result = await PortalApi.newsCenterGetTechnologicalInnovation({pageSize: 11})
  kjcxy.value = result
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
    if (itemJson.isShow) {
      isShow.value = itemJson.isShow
    }
  }
}

onMounted(() => {
  init(props.itemJson)
  get_kjcxy()
})
const moreClick = () => {
  window.open('/Portal/NewsCenter/PublicInfoList?code=' + kjcxy.value[tab3_index.value]['Code'], '_blank')
}
const dateFormat = (date: string, type: string) => {
  const d = new Date(date)
  if (type === 'year') {
    return d.getFullYear()
  }
  if (type === 'month') {
    return d.getMonth() > 8 ? d.getMonth() + 1 : '0' + (d.getMonth() + 1)
  }
  if (type === 'day') {
    return d.getDate() > 9 ? d.getDate() : '0' + d.getDate()
  }
  return d.getMonth() + 1 + '-' + d.getDate()
}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/admin.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/font-awesome.css');

.href2:hover, .href1:hover {
  color: rgb(60, 145, 214);
  cursor: pointer;
}

.date {
  display: flex;
  align-items: center;
  margin: 0;
  padding-right: 8px;

  .day {
    font-size: 16px;
    font-weight: 700;
    color: rgb(60, 145, 214);
  }

  .xgline {
    font-size: 14px;
    color: rgb(60, 145, 214);
    margin: 0px 4px;
  }

  .year {
    font-size: 14px;
    color: rgb(60, 145, 214);
  }
}

.gcs_cur {
  border-right: 0 !important;

  .active {
    border-bottom: 2px solid #0070ff;
    color: #0070ff;
  }
}

.gcs_tabhd_item:hover {
  .icon {
    background-color: #0070ff;
  }

  .active {
    color: #0070ff !important;
  }
}

.gcs_newslist_item {
  padding-left: 6px;

  span.gcs_title {
    flex: 1;
  }
}

.gcs_dt {
  display: inline-block;
  text-align: right;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */
  margin-left: 10px;
  font-size: 16px;
  font-weight: 700;
  color: rgb(60, 145, 214) !important;
}

.gcs_tab_kjcxyd_childs .gcs_tabhd {
  float: left;
  height: 520px;
  min-width: 260px;
}

.gcs_tabbd {
  background-color: #fff;
  height: 520px;
}

.gcs_title:hover {
  color: #000;
}

.gcs_title a {
  font-size: 16px;
  width: 300px;
  text-decoration: none;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: 3%;
}

.gcs_tabhd .icon {
  width: 6.5px;
  height: 6.5px;
  background-color: #d8ddec;
  margin-right: 10px;
  border-radius: 50%;
}
</style>
