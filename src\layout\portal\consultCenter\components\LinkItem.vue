<template>
  <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-ztzl">
    <div class="hbm-zxzx-row">
      <div class="hbm-zxzx-cell">
        <div
          class="gcs_tab_ztzl gcs_tab_zxzx_single"
          cty="tab"
          grp="zxzx_ztzl"
          v-for="title in titleList"
          :key="title.id"
        >
          <div class="gcs_tabhd" grp="zxzx_ztzl" v-if="title.id != undefined">
            <ul>
              <li :style="itemStyle.titleStyle" class="gcs_tabhd_item gcs_cur" atr="ztzl"
                >{{ title.title }}
                <div class="gcs_cur_inner"></div>
              </li>
            </ul>
          </div>
          <!-- 专题专栏 -->
          <div class="gcs_tabbd" grp="zxzx_ztzl" v-if="title.id == 'ZTZL'">
            <div class="gcs_tabitem" grp="zxzx_ztzl" atr="ztzl">
              <div></div>
              <div
                :style="{ background: `url('${link.background}') no-repeat` }"
                v-for="(link, index) in linkList"
                :key="index"
              >
                <a v-if="link.target == '_blank'" :style="itemStyle.spanStyle" :href="link.href" :target="link.target">{{
                  link.title
                }}</a>
                <router-link v-else :style="itemStyle.spanStyle" :to="link.href" >{{link.title}}</router-link>
              </div>
              <div></div>
            </div>
          </div>

          <!-- 快速入口 -->
          <div class="gcs_tabbd link" grp="zxzx_ksrk" v-if="title.id == 'KSRK'">
            <div class="list" v-cloak v-if="ksrk && ksrk.length > 0">
              <a
                :style="itemStyle.spanStyle"
                :href="_url(item.Url)"
                target="_blank"
                :title="item.Name"
                class="item"
                v-for="(item, index) in ksrk"
                :key="index"
              >
                <span class="icon"></span>
                <div>
                  <span class="text" v-text="item.Name"></span>
                  <span class="gcs_aaarow_flag">>></span>
                </div>
              </a>
            </div>
          </div>

          <!-- 友情链接 -->
          <div class="gcs_tabbd link" grp="zxzx_yqlj" v-if="title.id == 'YQLJ'">
            <div class="list" v-cloak v-if="yqlj && yqlj.length > 0">
              <a
                :style="itemStyle.spanStyle"
                :href="item.Url"
                target="_blank"
                :title="item.Name"
                class="item"
                v-for="(item, index) in yqlj"
                :key="index"
              >
                <span class="icon"></span>
                <div>
                  <span class="text" v-text="item.Name"></span>
                  <span class="gcs_aaarow_flag">>></span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import { getAccessToken } from '@/utils/auth'
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
import emitter from '@/utils/mitt'
defineOptions({
  name: 'LinkItem'
})

interface itemType {
  titleList: []
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {
    showZtzl: boolean
    showKsrk: boolean
    showYqlj: boolean
  }
}

const props = defineProps<{ itemJson: itemType }>()

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {}
})

const isShow = ref({})

// 标题列表
const titleList = ref([
  { id: 'ZTZL', title: '专题专栏', name: '专题专栏' },
  { id: 'KSRK', title: '快速入口', name: '快速入口' },
  { id: 'YQLJ', title: '友情链接', name: '友情链接' }
])

import zlghImg from '@/assets/imgs/zxzx/zlgh.png'
import szkmyImg from '@/assets/imgs/zxzx/szkmy.png'
import szyxtImg from '@/assets/imgs/zxzx/szyxt.png'
import yzxxImg from '@/assets/imgs/zxzx/yzxx.png'
import jlbgtImg from '@/assets/imgs/zxzx/jlbgt.png'
const linkList = ref([
  {
    title: '战略规划',
    background: zlghImg,
    href: 'http://10.10.1.173:8001/Portal/NewsCenter/PublicInfoList?code=ZLGH',
    target: '_blank'
  },
  {
    title: '数字昆明院',
    background: szkmyImg,
    href: 'http://10.10.1.28:9003/',
    target: '_blank'
  },
  {
    title: '市政院系统',
    background: szyxtImg,
    href: 'http://10.10.10.90:88',
    target: '_blank'
  },
  {
    title: '院长信箱',
    background: yzxxImg,
    href: 'http://10.10.1.29/yzxx/YZXX_List.asp',
    target: '_blank'
  },
  {
    title: '减利因素曝光台',
    background: jlbgtImg,
    href: '/Portal/NewsCenter/PublicInfoList?code=JLBGT',
    target: ''
  }
])
const ksrk = ref()
const yqlj = ref()

//快速入口
const get_ksrk = async () => {
  let result = await PortalApi.newsCenterGetLinks({ type: '快速入口', page: 1, pageSize: 10 })
  ksrk.value = result.records
}

//友情链接
const get_yqlj = async () => {
  let result = await PortalApi.newsCenterGetLinks({ type: '友情链接', page: 1, pageSize: 6 })
  yqlj.value = result.records
}

const _url = (url) => {
  if (url != null && url != '') {
    if (url.indexOf('http') == -1 && url.indexOf('https') == -1) {
      if (url.indexOf('/UIBuilder/UIViewer/') != -1) {
        url = baseUrl + url
      }
    }
    if (url.indexOf('ftp://') == -1) {
      //排除ftp链接
      if (url.indexOf('?') > 0) {
        url += '&' //如果已经有？号则拼接&符号
      } else {
        url += '?' //如果没有？号则拼接?符号
      }
      if (url.indexOf('token') == -1) {
        //最后拼接token
        url += 'token=' + getAccessToken()
      }
    }
  }
  return url
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    if (itemJson.titleList) {
      titleList.value = itemJson.titleList
    }
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
    if (itemJson.isShow) {
      isShow.value = itemJson.isShow
    }
  }
  get_ksrk()
  get_yqlj()
}

onMounted(() => {
  init(props.itemJson)
})

emitter.on('link', (obj: itemType) => {
  init(obj)
})
//解除绑定事件
onUnmounted(() => {
  emitter.off('link')
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/admin.css');
@import url('@/assets/css/font-awesome.css');

.gcs_tabitem {
  margin-left: 10px;
  height: auto;
  margin-right: 10px;
}

.gcs_tabbd {
  background-color: #ffffff;
  margin-bottom: 20px;
}
.hbm-zxzx-body-rightbaritem {
  background-color: #f9fafd;
}

.gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp='zxzx_ztzl'] div:before {
  content: '';
  display: table;
  clear: both;
}

.gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp='zxzx_ztzl'] div {
  height: auto;
  line-height: 56px;
  margin-bottom: 15px;
  text-align: left;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  padding-left: 63px;
  margin-left: 5px;
}

.gcs_tab_ztzl .gcs_tabbd .gcs_tabitem[grp='zxzx_ztzl'] div a {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  text-decoration: none;
}

.zxzx_ksrk .list {
  display: flex;
  flex-direction: column;
}

.link .list .item {
  height: auto;
  line-height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px 0 22px;
  color: #3c8cf1;
  text-decoration: none; /* 去除下划线 */
}
.link .list .item div {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.link .list .item:hover {
  background-color: #f4faff;
}
.link .list .item .text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 300px;
}
.link .list .item .icon {
  width: 5px;
  height: 5px;
  background-color: #3c8cf1;
  margin-right: 10px;
  border-radius: 50%;
}

.link .list .item .gcs_aaarow_flag {
  margin-left: 30px;
  width: 40px !important;
  margin-right: 10px;
}
</style>
