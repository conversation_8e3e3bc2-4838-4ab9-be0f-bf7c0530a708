<template>
  <el-row :gutter="20">
    <el-col :span="24" :xs="24">
      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item>
            <el-button @click="setMsgRed">选中标记为已读</el-button>
            <el-button @click="setAllMsgRed">全部标记为已读</el-button>
            <el-button @click="openReplyOrForward">回复</el-button>
            <el-button @click="openReplyOrForward">转发</el-button>
            <el-button @click="deleteMsg">删除</el-button>
            <el-button @click="openSendMsg">发送消息</el-button>
          </el-form-item>

          <el-form-item style="margin-left: 10px; float: right;">
            <el-input
              v-model="queryParams.params"
              placeholder="请输入发送人姓名、标题或附件"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
            <el-button style="margin-left: 10px" @click="resetQuery()">详细查询</el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table
          v-loading="loading"
          :data="list"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          ref="tableRef"
        >
          <el-table-column type="selection" width="55"/>
          <el-table-column
            label="发送人"
            align="center"
            prop="SenderName"
            :show-overflow-tooltip="true"
          >
            <template #default="{row}">
              <el-link type="primary" @click="openMsgInfo(row)">{{ row.SenderName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="发送时间"
            align="center"
            prop="SendTime"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="连接页面"
            align="center"
            prop="LinkUrl"
          />
          <el-table-column label="附件" align="center" prop="AttachFileIDs">
            <template #default="{row}">
              <el-link type="primary">{{ row.AttachFileIDs }}</el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="内容"
            align="center"
            prop="Content"
          >
            <template #default="{row}">
              <div v-html="row.Content"></div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
  <ReplyOrForward ref="replyOrForwardRef"/>
  <SendMsg ref="sendMsgRef"/>
  <MsgDetails ref="MsgDetailsRef"/>
  <el-dialog title="详细查询" v-model="dialogVisible">
    <el-row>
      <el-col :span="12">
        <el-form-item label="标题">
          <el-input v-model="queryParams.Title" clearable/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发送人">
          <el-input v-model="queryParams.SenderName" clearable/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="附件名">
          <el-input v-model="queryParams.AttachFileIDs" clearable/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发送时间">
          <el-date-picker
            v-model="queryParams.dataRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <div style="text-align: center;">
      <el-button @click="handleQuery" type="primary">查 询</el-button>
      <el-button @click="clearQuery" type="primary">清 空</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import * as msgApi from "@/api/portal/msgList";
import ReplyOrForward from "./replyOrForward.vue";
import MsgDetails from "./msgDetails.vue";
import SendMsg from "./sendMsg.vue";
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const tableRef = ref()
const replyOrForwardRef = ref();
const MsgDetailsRef = ref();
const sendMsgRef = ref();
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const dialogVisible = ref(false) // 详细查询的弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  params: '',
  Title: '',
  dataRange: '',
  SenderName: '',
  AttachFileIDs: '',
  SendTimeStart: '',
  SendTimeEnd: ''
})
const queryFormRef = ref() // 搜索的表单
//弹出搜索框
const resetQuery = () => {
  queryFormRef.value.resetFields()
  dialogVisible.value = true;
}
//清除搜索框内容
const clearQuery = () => {
  queryParams.Title = ''
  queryParams.SenderName = ''
  queryParams.dataRange = ''
  queryParams.AttachFileIDs = ''
  queryParams.SendTimeStart = ''
  queryParams.SendTimeEnd = ''
}
//详情页
const openMsgInfo = (row) => {
  MsgDetailsRef.value.open(row);
}
//发送消息
const openSendMsg = async () => {
  sendMsgRef.value.open();
}
//删除消息
const deleteMsg = async () => {
  const rows = tableRef.value.getSelectionRows()
  if (rows.length === 0) {
    message.warning('请先选择要删除的消息')
    return
  }
  await message.delConfirm()
  const ids = rows.map((item) => item.ReceiverID).toString();
  let params = {ids:ids}
  await msgApi.deleteMsg(params)
  message.success('删除成功')
  getList()
}
//回复，转发消息
const openReplyOrForward = () => {
  const row = tableRef.value.getSelectionRows()
  if (row.length === 0) {
    message.warning('请先选择要回复或转发的消息')
    return
  }
  replyOrForwardRef.value.open(row[0]);
}
//全部标记为已读
const setAllMsgRed = async () => {
  const ids = list.value.map((item) => item.ReceiverID).toString();
  let params = {
    ids: ids
  }
  await msgApi.setMsgRed(params)
  message.success('操作成功')
  getList()
}
//选中标记已读
const setMsgRed = async () => {
  const rows = tableRef.value.getSelectionRows()
  if (rows.length === 0) {
    message.warning('请先选择要标记的消息')
    return
  }
  const ids = rows.map((item) => item.ReceiverID).toString();
  let params = {ids:ids}
  await msgApi.setMsgRed(params)
  message.success('操作成功')
  getList()
}
const handleSelectionChange = (val) => {
  // 这部分代码是让复选框设置为单选
  val.map( (row,index) => {
    if(val.length <=1 ){
      return
    }
    // toggleRowSelection 用于多选表格，切换某一行的选中状态， 如果使用了第二个参数，则可直接设置这一行选中与否
    tableRef.value.toggleRowSelection(row,false)
    if(index === val.length-1){
      tableRef.value.toggleRowSelection(row,true)
    }
  })
}
const handleRowClick = (row) => {
  tableRef.value.toggleRowSelection(row);
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await msgApi.getMsgList(queryParams);
    list.value = data.records;
    total.value = data.total
  } finally {
    loading.value = false
    dialogVisible.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>
