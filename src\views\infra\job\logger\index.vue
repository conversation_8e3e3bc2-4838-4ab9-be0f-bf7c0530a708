<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="120px"
    >
      <!-- <el-form-item label="处理器的名字" prop="handlerName">
        <el-input v-model="queryParams.handlerName" placeholder="请输入处理器的名字" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item> -->
      <el-form-item label="开始执行时间" prop="beginTime">
        <el-date-picker
          v-model="queryParams.beginTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择开始执行时间"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="结束执行时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择结束执行时间"
          clearable
          :default-time="new Date('1 23:59:59')"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择任务状态"
          clearable
          class="!w-240px"
        >
          <el-option label="成功" value="1" />
          <el-option label="失败" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button type="success" plain @click="isDel = true" :loading="exportLoading"
          >删除日志
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="日志编号" align="center" prop="id" /> -->
      <!-- <el-table-column label="任务编号" align="center" prop="jobId" /> -->
      <el-table-column label="任务名称" align="center" prop="TaskName" />
      <el-table-column label="任务分组" align="center" prop="TaskGroup" />
      <el-table-column label="执行时长" align="center" prop="Duration">
        <template #default="scope">
          <span>{{ calculateTotalDuration(scope.row) + ' 毫秒' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" prop="Status">
        <template #default="scope">
          <el-tag
            :type="getExecutionStatus(scope.row) === '成功' ? 'success' : 'danger'"
            effect="light"
            style="width: 60px"
          >
            {{ getExecutionStatus(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" align="center" width="500px">
        <template #default="scope">
          <span>{{ formatDate(scope.row.CreateTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="openDetail(scope.row)"
            v-hasPermi="['infra:job:query']"
          >
            详细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <el-dialog v-model="isDel" title="选择删除日期" width="40%" style="margin-top: 10%">
    <el-form-item label="日期范围">
      <div class="block">
        <el-date-picker
          v-model="value1"
          type="daterange"
          range-separator="到"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :size="'default'"
        />
      </div>
    </el-form-item>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="isDel = false">取消</el-button>
        <el-button type="primary" @click="handelDelete"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 表单弹窗：查看 -->
  <JobLogDetail ref="detailRef" />
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import JobLogDetail from './JobLogDetail.vue'
import * as JobLogApi from '@/api/infra/jobLog'

defineOptions({ name: 'InfraJobLog' })
const isDel = ref(false)
const message = useMessage() // 消息弹窗
const { query } = useRoute() // 查询参数
const value1 = ref('')
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  jobId: query.id,
  handlerName: undefined,
  beginTime: undefined,
  endTime: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/**
 * 计算任务总运行时间
 * 从Result字段中解析ApiSQL和ApiUrl的运行时间并相加
 */
const calculateTotalDuration = (row: any) => {
  try {
    // 如果Result字段不存在或为空，返回原始Duration
    if (!row.Result) {
      return row.Duration || 0
    }

    // 解析Result字段
    const result = JSON.parse(row.Result)
    let totalDuration = 0

    // 解析ApiSQL的运行时间
    if (result.ApiSQL) {
      const apiSQLArray = JSON.parse(result.ApiSQL)
      apiSQLArray.forEach((api) => {
        if (api.endTime && api.beginTime) {
          totalDuration += api.endTime - api.beginTime
        }
      })
      console.log('apiSQL==================>', apiSQLArray)
    }

    // 解析ApiUrl的运行时间
    if (result.ApiUrl) {
      const apiUrlArray = JSON.parse(result.ApiUrl)
      apiUrlArray.forEach((api) => {
        if (api.endTime && api.beginTime) {
          totalDuration += api.endTime - api.beginTime
        }
      })
      console.log('apiUrl==================>', apiUrlArray)
    }

    // 如果计算出的总时间为0，返回原始Duration
    return totalDuration > 0 ? totalDuration : row.Duration || 0
  } catch (error) {
    console.error('解析运行时间失败:', error)
    // 解析失败时返回原始Duration
    return row.Duration || 0
  }
}

/**
 * 获取任务执行状态
 * 根据ApiSQL和ApiUrl的status属性判断执行状态
 * 如果都为success则显示"成功"，否则显示"失败"
 */
const getExecutionStatus = (row: any) => {
  try {
    // 如果Result字段不存在或为空，使用原始Status判断
    if (!row.Result) {
      return row.Status == '1' ? '成功' : '失败'
    }

    // 解析Result字段
    const result = JSON.parse(row.Result)
    let allSuccess = true

    // 检查ApiSQL的status
    if (result.ApiSQL) {
      const apiSQLArray = JSON.parse(result.ApiSQL)
      for (const api of apiSQLArray) {
        if (api.status !== 'success') {
          allSuccess = false
          break
        }
      }
    }

    // 检查ApiUrl的status（只有在ApiSQL都成功的情况下才检查）
    if (allSuccess && result.ApiUrl) {
      const apiUrlArray = JSON.parse(result.ApiUrl)
      for (const api of apiUrlArray) {
        if (api.status !== 'success') {
          allSuccess = false
          break
        }
      }
    }

    return allSuccess ? '成功' : '失败'
  } catch (error) {
    console.error('解析执行状态失败:', error)
    // 解析失败时使用原始Status判断
    return row.Status == '1' ? '成功' : '失败'
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const pageData = await JobLogApi.getJobLogPage(queryParams)
    list.value = pageData.data
    total.value = pageData.total 
  } catch (error) {
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看操作 */
const detailRef = ref()
const openDetail = (row) => {
  detailRef.value.open(row)
}
// 处理删除
const handelDelete = async () => {
  isDel.value = false
  const ret = await JobLogApi.delJobLogs({
    jobId: query.id,
    startTime: formatDate(new Date(value1.value[0])),
    endTime: formatDate(new Date(value1.value[1]))
  })
  if (ret) {
    useMessage().success('操作成功')
  }
  getList()
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
