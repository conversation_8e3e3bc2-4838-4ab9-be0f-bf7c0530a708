<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-form-item label="标题显示" />
        </el-col>
        <el-col :span="18">
          <el-form-item>
            <el-select
              v-model="value"
              multiple
              placeholder="请选择"
              clearable
              @change="titleChange"
            >
              <el-option
                v-for="item in titleList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.titleStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.spanStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">日期格式</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="form.itemStyle.dateFormate" placeholder="请选择" clearable>
            <el-option
              v-for="item in dateOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { dateOption } from '@/utils/formatTime'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'FinaceForm'
})

const titleList = ref([
  { id: 'CWZJ', title: '财务资金', name: '财务资金', data: ref() },
  { id: 'SWGL', title: '税务管理', name: '税务管理', data: ref() },
  { id: 'ZCYCQ', title: '资产与产权管理', name: '资产与产权管理', data: ref() }
])
const value = ref(['CWZJ', 'SWGL', 'ZCYCQ'])

const form = ref({
  titleList: titleList.value,
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: '',
    dateFormate: ''
  },
  isShow: {}
})

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('finance', form.value)
  },
  { immediate: true, deep: true }
)

defineExpose({ form })

const titleChange = () => {
  let data = titleList.value.filter((item) => value.value.indexOf(item.id) !== -1)
  form.value.titleList = data
}
const setValue = () => {
  value.value = form.value.titleList.map((item) => item.id)
}
const props = defineProps<{ itemJson: any }>()
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue
})
</script>
