import $ from 'jquery'
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'

const userStore = useUserStore()

var _token; // 该项目及以下master已经作了集成登录，系统会在登录后自动赋值 具体参见masterattach.js
var _userid;
var _workno;
var _officename;
var _deptid;
var _deptname;
var _curinfo = {};
// KDAPI 地址(资讯中心后台  项目中心后台)
var serverroot00 = "http://***********:8097/"; //"http://localhost:22141/";//"http://localhost:8090/";//
// 173 菜单Tab功能页root url
var serverroot03 = "http://***********:8001"; // 默认OA系统地址
//
var _cmaj = function (
    adom,
    type,
    url,
    data,
    headdata,
    contenttype,
    successfn,
    errorfn
) {
    if (!adom) {
        adom = $("body").get(0);
    }
    if (!data) {
        data = "";
    }
    if (!contenttype) {
        contenttype = "application/x-www-form-urlencoded";
    }
    var $_loading = $(adom).find(".gcs_loading");

    if ($_loading.length == 0) {
        $(adom).append(
            "<div class='gcs_loading'><div class='gcs_loading_log'><p></p></div></div>"
        );
    }
    $_loading = $(adom).find(".gcs_loading");
    var _w = $(adom).width();
    var _h = $(adom).height();
    if (_w > 60) {
        _w = 60;
    }
    if (_h > 60) {
        _h = 60;
    }
    if (_w <= 60) {
        $_loading.find(".gcs_loading_log>p").css("width", "100%");
    }
    if (_h <= 60) {
        $_loading.find(".gcs_loading_log>p").css("height", "100%");
    }
    $_loading.css({ "background-size": _w + "px " + _h + "px" });
    $_loading.show();
    var _ajdata = {
        type: type,
        url: url,
        data: data,
        contentType: contenttype,
        success: function (dat) {
            $_loading.remove();
            if (successfn) {
                successfn(dat);
            }
        },
        error: function (err) {
            console.log(err);
            $_loading.find(".gcs_loading_log>p").html(err);
            $_loading.find(".gcs_loading_log").show();

            setTimeout(function () {
                $_loading.remove();
            }, 2000);
            if (errorfn) {
                errorfn(err);
            }
        },
    };
    if (headdata) {
        _ajdata["beforeSend"] = function (xhr) {
            for (var i in headdata) {
                var _key = i;
                var _val = headdata[i];
                xhr.setRequestHeader(_key, _val);
            }
        };
    }
    $.ajax(_ajdata);
};
var _urladdparam = function (url, pmname, pmvalue) {
    if (url.indexOf(pmname + "=") > -1) {
        // do nothing
        return url;
    } else {
        if (url.indexOf("?") > -1) {
            return url + "&" + pmname + "=" + pmvalue;
        } else {
            return url + "?" + pmname + "=" + pmvalue;
        }
    }
};
var _getquerystring = function (name) {
    //var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    //var r = window.location.search.substr(1).match(reg);
    //if (r != null) return unescape(r[2]); return null;
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    //window.location.replaceurlStr.replace(/%/g, '%25');
    var r = window.location.search.substr(1).match(reg);
    if (arguments.length > 1) {
        if (arguments[1]) {
            var _urlarr = arguments[1].split("?");
            if (_urlarr.length > 1) {
                var _search = _urlarr[1];
                r = _search.substr(1).match(reg);
            }
        }
    }
    if (r != null) return decodeURI(r[2]);
    return null;
};
String.prototype.endWith = function (str) {
    if (
        str == null ||
        str == "" ||
        this.length == 0 ||
        str.length > this.length
    )
        return false;
    if (this.substring(this.length - str.length) == str) return true;
    else return false;
    return true;
};
export const masterJS = !(function (w) {
    var clsRoot = "cont";
    var clsMasterCtrl = "master-ctrl";
    var domTagHead = "header";
    var clsHeadSel = "." + clsRoot + " " + domTagHead;
    var clsHeadControlSel =
        "." + clsRoot + " " + domTagHead + " .header-control";

    var clsUserCenterPopSel = ".hbm-user-center";
    var clsBalloonSel = ".hbm-balloon";
    var clsBody = "body";
    var domTagFooter = "footer";
    var clsBodyLeft = "body-left";
    var clsBodyLeftMenu = "body-left-menu";
    var clsBodyContentJqsel = ".cont .body .body-content";
    var clsTpmenuLeftSel = "header .sys-tpmenu .sys-tpmenu-left";

    var clsMenuitem = "hbm-menu-item";
    var clsMenus = "hbm-menus";
    var clsBodyIndexSel = "." + clsRoot + " ." + clsBody + " .body-index";

    var clsTab = "body-page-tab";
    var clsTabhd = "body-page-tab-hd";
    var clsTabhdSSel = "." + clsTabhd;
    var clsTabPagehdItem = "page-hd";
    var clsTabPagehdItemSSel = "." + clsTabPagehdItem;
    var clsTabbd = "body-page-tab-bd";
    var clsTabbdSSel = "." + clsTabbd;
    var clsPageTabSel =
        ".cont .body .body-content .body-content-main ." + clsTab;
    var clsPageTabhdSel = clsPageTabSel + " ." + clsTabhd;
    var clsPageTabbdSel = clsPageTabSel + " ." + clsTabbd;
    var clsPageTabItemClose = "tab-close";
    var clsPageTabitemCloseCustomSel =
        "." + clsTabhd + " ." + clsPageTabItemClose;

    var clsMasterCtrlJqsel = "." + clsMasterCtrl;
    var clsBodyLeftJqsel = "." + clsBodyLeft;

    var clsAdDockerSel = "." + clsRoot + " .hbm-ad-docker";
    var clsAdDockerItemSel = "." + clsRoot + " .hbm-ad-docker-item";

    var clsDockerYw = "hbm-ad-docker-ywlist";
    var cmpxDockerLxyw = "hbm-ad-docker-item[atr='lxyw']";

    var pagedialogsel = "#hbm-page-window";

    var httypeattr = "ht";
    var httypeobj = "htobj";
    var dialogmaskobj = "dlgmask";
    var dlgzindex = 100;
    var dlgoriinfo = "dlgoriinfo";
    var hts = [
        { name: "hbmtab", value: "HbmTab" },
        {
            name: "hbmballoon",
            value: "HbmBalloon",
        },
        { name: "hbmmenus", value: "HbmMenus" },
        { name: "hbmwindow", value: "HbmWindow" },
    ];
    //
    var index_mode = 0; //0 : index  1:itempage
    var addZindex = function () {
        dlgzindex += 10;
        return [dlgzindex, dlgzindex + 1];
    };
    var subZindex = function () {
        dlgzindex -= 10;
        return [dlgzindex, dlgzindex + 1];
    };
    var _IsUrl = function (str) {
        var _reg = /(^http[s]?:\/\/)|(^\/\w+[.]?)|(^\w+\/\w+)|(^\w+[.])/g;
        return _reg.test(str);
    };
    var _IsAbsUrl = function (str) {
        var _reg = /^http[s]?:\/\//g;
        return _reg.test(str);
    };

    var getElCoordinate = function (dom) {
        var t = dom.offsetTop;
        var l = dom.offsetLeft;
        if (dom.offsetParent) {
            dom = dom.offsetParent;
        }
        var _w = $(dom).get(0).offsetWidth;
        var _h = $(dom).get(0).offsetHeight;
        while (dom) {
            t += dom.offsetTop;
            l += dom.offsetLeft;
            dom = dom.offsetParent;
        }
        return { top: t, left: l, width: _w, height: _h };
    };
    var changeindexmode = function (mod) {
        if (mod == 0) {
            // $(clsBodyLeftJqsel).hide();
            $(clsBodyContentJqsel).hide();
            $(clsBodyIndexSel).show();
        }
        if (mod == 1) {
            // $(clsBodyLeftJqsel).show();
            $(clsBodyIndexSel).hide();
            $(clsBodyContentJqsel).show();
            resizeme();
        }
    };
    var getOriElCoordinate = function (dom) {
        if (!dom) {
            return {};
        }
        var t = dom.offsetTop;
        var l = dom.offsetLeft;
        var _w = $(dom).get(0).offsetWidth;
        var _h = $(dom).get(0).offsetHeight;

        var _x = l;
        var _y = t;
        return { left: _x, top: _y, width: _w, height: _h };
    };
    var mousePosition = function (ev) {
        if (!ev) ev = window.event;
        if (ev.pageX || ev.pageY) {
            return { x: ev.pageX, y: ev.pageY };
        }
        return {
            x:
                ev.clientX +
                document.documentElement.scrollLeft -
                document.body.clientLeft,
            y:
                ev.clientY +
                document.documentElement.scrollTop -
                document.body.clientTop,
        };
    };
    var readOnly = function (dom) {
        $(dom).attr({
            unselectable: "on",
            onselectstart: "return false",
            onselect: "document.selection.empty()",
        });
    };
    var ExecTypeNewWinArr = ["OpenWindow", "newwin"];
    var ExecTypeCommon = [""];
    var ExecTypeDialog = ["dialog"];
    // var ExecTypeButtonIframe = []
    var _GetExecType = function (exectype) {
        if (!exectype) {
            return 0;
        }
        var rtn = 0;
        for (var i = 0; i < ExecTypeDialog.length; i++) {
            if (exectype == ExecTypeDialog[i]) {
                return 2;
            }
        }
        for (var i = 0; i < ExecTypeNewWinArr.length; i++) {
            if (exectype == ExecTypeNewWinArr[i]) {
                return 1;
            }
        }
        for (var i = 0; i < ExecTypeCommon.length; i++) {
            if (exectype == ExecTypeCommon[i]) {
                return 0;
            }
        }
        return 0;
    };
    //
    $(function () {
        $(clsMasterCtrlJqsel).on("click", function () {
            $(clsBodyLeftJqsel).toggleClass("body-left-hide");
            $(clsMasterCtrlJqsel + " i").toggleClass("fa-indent fa-outdent");
            $(clsMasterCtrlJqsel).toggleClass("master-ctrl-in master-ctrl-out");
            $(clsBodyContentJqsel).toggleClass(
                "body-content-fill body-content-dft"
            );
            $(clsTpmenuLeftSel).toggleClass("sys-tpmenu-out sys-tpmenu-in");
        });
        //
        window.onresize = function () {
            resizeme();
        };
        resizeme();
        //
        var master = new HbmMaster();
        master.init();
        w.hbmaster = master;
        //
        buildct();
    });

    var buildct = function () {
        for (var i = 0; i < hts.length; i++) {
            $("body")
                .find("[" + httypeattr + "='" + hts[i]["name"] + "']")
                .each(function () {
                    var _build = $(this).data("build");
                    if (_build) {
                        return true;
                    }

                    $(this).data("build", true);
                    var _val = hts[i]["value"];
                    var _valobj = eval(" new " + hts[i]["value"] + "(this)");
                    _valobj.init();
                    //
                    $(this).data(httypeobj, _valobj);
                });
        }
    };
    var resizeme = function () { 
        var _hh = $(domTagHead).outerHeight();
        var _bth = $(domTagFooter).outerHeight();
        var _wh = $(window).height();
        var _bdh = _wh - _hh;
        var _tbhh = $(clsPageTabhdSel).outerHeight();
        var _tbbdh = _bdh - _tbhh;
        // $("." + clsBody).height(2 * _bdh);
        $(clsBodyContentJqsel + " .body-content-main").height(_bdh - _bth);
        $(clsPageTabbdSel).height(_tbbdh);
        $(clsBodyIndexSel).height(_bdh);

        // $("." + clsBody).css("min-height",_bdh + "px");
        // $(clsBodyContentJqsel + " .body-content-main").css("min-height",(_bdh - _bth) + "px");
        // $(clsPageTabbdSel).css("min-height",_tbbdh);
        // $(clsBodyIndexSel).css("min-height",_bdh);
    };
    var _correctoaurl = function (url) {
        if (
            url.indexOf("token=") < 0 ||
            url.toLowerCase().indexOf("{token}") < 0
        ) {
            url +=
                url.indexOf("?") > -1
                    ? "&token=" + getAccessToken()
                    : "?token=" + getAccessToken();
        }
        var _setting = { url: url };
        replaceUrl(_setting);
        return _setting["url"];
    };
    var tabpageoptions = { cur: "home" };
    var HbmItempageTab = function (dom, options) {
        this.cont = dom;
        var options = options;
        this.init = function (dom) {
            if (dom) {
                cont = dom;
            }
            $(dom).addClass(clsTab);
            if ($(dom).find("." + clsTabhd).length == 0) {
                $(dom).append('<div class="' + clsTabhd + '"><ul></ul></div>');
            }
            if ($(dom).find("." + clsTabbd).length == 0) {
                $(dom).append('<div class="' + clsTabbd + '"></div>');
            }
            if (arguments.length > 1 && arguments[1]) {
                options = arguments[1];
            }
            //
            this.eventbind();
        };
    };
    HbmItempageTab.prototype = {
        // pageinfo: {"code":"","name":"","ico":"","page":{"url":"","content":""}}
        add: function (pageinfo) {
            try {
                var code = "";
                var name = "";
                var content = "";
                var url = "";
                var control = false;
                var active = false;
                var ico = "file-o";
                if (typeof pageinfo == "object") {
                    code = pageinfo["code"];
                    name = pageinfo["name"];
                    content = pageinfo["page"]["content"];
                    url = pageinfo["page"]["url"];
                    if (arguments.length > 1) {
                        control = arguments[1];
                    }
                    if (arguments.length > 2) {
                        active = arguments[2];
                    }
                    if (arguments.length > 3) {
                        ico = arguments[3];
                    }
                } else {
                    code = arguments[0];
                    name = arguments[1];
                    content = arguments[2];
                    if (arguments.length > 3) {
                        control = arguments[3];
                    }
                    if (arguments.length > 4) {
                        active = arguments[4];
                    }
                    if (arguments.length > 5 && arguments[5]) {
                        ico = arguments[5];
                    }
                    if (_IsUrl(content)) {
                        if (_IsAbsUrl(content)) {
                            url = content;
                            content = "";
                        } else {
                            var _reg = /^\//g;
                            url = _reg.test(content)
                                ? serverroot03 + content
                                : serverroot03 + "/" + content;
                            content = "";
                        }
                    }
                }
                if (code && name) {
                    if (
                        $(this.cont).find(
                            "." + clsTabhd + " li[code='" + code + "']"
                        ).length == 0
                    ) {
                        var controlstr = "";
                        if (control) {
                            controlstr =
                                '<span class="tab-close fa fa-close"></span>';
                        }
                        $(this.cont)
                            .find("." + clsTabhd + " ul")
                            .append(
                                '<li code="' +
                                    code +
                                    '"><div class="page-hd"><span class="page-ico fa fa-' +
                                    ico +
                                    '"><i></i></span><span class="itempage-title" title="' +
                                    name +
                                    '">' +
                                    name +
                                    "</span>" +
                                    controlstr +
                                    "</div></li>"
                            );
                    }
                    if (
                        $(this.cont).find(
                            "." + clsTabbd + " div[code='" + code + "']"
                        ).length == 0
                    ) {
                        if (url) {
                            url +=
                                url.indexOf("?") > -1
                                    ? "&token=" + _token
                                    : "?token=" + _token;
                            $(this.cont)
                                .find("." + clsTabbd)
                                .append(
                                    '<div class="body-page-content" code="' +
                                        code +
                                        "\"><iframe src='" +
                                        url +
                                        "'></iframe></div>"
                                );
                        } else {
                            $(this.cont)
                                .find("." + clsTabbd)
                                .append(
                                    '<div class="body-page-content" code="' +
                                        code +
                                        '">' +
                                        content +
                                        "</div>"
                                );
                        }
                    }
                    if (active) {
                        this.active(code);
                    }
                }
            } catch (e) {
                // throw e;
                console.log(e);
            }
        },
        showindex: function () {
            $(clsBodyContentJqsel).hide();
            $(clsBodyIndexSel).show();
        },
        remove: function (code, activecode) {
            var _tis = this;
            var dom = this.cont;
            //
            $(dom)
                .find("." + clsTabhd + " li[code='" + code + "']")
                .remove();
            $(dom)
                .find("." + clsTabbd + " div[code='" + code + "']")
                .remove();

            if (activecode) {
                this.active(activecode, true);
            } else {
                if ($(dom).find("." + clsTabhd + " li.page-cur").length == 0) {
                    var _lastcode = $(dom)
                        .find("." + clsTabhd + " ul")
                        .children("li:last-child")
                        .attr("code");
                    if (_lastcode) {
                        _tis.active(_lastcode, true);
                    } else {
                        _tis.showindex();
                    }
                }
            }
        },
        active: function (code, focus) {
            var dom = this.cont;
            //
            var $hddom = $(dom).find(clsTabhdSSel + " li[code='" + code + "']");
            if (!$hddom.hasClass("page-cur")) {
                $(dom)
                    .find(clsTabhdSSel + " li[code]")
                    .removeClass("page-cur");
                $hddom.addClass("page-cur");
                //
                var $bddom = $(dom).find(
                    clsTabbdSSel + " div[code='" + code + "']"
                );
                $(dom)
                    .find(clsTabbdSSel + " div[code].page-cur")
                    .removeClass("page-cur");
                $bddom.addClass("page-cur");
            }
            masterLeftMenu.showcurview(code, focus);
        },
        eventbind: function () {
            var _tis = this;
            var dom = this.cont;
            $(dom).delegate(clsTabPagehdItemSSel, "click", function () {
                var _code = $(this).closest("li").attr("code");

                _tis.active(_code, true);
            });

            $(dom).delegate(clsPageTabitemCloseCustomSel, "click", function (
                e
            ) {
                var ev = e || window.event;
                var _code = $(this).closest("li").attr("code");
                //
                _tis.remove(_code);
                //
                ev.stopPropagation();
            });
        },
    };

    var HbmTab = function (dom) {
        this.dom = dom;
        this.init = function () {
            //
            this.eventbind();
        };
    };
    HbmTab.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _grp = $(_tis.dom).attr("grp");
            var _selor = _grp
                ? ".hbm-tabhd[grp='" + _grp + "'] li.hbm-tabhd-item"
                : ".hbm-tabhd li.hbm-tabhd-item";
            $(_selor).bind("click", function () {
                var _code = $(this).attr("code");
                _tis.setcur(_code);
            });
        },
        setcur: function (code) {
            var _tis = this;
            var _dom = _tis.dom;
            var _code = code;
            var $_hdcurselor;
            var $_bdcurselor;
            //
            var $_hdallselor;
            var $_bdallselor;
            var _grp = $(_dom).attr("grp");
            var _hdallselorstr = _grp
                ? ".hbm-tabhd[grp='" + _grp + "'] li.hbm-tabhd-item"
                : ".hbm-tabhd li.hbm-tabhd-item.hbm-hdcur";
            $_hdallselor = $(_dom).find(_hdallselorstr);
            //
            $_bdallselor = _grp
                ? $(_dom).find(".hbm-tabbd .hbm-tabbditem[grp='" + _grp + "']")
                : $(_tis.dom).find(".hbm-tabbd .hbm-tabbditem.hbm-cur");
            if (!_code) {
                $_hdcurselor = _grp
                    ? $(_dom)
                          .find(
                              ".hbm-tabhd[grp='" +
                                  _grp +
                                  "'] li.hbm-tabhd-item.hbm-hdcur"
                          )
                          .first()
                    : $(_dom)
                          .find(".hbm-tabhd li.hbm-tabhd-item.hbm-hdcur")
                          .first();
                if ($_hdcurselor.length == 0) {
                    $_hdcurselor = _grp
                        ? $(_dom)
                              .find(
                                  ".hbm-tabhd[grp='" +
                                      _grp +
                                      "'] li.hbm-tabhd-item"
                              )
                              .first()
                        : $(_dom)
                              .find(".hbm-tabhd li.hbm-tabhd-item.hbm-hdcur")
                              .first();
                }
                //
                _code = $_hdcurselor.attr("code");
            }
            //
            else {
                $_hdcurselor = _grp
                    ? $(_dom)
                          .find(
                              ".hbm-tabhd[grp='" +
                                  _grp +
                                  "'] li.hbm-tabhd-item[code='" +
                                  _code +
                                  "']"
                          )
                          .first()
                    : $(_dom)
                          .find(
                              ".hbm-tabhd li.hbm-tabhd-item[code='" +
                                  _code +
                                  "']"
                          )
                          .first();
            }
            $_bdcurselor = _grp
                ? $(_dom)
                      .find(
                          ".hbm-tabbd .hbm-tabbditem[grp='" +
                              _grp +
                              "'][code='" +
                              _code +
                              "']"
                      )
                      .first()
                : $(_dom)
                      .find(".hbm-tabbd .hbm-tabbditem[code='" + _code + "']")
                      .first();
            //
            $_hdallselor.removeClass("hbm-hdcur");
            $_bdallselor.removeClass("hbm-cur");
            //
            $_hdcurselor.addClass("hbm-hdcur");
            $_bdcurselor.addClass("hbm-cur");
        },
        getcur: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _grp = $(_dom).attr("grp");
            var $_hdcurselor;
            var $_bdcurselor;

            $_hdcurselor = _grp
                ? $(_dom)
                      .find(
                          ".hbm-tabhd[grp='" +
                              _grp +
                              "'] li.hbm-tabhd-item.hbm-hdcur"
                      )
                      .first()
                : $(_dom)
                      .find(".hbm-tabhd li.hbm-tabhd-item.hbm-hdcur")
                      .first();
            $_bdcurselor = _grp
                ? $(_dom)
                      .find(
                          ".hbm-tabbd .hbm-tabbditem[grp='" +
                              _grp +
                              "'].hbm-cur"
                      )
                      .first()
                : $(_dom).find(".hbm-tabbd .hbm-tabbditem.hbm-cur").first();
            //
            if ($_hdcurselor.length == 0) {
                return null;
            }
            var _code = $_hdcurselor.attr("code");
            return _code;
        },
    };
    var _menuicomap = {
        "icon-setting": "cog",
        "icon-role": "users",
        "icon-dept": "sitemap",
        "icon-set": "suitcase",
        "icon-user": "user-o",
    };
    var HbmLeftMenu = function () {
        this.dom;
        this.eventselected = null;
        this.init = function (dom, data) {
            var _tis = this;
            //
            _tis.dom = dom;
            $(dom).addClass(clsBodyLeftMenu);
            if (data) {
                _tis.load(data, dom);
            }
            //
            _tis.eventbind();
        };

        this.pos = 0;
        this.data = null;
    };
    HbmLeftMenu.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = _tis.dom;
            $("." + clsBodyLeftMenu).delegate(
                ".left-menu-item",
                "click",
                function (e) {
                    var ev = e || window.event;
                    var tar = ev.target;
                    if (
                        $(tar).hasClass("left-menu-expandico") ||
                        $(tar).hasClass("left-menu-noexpandico")
                    ) {
                        // do nothing
                    } else {
                        tar = this;
                        if (
                            $(tar).find(".left-menu-expandico").length > 0 ||
                            $(tar).find(".left-menu-noexpandico").length > 0
                        ) {
                            tar =
                                $(tar).find(".left-menu-expandico").length > 0
                                    ? $(tar).find(".left-menu-expandico").get(0)
                                    : $(tar).find(".left-menu-noexpandico")
                                          .length > 0
                                    ? $(tar)
                                          .find(".left-menu-noexpandico")
                                          .get(0)
                                    : null;
                        }
                    }
                    if (
                        $(tar).hasClass("left-menu-expandico") ||
                        $(tar).hasClass("left-menu-noexpandico")
                    ) {
                        if ($(tar).hasClass("left-menu-expandico")) {
                            _tis.showsubitems(this, false, false);
                        } else {
                            _tis.showsubitems(this, true, true);
                        }
                        ev.stopPropagation();
                    } else {
                        if (_tis.eventselected) {
                            var _code = $(this).attr("code");
                            var _thedata = _tis.getdata(_code);
                            ev.hbmdata = _thedata;
                            _tis.eventselected(ev);
                        }
                    }
                    _tis.showcurview(_code);
                }
            );
            //
        },
        getdata: function (code, data) {
            var _tis = this;
            var _dom = _tis.dom;
            var _data =
                data && data.length > 0 ? data : $(_dom).data("menusdt");
            var _cfg = $(_dom).data("menuscfg");
            //
            for (var i = 0; i < _data.length; i++) {
                var _code = _data[i][_cfg["codefld"]];
                if (_code && code == _code) {
                    return {
                        code: _data[i][_cfg["codefld"]],
                        name: _data[i][_cfg["namefld"]],
                        content: _data[i][_cfg["contentfld"]],
                        url: _data[i][_cfg["urlfld"]],
                        triggertype: _data[i][_cfg["triggertypefld"]],
                    };
                } else {
                    if (
                        _data[i][_cfg["childrenfld"]] &&
                        _data[i][_cfg["childrenfld"]].length > 0
                    ) {
                        var _rtn = _tis.getdata(
                            code,
                            _data[i][_cfg["childrenfld"]]
                        );
                        if (_rtn) {
                            return _rtn;
                        }
                    }
                }
            }
        },
        showsubitems: function (menuitemdom, show, showonly) {
            if (!$(menuitemdom).hasClass("left-menu-item")) {
                return;
            }
            if (show) {
                if (showonly) {
                    // $(menuitemdom).closest("ul").find(".left-menu-subitems").hide();
                    // $(menuitemdom).closest("ul").find(".left-menu-subitems").prev().children("span:eq(2)").addClass("left-menu-noexpandico").removeClass("left-menu-expandico");
                }
                $(menuitemdom)
                    .find(".left-menu-item-inner")
                    .children("span:last-child")
                    .removeClass("left-menu-noexpandico");
                $(menuitemdom)
                    .find(".left-menu-item-inner")
                    .children("span:last-child")
                    .addClass("left-menu-expandico");
                if ($(menuitemdom).next().hasClass("left-menu-subitems")) {
                    $(menuitemdom).next().show();
                }
            } else {
                $(menuitemdom)
                    .find(".left-menu-item-inner")
                    .children("span:last-child")
                    .removeClass("left-menu-expandico");
                $(menuitemdom)
                    .find(".left-menu-item-inner")
                    .children("span:last-child")
                    .addClass("left-menu-noexpandico");
                if ($(menuitemdom).next().hasClass("left-menu-subitems")) {
                    $(menuitemdom).next().hide();
                }
            }
        },
        getmenustr: function (data, root) {
            var _tis = this;
            var rtn = root ? '<ul class="left-menu-root">' : "<ul>";
            var _settings =
                arguments.length > 2 && arguments[2]
                    ? arguments[2]
                    : {
                          codefld: "code",
                          namefld: "name",
                          contentfld: "content",
                          urlfld: "url",
                          trigertypefld: "ttype",
                          icofld: "ico",
                          childrenfld: "children",
                      };
            //
            if (data) {
                for (var i = 0; i < data.length; i++) {
                    var _code = data[i][_settings["codefld"]];
                    var _name = data[i][_settings["namefld"]];
                    var _content = data[i][_settings["contentfld"]];
                    var _url = data[i][_settings["urlfld"]];
                    var _triggertype = data[i][_settings["triggertypefld"]];
                    var _ico = data[i][_settings["icofld"]];
                    var _children = data[i][_settings["childrenfld"]];
                    _ico = _ico
                        ? _menuicomap[_ico]
                            ? _menuicomap[_ico]
                            : _ico
                        : _children && _children.length > 0
                        ? "folder-o"
                        : "file-o";
                    var _icostr = "";
                    if (_ico) {
                        _icostr = " fa fa-" + _ico;
                    }
                    var _spacestr = "";
                    for (var j = 0; j < _tis.pos; j++) {
                        _spacestr += '<span class="left-menu-space"></span>';
                    }
                    var _menuitemlevelstr = " menu-item-j";
                    // if (_tis.pos % 2 == 0) {
                    if (_children && _children.length > 0) {
                        _menuitemlevelstr = " menu-item-o";
                    }

                    var _attachexpandstr =
                        _children && _children.length > 0
                            ? _tis.pos == 0
                                ? '<span class="left-menu-noexpandico"></span>'
                                : '<span class="left-menu-noexpandico"></span>'
                            : "";
                    rtn +=
                        '<li><div class="left-menu-item' +
                        _menuitemlevelstr +
                        '" code="' +
                        _code +
                        '" title="' +
                        _name +
                        '"><div class="left-menu-item-inner">' +
                        _spacestr +
                        '<span class="left-menu-ico' +
                        _icostr +
                        '"></span><span class="left-menu-link"><a>' +
                        _name +
                        "</a></span>" +
                        _attachexpandstr +
                        "</div></div>";
                    if (_children && _children.length > 0) {
                        rtn += '<div class="left-menu-subitems">';
                        _tis.pos++;
                        rtn += _tis.getmenustr(_children, false, _settings);
                        _tis.pos--;
                        rtn += "</div>";
                    }
                    rtn += "</li>";
                }
                rtn += "</ul>";
                return rtn;
            } else {
                return "";
            }
        },
        load: function (data, nodedom) {
            var _tis = this;
            var _dom = _tis.dom;
            var _parentcode = "";
            var _parentname = "";
            var _parentico = "";
            var _settings = {};
            if (!nodedom) {
                nodedom = _tis.dom;
            }
            if (arguments.length > 2) {
                _parentcode = arguments[2];
            }
            if (arguments.length > 3) {
                _parentname = arguments[3];
            }
            if (arguments.length > 4) {
                _parentico = arguments[4];
            }
            if (arguments.length > 5) {
                _settings = arguments[5];
            }
            if ($(nodedom).hasClass(clsBodyLeftMenu)) {
                $(nodedom).children().remove();
                //
                var _htm =
                    '<div class="left-menu-item left-menu-title" code="' +
                    _parentcode +
                    '" title="' +
                    _parentname +
                    '"><div class="left-menu-item-inner"><span class="left-menu-ico fa fa-folder"></span><span class="left-menu-link"><a>' +
                    _parentname +
                    "</a></span><span class=\"left-menu-expandico\" style='display:none;'></span></div></div>";
                var _phtm = _tis.getmenustr(data, true, _settings);
                _htm += _phtm;
                $(nodedom).append(_htm);
                $(_dom).data("menusdt", data);
                $(_dom).data("menuscfg", _settings);
            }
        },
        showcurview: function (code, focus) {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).find(".left-menu-item").removeClass("hbm-cur");
            $(_dom)
                .find(".left-menu-item[code='" + code + "']")
                .addClass("hbm-cur");
            if (focus) {
                $(_dom)
                    .find(".left-menu-item[code='" + code + "']")
                    .get(0)
                    .scrollIntoView();
            }
        },
    };

    var HbmTopMenu = function () {
        this.dom;
        this.eventclick = null;
        this.eventitemactive = null;
        this.data = [];
        this.cfg;
        this.init = function (dom) {
            this.dom = dom;
            //
            this.eventbind();
        };
    };
    HbmTopMenu.prototype = {
        eventbind: function () {
            var _tis = this;
            $(_tis.dom).delegate($(_tis).find("li"), "click", function (e) {
                var ev = e || window.event;

                var _tar = ev.target;
                if (_tar.tagName == "UL") {
                    ev.stopPropagation();
                    return;
                }
                if (_tar.tagName == "A") {
                    _tar = $(_tar).closest("li");
                }
                $(_tis.dom).find("li").removeClass("gcs_cur");
                $(_tar).addClass("gcs_cur");
                if (_tis.eventclick) {
                    var _code = $(_tar).attr("code");
                    var _name = $(_tar).find("a").html();
                    ev.hbmdata = { code: _code, name: _name };
                    ev.hbmdatainner = _tis._getinnerdata(_code);
                    _tis.eventclick(ev);
                }
                ev.stopPropagation();
            });
        },
        active: function (code) {
            var _tis = this;
            $(_tis.dom).find("li").removeClass("gcs_cur");
            $(_tis.dom)
                .find("li[code='" + code + "']")
                .addClass("gcs_cur");
            var _pm = {
                hbmdata: code,
                hbmdom: $(_tis.dom)
                    .find("li[code='" + code + "']")
                    .get(0),
                hbmdatainner: _tis._getinnerdata(code),
            };
            if (_tis.eventitemactive) {
                _tis.eventitemactive(_pm);
            }
        },
        _getinnerdata: function (code) {
            var _tis = this;
            if (!_tis.data || _tis.data.length == 0) {
                return null;
            }
            var _codefld = _tis.cfg["codefld"] || _tis.defaultcfgs["codefld"];
            for (var i = 0; i < _tis.data.length; i++) {
                if (_tis.data[i][_codefld] == code) {
                    return _tis.data[i];
                }
            }
            return null;
        },
        load: function (data, cfg) {
            var _tis = this;
            _tis.data = data || [];
            if (!data || data.length == 0) {
                return;
            }
            _tis.cfg = $.extend(
                false,
                _tis.defaultcfgs,
                _tis.cfg || {},
                cfg || {}
            );

            var topmenuht = "<ul>";
            for (var i = 0; i < data.length; i++) {
                var _code = data[i][_tis.cfg["codefld"]];
                var _name = data[i][_tis.cfg["namefld"]];
                var _img = data[i][_tis.cfg["imgfld"]];
                topmenuht +=
                    "<li code='" +
                    _code +
                    "'><a class='" +
                    _code +
                    '\' href="javascript:">' +
                    _name +
                    "</a></li>";
            }
            topmenuht += "</ul>";
            // alert(topmenuht);
            //
            $(_tis.dom).html(topmenuht);
        },
    };
    HbmTopMenu.prototype.defaultcfgs = {
        codefld: "code",
        namefld: "name",
        imgfld: "resimg",
    };

    var HbmHeadTools = function () {};
    HbmHeadTools.prototype = {};

    var baloonpops = [
        "hbm-pop-up",
        "hbm-pop-right",
        "hbm-pop-down",
        "hbm-pop-left",
    ];
    var HbmBalloon = function (dom, pop) {
        this.dom = dom;
        this.pop = pop;

        $(dom).data(httypeobj, this);

        this.init = function () {};
    };
    HbmBalloon.prototype = {
        show: function (dockerdom, pop) {
            var _tis = this;
            var _dom = _tis.dom;
            _tis.setpop(pop);
            var dominfo = getElCoordinate(dockerdom);
            // var _dominfo2 = getElCoordinate(_dom);
            var _x = 0;
            var _y = 0;
            switch (_tis.pop) {
                case "t":
                    _x =
                        dominfo.left +
                        ($(dockerdom).width() - $(_dom).width()) / 2;
                    _y = dominfo.top + $(dockerdom).height();
                    break;
                case "r":
                    _x = dominfo.left - $(_dom).width();
                    _y =
                        dominfo.top +
                        ($(dockerdom).height() - $(_dom).height());
                    break;
                case "b":
                    _x =
                        dominfo.left +
                        ($(dockerdom).width() - $(_dom).width()) / 2;
                    _y =
                        dominfo.left - $(dockerdom).height() - $(_dom).height();
                    break;
                case "l":
                    _x = dominfo.left + $(dockerdom).width();
                    _y =
                        dominfo.left -
                        ($(_dom).height() - $(dockerdom).height()) / 2;
                    break;
                default:
                    break;
            }
            _y += 10;
            $(_tis.dom).css({ left: _x + "px", top: _y + "px" });

            $(_tis.dom).show();
        },
        hide: function () {
            var _tis = this;
            $(_tis.dom).hide();
        },
        setpop: function (pop) {
            var _tis = this;
            var _dom = _tis.dom;
            if (pop) {
                _tis.pop = pop;
            } else {
                pop = _tis.pop;
                pop = pop ? pop : "t";
                _tis.pop = pop;
            }
            switch (pop) {
                case "l":
                    $(_dom).removeClass(baloonpops[0]);
                    $(_dom).removeClass(baloonpops[1]);
                    $(_dom).removeClass(baloonpops[2]);
                    $(_dom).addClass(baloonpops[3]);
                    break;
                case "r":
                    $(_dom).removeClass(baloonpops[0]);
                    $(_dom).addClass(baloonpops[1]);
                    $(_dom).removeClass(baloonpops[2]);
                    $(_dom).removeClass(baloonpops[3]);
                    break;
                case "t":
                    $(_dom).addClass(baloonpops[0]);
                    $(_dom).removeClass(baloonpops[1]);
                    $(_dom).removeClass(baloonpops[2]);
                    $(_dom).removeClass(baloonpops[3]);
                    break;
                case "b":
                    $(_dom).removeClass(baloonpops[0]);
                    $(_dom).removeClass(baloonpops[1]);
                    $(_dom).addClass(baloonpops[2]);
                    $(_dom).removeClass(baloonpops[3]);
                    break;
                default:
                    break;
            }
        },
    };

    var HbmMenus = function (dom) {
        this.dom = dom;
        this.menuitemclick = null;
        $(dom).data(httypeobj, this);
        this.init = function () {
            this.eventbind();
        };
    };

    HbmMenus.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).delegate("." + clsMenuitem, "click", function (e) {
                if (_tis.menuitemclick) {
                    var ev = e || window.event;
                    var _tar = ev.target;
                    var _tn = _tar.tagName;
                    var _aimtar = null;
                    switch (_tn) {
                        case "SPAN":
                            _aimtar = $(_tar)
                                .closest("div." + clsMenuitem)
                                .get(0);
                            break;
                        case "DIV":
                            if ($(_tar).hasClass(clsMenuitem)) {
                                _aimtar = _tar;
                            } else {
                                _aimtar = $(_tar)
                                    .closest("div." + clsMenuitem)
                                    .get(0);
                            }
                            break;
                        default:
                            break;
                    }
                    //
                    var _code = $(_aimtar).attr("code");
                    var _name = $(_aimtar).find("span:nth-child(1)").html();
                    //
                    ev.hbmdata = { code: _code, name: _name };
                    ev.hbmaim = _aimtar;
                    _tis.menuitemclick(ev);
                }
            });
        },
        load: function (menus) {
            var _tis = this;
            var _dom = _tis.dom;
            if (menus && menus.length > 0) {
                var _ht = "";
                for (var i = 0; i < menus.length; i++) {
                    var _code = menus[i]["code"];
                    var _name = menus[i]["name"];
                    var _ico = menus[i]["ico"];
                    var _icostr = " fa fa-" + _ico;
                    _ht +=
                        '<div class="hbm-menu-item" code="' +
                        _code +
                        '"><span class="hbm-ico' +
                        _icostr +
                        '"></span><span>' +
                        _name +
                        "</span></div>";
                }
                //
                $(_dom).html(_ht);
            }
        },
        addmenuitem: function (item, posi) {
            var _tis = this;
            var _dom = _tis.dom;
            var _code = menus[i]["code"];
            var _name = menus[i]["name"];
            var _ico = menus[i]["ico"];
            var _icostr = "fa-" + _ico;
            var $_aimdom = $(_dom).find(".hbm-menu-item[code='" + _code + "']");
            if ($_aimdom.length > 0) {
                return;
            } else {
                if (posi == "first") {
                    $(_dom).prepend(
                        '<div class="hbm-menu-item" code="' +
                            _code +
                            '"><span class="hbm-ico' +
                            _icostr +
                            '"></span><span>' +
                            _name +
                            "</span></div>"
                    );
                } else {
                    $(_dom).append(
                        '<div class="hbm-menu-item" code="' +
                            _code +
                            '"><span class="hbm-ico' +
                            _icostr +
                            '"></span><span>' +
                            _name +
                            "</span></div>"
                    );
                }
            }
        },
        addmenus: function (items, posi) {
            var _ht = "";
            for (var i = 0; i < menus.length; i++) {
                var _code = menus[i]["code"];
                var _name = menus[i]["name"];
                var _ico = menus[i]["ico"];
                var _icostr = " fa fa-" + _ico;
                _ht +=
                    '<div class="hbm-menu-item" code="' +
                    _code +
                    '"><span class="hbm-ico' +
                    _icostr +
                    '"></span><span>' +
                    _name +
                    "</span></div>";
            }
            //
            if (posi == "first") {
                $(_dom).prepend(_ht);
            } else {
                $(_dom).append(_ht);
            }
        },
        show: function (dockerdom) {
            var _tis = this;
            var _dom = _tis.dom;
            var dominfo = getElCoordinate(dockerdom);
            var _x = dominfo.left;
            var _y = dominfo.top + $(dockerdom).height();
            // $(dockerdom).addClass
            $(_tis.dom).css({ left: _x + "px", top: _y + "px" });

            $(_tis.dom).show();
        },
        hide: function () {
            var _tis = this;
            $(_tis.dom).hide();
        },
    };

    var theposi = { absolute: "0", fixed: "1" };
    var windowtype = { common: "0", info: "1", warning: "2", error: "3" };
    var windowclosebehavior = { close: "0", dock: "1" };
    var windowcls = {
        "0": "hbm-window",
        "1": "hbm-window-info",
        "2": "hbm-window-warning",
        "3": "hbm-window-error",
    };
    var show_mode = { common: "0", up: 1, down: 2, left: 3, right: 4 };

    var HbmWindow = function (dom) {
        this.dom = dom;
        this.$head = $(dom).find(".hbm-head");
        this.$head_close = $(dom).find(".hbm-head .hbm-close");
        this.$head_title = $(dom).find(".hbm-head .hbm-title");
        this.$head_controls = $(dom).find(".hbm-head .hbm-controls");
        this.$head_controls_min = $(dom).find(
            ".hbm-head .hbm-controls .hbm-min"
        );
        this.$head_controls_max = $(dom).find(
            ".hbm-head .hbm-controls .fa-window-maximize"
        );
        this.$head_controls_close = $(dom).find(
            ".hbm-head .hbm-controls .hbm-close"
        );
        this.$body = $(dom).find(".hbm-body");
        this.$footer = $(dom).find(".hbm-footer");
        this.$footer_btnok = $(dom).find(".hbm-btn-ok");
        this.$footer_btnclose = $(dom).find(".hbm-btn-close");
        this.eventokclick = null;
        this.eventcloseclick = null;
        this.movemode = theposi[$(dom).css("position")];
        this.cfgs = {};
        this.init = function (cfgs) {
            var _tis = this;
            var _dom = _tis.dom;
            readOnly(this.$head_title.get(0));
            _tis.setcfgs(_tis.cfgs);
            this.eventbind();
        };
    };
    HbmWindow.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _drag;
            _tis.$head_close.on("click", function () {
                _tis.close();
            });
            _tis.$head.on("mousedown", function (e) {
                _drag = _tis.cfgs["drag"];
                if (!_drag) {
                    $(_tis.$head).css("cursor", "default");
                    return;
                } else {
                    $(_tis.$head).css("cursor", "move");
                }
                var ev1 = e || window.event;
                var _tar = ev1.target;
                if (
                    $(_tar).hasClass("hbm-close") ||
                    $(_tar).hasClass("hbm-ico")
                ) {
                    _wtar = _tar;
                    return;
                }
                //
                var _wdom = _dom;
                var posiex2 = getOriElCoordinate($(_wdom).get(0));
                var posi3 = mousePosition(ev1);
                var _a = $(_wdom);
                //
                if ($(".hbm-wmove").length == 0) {
                    $("body").append("<div class='hbm-wmove'></div>");
                }
                var leftM = posiex2.left;
                var topM = posiex2.top;
                //
                switch (_tis.movemode) {
                    case "0":
                        $(".hbm-wmove").css("position", "absolute");
                        break;
                    case "1":
                        $(".hbm-wmove").css("position", "fixed");
                        break;
                    default:
                        break;
                }
                $(".hbm-wmove").css({
                    left: leftM + "px",
                    top: topM + "px",
                    width: $(_a).width() + "px",
                    height: $(_a).height() + "px",
                });
                $(".hbm-wmove").show();
                document.onmousemove = function (e) {
                    var ev2 = e || window.event;
                    var posi4 = mousePosition(ev2);
                    //
                    var dtx = posi4.x - posi3.x;
                    var dty = posi4.y - posi3.y;
                    //
                    var _x = leftM + dtx;
                    var _y = topM + dty;
                    if (_x < 0) {
                        _x = 0;
                    }
                    var $parentdom = _tis.movemode == "0" ? $(document) : $(w);
                    if (_x > $parentdom.width() - _a.get(0).offsetWidth) {
                        _x = $parentdom.width() - _a.get(0).offsetWidth;
                    }

                    if (_y < 0) {
                        _y = 0;
                    }
                    if (_y > $parentdom.height() - _a.get(0).offsetHeight) {
                        _y = $parentdom.height() - _a.get(0).offsetHeight;
                    }
                    //
                    $(".hbm-wmove").css({ left: _x + "px", top: _y + "px" });
                };
                document.onmouseup = function (e) {
                    var ev3 = e || window.event;

                    //
                    var posiex2 = getOriElCoordinate($(".hbm-wmove").get(0));
                    //
                    $(_a).css({
                        left: posiex2.left + "px",
                        top: posiex2.top + "px",
                    });
                    $(".hbm-wmove").width(0);
                    $(".hbm-wmove").height(0);
                    $(".hbm-wmove").hide();
                    //
                    document.onmousemove = null;
                    document.onmouseup = null;
                };
            });

            _tis.$footer_btnok.on("click", function (e) {
                var ev = e || window.event;
                if (_tis.eventokclick != null) {
                    _tis.eventokclick(ev);
                }
                _tis.close();
            });
            _tis.$footer_btnclose.on("click", function (e) {
                var ev = e || window.event;
                if (_tis.eventcloseclick != null) {
                    _tis.eventcloseclick(ev);
                }
                _tis.close();
            });
            _tis.$head.delegate(".fa-window-maximize", "click", function () {
                _tis.maximize();
            });
            _tis.$head.delegate(".fa-window-restore", "click", function () {
                _tis.restore();
            });
        },
        setcfgs: function (cfgs) {
            var _tis = this;
            var _dom = _tis.dom;
            //
            _tis.cfgs = $.extend(
                false,
                _tis.defaultcfg,
                _tis.cfgs || {},
                cfgs || {}
            );
            var _windowcls = windowcls[_tis.cfgs["windowtype"]];
            $(_dom).addClass(_windowcls);
            _tis.$head.css(
                "background-color",
                _tis.cfgs["head"]["background-color"]
            );
            _tis.cfgs["controls"]["min"]
                ? _tis.$head_controls_min.show()
                : _tis.$head_controls_min.hide();
            _tis.cfgs["controls"]["max"]
                ? _tis.$head_controls_max.show()
                : _tis.$head_controls_max.hide();
            _tis.cfgs["controls"]["close"]
                ? _tis.$head_controls_close.show()
                : _tis.$head_controls_close.hide();
            if (_tis.cfgs["head"]["title"])
                _tis.$head_title.css(
                    "color",
                    _tis.cfgs["head"]["title"]["color"]
                );
            if (_tis.cfgs["width"])
                $(_dom).css("width", _tis.cfgs["width"] + "px");
            if (_tis.cfgs["height"])
                $(_dom).css("width", _tis.cfgs["width"] + "px");
            return _tis;
        },
        maximize: function () {
            var _tis = this;
            var _dom = _tis.dom;
            //
            var _oriinfo = {};
            _oriinfo["left"] = $(_dom).offset().left;
            _oriinfo["top"] = $(_dom).offset().top;
            _oriinfo["width"] = $(_dom).width();
            _oriinfo["height"] = $(_dom).height();
            _setdata(_dom, dlgoriinfo, _oriinfo);
            //
            var _w1 = $(document).width();
            var _h1 = $(document).height();
            $(_dom).css({ left: "0px", top: "0px" });
            $(_dom).width(_w1);
            $(_dom).height(_h1);
            _tis.$head
                .find(".fa-window-maximize")
                .addClass("fa-window-restore");
            _tis.$head
                .find(".fa-window-maximize")
                .removeClass("fa-window-maximize");
            _tis.resizedraw();
            return _tis;
        },
        restore: function () {
            var _tis = this;
            var _dom = _tis.dom;
            //
            var _oriinfo = _getdata(_dom, dlgoriinfo);
            if (_oriinfo) {
                $(_dom).width(_oriinfo["width"]);
                $(_dom).height(_oriinfo["height"]);
                $(_dom).css({
                    left: _oriinfo["left"] + "px",
                    top: _oriinfo["top"] + "px",
                });
                _tis.$head
                    .find(".fa-window-restore")
                    .addClass("fa-window-maximize");
                _tis.$head
                    .find(".fa-window-restore")
                    .removeClass("fa-window-restore");
                _tis.resizedraw();
            }
            return _tis;
        },
        resizedraw: function () {
            var _tis = this;
            var _dom = _tis.dom;
            //
            var _w1 = $(document).width() + $(document).scrollLeft();
            var _h1 = $(document).height() + $(document).scrollTop();
            var _w2 = $(_dom).outerWidth(false);
            var _h2 = $(_dom).outerHeight(false);
            //
            $(_dom)
                .find(".hbm-body")
                .css({ height: _h2 - 65 + "px" });
            //
            return _tis;
        },
        thecontent: function (str) {
            var _tis = this;
            _tis.$body.html(str);
            return _tis;
        },
        title: function (title) {
            var _tis = this;
            _tis.$head_title.html(title);
            return _tis;
        },
        close: function () {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).hide();
            $(_dom.data(dialogmaskobj)).hide();
            subZindex();
        },
        show: function (showmode) {
            var _tis = this;
            var _dom = _tis.dom;
            if (arguments.length > 1 && !isNaN(arguments[1])) {
                _tis.cfgs["speed"] = arguments[1];
            }
            //
            if (showmode) {
                var _mode = show_mode[showmode];
                switch (_mode) {
                    case "0":
                        _tis._cmshow();
                        break;
                    case "1":
                        _tis._showup();
                        break;
                    default:
                        _tis._showup();
                        break;
                }
            } else {
                _tis._cmshow();
            }
            return _tis;
        },
        _cmshow: function () {
            var _tis = this;
            var _dom = _tis.dom;
            if (!$(_dom).data(dialogmaskobj)) {
                var $msk = $("<div class='hbm-window-mask'></div>");
                $(_dom).data(dialogmaskobj, $msk.get(0));
            }
            $(_dom).css("z-index", dlgzindex);
            $mask = $($(_dom).data(dialogmaskobj));
            $mask.css("z-index", dlgzindex + 1);

            //
            var _w1 = $(window).width() + $(document).scrollLeft();
            var _h1 = $(window).height() + $(document).scrollTop();
            var _w2 = $(_dom).outerWidth(false);
            var _h2 = $(_dom).outerHeight(false);
            //
            var _x = (_w1 - _w2) / 2;
            var _y = (_h1 - _h2) / 3;
            //;
            $mask.css({
                left: "0px",
                top: "0px",
                width: "100%",
                height: "100%",
            });
            //
            $(_dom).css({ left: _x + "px", top: _y + "px" });
            $(_dom)
                .find(".hbm-body")
                .css({ height: _h2 - 65 + "px" });
            // $(_dom).find(".hbm-body iframe").css({ "height": "100%","width":"100%" });
            //
            $($(_dom).data(dialogmaskobj)).show();
            $(_dom).show();
        },
        _showup: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _height = $(_dom).height();
            $(_dom).css({ height: "0px", bottom: "0px", right: "0px" });
            $(_dom).show();
            $(_dom).animate({ height: _height + "px" }, _tis.cfgs["speed"]);
            return _tis;
        },
    };
    HbmWindow.prototype.defaultcfg = {
        head: { title: { color: "#FFF" }, "background-color": "#b02a37" },
        windowtype: "info",
        controls: { min: false, max: false, close: true },
        closebehavior: "close",
        drag: false,
        speed: 3000,
    };

    var GsTab = function (dom) {
        this.dom = dom;
        this.grp = $(dom).attr("grp");
        this.eventitemclick;
        this.init = function () {
            var _t = this;
            _t.eventbind();
            //
            var _atr = _t.getcuratr();
            _t.active(_atr);
        };
        this.cfg = {};
    };
    GsTab.prototype = {
        eventbind: function () {
            var _t = this;
            $(_t.dom).delegate(
                ".gcs_tabhd[grp='" + _t.grp + "'] .gcs_tabhd_item",
                "click",
                function (e) {
                    var _atr = $(this).attr("atr");
                    var ev = e || window.event;
                    if (typeof _atr != "undefined") {
                        _t.active(_atr);
                        ev["gsdata"] = { code: _atr };
                        if (_t.eventitemclick) {
                            _t.eventitemclick(ev);
                        }
                    }
                }
            );
        },
        active: function (atr) {
            var _t = this;
            $(_t.dom)
                .find(".gcs_tabhd[grp='" + _t.grp + "'] .gcs_tabhd_item")
                .removeClass("gcs_cur");
            $(_t.dom)
                .find(
                    ".gcs_tabbd[grp='" +
                        _t.grp +
                        "'] .gcs_tabitem[grp='" +
                        _t.grp +
                        "']"
                )
                .hide();

            $(_t.dom)
                .find(
                    ".gcs_tabhd[grp='" +
                        _t.grp +
                        "'] .gcs_tabhd_item[atr='" +
                        atr +
                        "']"
                )
                .addClass("gcs_cur");
            $(_t.dom)
                .find(
                    ".gcs_tabbd[grp='" +
                        _t.grp +
                        "'] .gcs_tabitem[grp='" +
                        _t.grp +
                        "'][atr='" +
                        atr +
                        "']"
                )
                .show();
        },
        getcuratr: function () {
            var _t = this;
            var _curatr = $(_t.dom)
                .find(
                    ".gcs_tabhd[grp='" + _t.grp + "'] .gcs_tabhd_item.gcs_cur"
                )
                .attr("atr");
            return _curatr;
        },
        data: function (data, cfg) {
            var _t = this;
            _t.cfg = $.extend(false, _t.defaultcfg, _t.cfg || {}, cfg || {});
            if (data && data.length > 0) {
                var _varhdstr = "";
                var _varbdstr = "";
                for (var i = 0; i < data.length; i++) {
                    var _atr = data[i][_t.cfg["atrfld"]];
                    var _atrstr = data[i][_t.cfg["titlefld"]];
                    _varhdstr +=
                        '<li class="gcs_tabhd_item" atr="' +
                        _atr +
                        '">' +
                        _atrstr +
                        "</li>";
                    if (_t.grp) {
                        _varbdstr +=
                            '<div class="gcs_tabitem" grp="' +
                            _t.grp +
                            '" atr="' +
                            _atr +
                            '"></div>';
                    } else {
                        _varbdstr +=
                            '<div class="gcs_tabitem" atr="' +
                            _atr +
                            '"></div>';
                    }
                }
                if (_t.grp) {
                    $(_t.dom).find(".gcs_tabhd ul").html(_varhdstr);
                    $(_t.dom).find(".gcs_tabbd").html(_varbdstr);
                } else {
                    $(_t.dom)
                        .find(".gcs_tabhd[grp='" + _t.grp + "'] ul")
                        .html(_varhdstr);
                    $(_t.dom)
                        .find(".gcs_tabbd[grp='" + _t.grp + "']")
                        .html(_varbdstr);
                }

                if (data && data.length > 0) {
                    var _atr = data[0][_t.cfg["atrfld"]];
                    _t.active(_atr);
                }
            } else {
                if (_t.grp) {
                    $(_t.dom).find(".gcs_tabhd ul").empty();
                    $(_t.dom).find(".gcs_tabcont").empty();
                } else {
                    $(_t.dom)
                        .find(".gcs_tabhd[grp='" + _t.grp + "'] ul")
                        .empty();
                    $(_t.dom)
                        .find(
                            ".gcs_tabcont[grp='" +
                                _t.grp +
                                "'] .gcs_tabitem[grp='" +
                                _t.grp +
                                "']"
                        )
                        .remove();
                }
            }
        },
        setcontent: function (atr, htstr) {
            var _t = this;
            if (_t.grp) {
                $(_t.dom)
                    .find(
                        ".gcs_tabbd[grp='" +
                            _t.grp +
                            "'] .gcs_tabitem[grp='" +
                            _t.grp +
                            "'][atr='" +
                            atr +
                            "']"
                    )
                    .html(htstr);
            } else {
                $(_t.dom)
                    .find(
                        ".gcs_tabbd[grp='" +
                            _t.grp +
                            "'] .gcs_tabitem[grp='" +
                            _t.grp +
                            "']"
                    )
                    .html(htstr);
            }
        },
    };
    GsTab.prototype.defaultcfg = {
        map: { atrfld: "atr", titlefld: "title" },
    };
    var GsPaging = function (dom) {
        this.dom = dom;
        var _this = this;
        this.init = function () {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).addClass("gcs_paging");
            $(_dom).html(
                "<div class='gcs_paging_main'><ul><li><span class='gcs_paging_btn gcs_paging_first' title='第一页'></span></li><li><span class='gcs_paging_btn gcs_paging_pre' title='前一页'></span></li><li><span class='gcs_paging_text'>页码</span></li><li><span class='gcs_paging_input'><input type='text'></span></li><li><span class='gcs_paging_btn gcs_paging_goto'>Go</span></li><li><span class='gcs_paging_btn gcs_paging_next' title='后一页'></span></li><li><span class='gcs_paging_btn gcs_paging_last' title='末页'></span></li><li><span class='gcs_paging_info'>当前第&nbsp;&nbsp;<font color='blue'>1/1</font>&nbsp;&nbsp;页&nbsp;&nbsp;&nbsp;每页显示行数: 10&nbsp;&nbsp;</span></li><li><select class='gcs_paging_limitselector'><option value='5'>5</option><option value='10'>10</option><option value='15'>15</option><option value='20'>20</option><option value='30'>30</option><option value='50'>50</option><option value='100'>100</option></select></li><li><span>记录总数:</span><span class='gcs_total'>0</span></li></ul></div>"
            );
            //
            _tis.eventbind();
        };
        this.eventpaging = null;

        var _getpaginfo = function () {
            var _dom = _this.dom;
            var pginfo = {};
            var _curpg = $(_dom).attr("pg");
            var _curlimit = $(_dom).find(".gcs_paging_limitselector").val();
            var _pgtotal = $(_dom).attr("pgtt");
            var _total = $(_dom).find("gcs_total").html();
            pginfo["pg"] = _curpg || 1;
            pginfo["limit"] = _curlimit || 10;
            pginfo["pgtotal"] = _pgtotal || 0;

            pginfo["pg"] = pginfo["pg"] <= 0 ? 1 : pginfo["pg"];
            pginfo["total"] = _total || 0;
            return pginfo;
        };
        this.getpaginfo = function () {
            var rtn = _getpaginfo();
            return rtn;
        };
        var _setpaginfo = function (pginfo) {
            var _tis = _this;
            var _dom = _tis.dom;
            $(_dom).attr("pg", pginfo["pg"]);
            $(_dom).find(".gcs_paging_limitselector").val(pginfo["limit"]);
            $(_dom).attr("pgtt", pginfo["pgtotal"]);
            $(_dom)
                .find(".gcs_paging_info")
                .html(
                    "当前第&nbsp;&nbsp;<font color='blue'>" +
                        pginfo["pg"] +
                        "/" +
                        pginfo["pgtotal"] +
                        "</font>页   每页显示行数:<span class='gcs_pglimit'>" +
                        pginfo["limit"] +
                        "</span>"
                );
            $(_dom)
                .find(".gcs_total")
                .html(pginfo["total"] || "0");
        };
        this.setpaginfo = function (pginfo) {
            var _tis = this;
            var _dom = _tis.dom;
            _setpaginfo(pginfo);
        };

        this.goto = function (page) {
            var _tis = this;
            var _dom = _tis.dom;
            var pg = page || $(_dom).find(".gcs_paging_input input").val() || 1;
            var _pginfo = _tis.getpaginfo();
            _pginfo["pg"] = pg;
            if (_pginfo["pg"] < 1) {
                _pginfo["pg"] = 1;
            }
            var _pgtt0 = _pginfo["pgtotal"];
            if (_pgtt0 < pg) {
                _pginfo["pg"] = _pgtt0;
            } else {
                if (_pgtt0 < 1) {
                    _pginfo["pg"] = 1;
                }
            }
            $(_dom).find(".gcs_paging_input input").val(_pginfo["pg"]);
            _setpaginfo(_pginfo);
        };

        this.setpglimit = function (limit) {
            var _tis = this;
            var _dom = _tis.dom;
            var _limit =
                limit || $(_dom).find(".gcs_paging_limitselector").val() || 10;
            //
            var _pginfo = _tis.getpaginfo();
            _pginfo["limit"] = limit;
            _pginfo["pg"] = 1;
            //
            _setpaginfo(_pginfo);
        };

        this.tolast = function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _pginfo = _tis.getpaginfo();
            _pginfo["pgtotal"];
            _pginfo["pg"] = _pginfo["pgtotal"];
            //
            _setpaginfo(_pginfo);
        };

        this.tofirst = function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _pginfo = _tis.getpaginfo();
            _pginfo["pg"] = 1;
            //
            _setpaginfo(_pginfo);
        };

        this.toprev = function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _pginfo = _tis.getpaginfo();
            console.log(_pginfo);
            var _pg = _pginfo["pg"];
            _pg = _pg - 1;
            if (_pg < 1) {
                _pginfo["pg"] = 1;
            } else {
                if (_pg > _pginfo["pgtotal"]) {
                    _pginfo["pg"] = _pginfo["pgtotal"];
                } else {
                    _pginfo["pg"] = _pg;
                }
            }
            //
            _setpaginfo(_pginfo);
        };

        this.tonext = function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _pginfo = _tis.getpaginfo();
            var _pg = _pginfo["pg"];
            var _pgtt = _pginfo["pgtotal"];
            if (_pg == _pgtt) {
                return;
            }
            _pg = _pg - 0 + 1;
            if (_pg > _pgtt) {
                _pginfo["pg"] = _pgtt;
            } else {
                _pginfo["pg"] = _pg;
            }
            //
            _setpaginfo(_pginfo);
        };
    };
    GsPaging.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).delegate(".gcs_paging_goto", "click", function (e0) {
                var _e0 = e0 || window.event;
                //
                _tis.goto();
                var _pginfo = _tis.getpaginfo();
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
                _e0.stopPropagation();
            });
            $(_dom).delegate(".gcs_paging_input input", "keydown", function (
                e0
            ) {
                var _e0 = e0 || window.event;
                //
                if (_e0.keyCode == 13) {
                    //
                    _tis.goto(_tis);
                    var _pginfo = _tis.getpaginfo();
                    // _e0.gsctrl = _tis;
                    _e0.pginfo = _pginfo;
                    if (_tis.eventpaging) {
                        _tis.eventpaging(_e0);
                    }
                }

                //
                _e0.stopPropagation();
            });
            $(_dom).delegate(".gcs_paging_next", "click", function (e0) {
                var _e0 = e0 || window.event;
                //
                _tis.tonext(_tis);
                var _pginfo = _tis.getpaginfo();
                // _e0.gsctrl = _tis;
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
                _e0.stopPropagation();
            });
            //
            $(_dom).delegate(".gcs_paging_pre", "click", function (e0) {
                var _e0 = e0 || window.event;
                //
                _tis.toprev(_tis);
                var _pginfo = _tis.getpaginfo();
                // _e0.gsctrl = _tis;
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
                _e0.stopPropagation();
            });

            $(_dom).delegate(".gcs_paging_first", "click", function (e0) {
                var _e0 = e0 || window.event;
                //
                _tis.tofirst(_tis);
                var _pginfo = _tis.getpaginfo();
                // _e0.gsctrl = _tis;
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
                _e0.stopPropagation();
            });

            $(_dom).delegate(".gcs_paging_last", "click", function (e0) {
                var _e0 = e0 || window.event;
                //
                _tis.tolast(_tis);
                var _pginfo = _tis.getpaginfo();
                // _e0.gsctrl = _tis;
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
                _e0.stopPropagation();
            });

            $(_dom).delegate(".gcs_paging_limitselector", "change", function (
                e
            ) {
                var _e0 = e || window.event;
                //
                _tis.setpglimit($(this).val());
                var _pginfo = _tis.getpaginfo();
                // _e0.gsctrl = _tis;
                _e0.pginfo = _pginfo;
                if (_tis.eventpaging) {
                    _tis.eventpaging(_e0);
                }
                //
            });

            $(_dom).delegate(
                ".gcs_paging_main ul li .gcs_paging_input input",
                "focus",
                function () {
                    $(this)
                        .closest(".gcs_paging_input")
                        .css("background-color", "#CCE5FF");
                }
            );
            $(_dom).delegate(
                ".gcs_paging_main ul li .gcs_paging_input input",
                "blur",
                function () {
                    $(this)
                        .closest(".gcs_paging_input")
                        .css("background-color", "#FFF");
                }
            );
        },
    };
    var GsTable = function (dom, cfg) {
        this.dom = dom;
        this.paging = null;
        this.name = "gstable" + new Date().getTime();
        this.cfg = $.extend(false, this.defaultcfg, cfg || {});
        this.$tbeditorsourcedom;
        this.enabled = true;
        this.init = function () {
            var _tis = this;
            var _dom = _tis.dom;
            $(_dom).addClass("gcs_table");
            //
            _inittable(true);
            //
            _tis.eventbind();
        };
        this.pgfn;
        this.eventpaging = function (fn) {
            var _tis = this;
            if (fn) {
                _tis.pgfn = fn;
                if (_tis.paging) {
                    _tis.paging.eventpaging = fn;
                }
            }
        };
        var _this = this;
        this.eventopclick = null;
        this.eventrowselect = null;
        this.eventcellclick = null;
        this.inittable = function (overwrite) {
            var _tis = this;
            _inittable(overwrite);
        };
        this.loaddata = function (data, cfg, pginfo) {
            var _tis = this;
            var _dom = _tis.dom;
            _tis.cfg = $.extend(
                false,
                _tis.defaultcfg || {},
                _tis.cfg || {},
                cfg || {}
            );
            _tis.cfg["paging"] = !!pginfo;
            _inittable(true);
            if (_tis.cfg["body-height"]) {
                $(_dom)
                    .find(">.gcs_body")
                    .css("height", _tis.cfg["body-height"]);
            }
            var _data = data;
            if (_data && _data.length > 0) {
                if (pginfo) {
                    var _pgtotal = pginfo["pgtotal"];
                    //if (!_pgtotal) {
                    //    _pgtotal = dat["pgtotal"];
                    //}
                    var _limit = pginfo["limit"];
                    var _page = pginfo["page"];
                    var _pginfo = {};
                    _pginfo["pg"] = _page;
                    _pginfo["pgtotal"] = _pgtotal;
                    _pginfo["limit"] = _limit;
                    _pginfo["total"] = pginfo["total"];
                    if (_tis.paging) {
                        _tis.paging.setpaginfo(_pginfo);
                    }
                }
                //
                var _htr = "";
                var _opcolwidth = _tis.cfg["opcol-width"]
                    ? _tis.cfg["opcol-width"]
                    : "200px";
                for (var k = 0; k < _data.length; k++) {
                    _htr += "<tr>";
                    if (_tis.cfg["multisel"]) {
                        _htr +=
                            "<td fld='gs_multisel'><div class='gcs_tablecell' style='width:30px;text-align:center;'><input type='checkbox'/></div></td>";
                    } else {
                        if (_tis.cfg["radio"]) {
                            _htr +=
                                "<td fld='gs_selradio'><div class='gcs_tablecell' style='width:30px;text-align:center;'><input type='radio' name='" +
                                _tis.name +
                                "'/></div></td>";
                        }
                    }
                    for (var j = 0; j < _tis.cfg["cols"].length; j++) {
                        var __fld = _tis.cfg["cols"][j]["name"];
                        var __len = _tis.cfg["cols"][j]["length"];
                        var __dftval = _tis.cfg["cols"][j]["default"] || "";
                        var _fldv = _data[k][__fld] || __dftval;
                        var _align = _tis.cfg["cols"][j]["align"] || "";
                        var _alignstyle = "";
                        if (_align != "") {
                            _alignstyle = "text-align:center;";
                        }
                        var _lenstyle = "";
                        if (__len <= 0) {
                            _lenstyle = "display:none;";
                        }
                        _htr +=
                            "<td fld='" +
                            __fld +
                            "' style='" +
                            _lenstyle +
                            _alignstyle +
                            "'><div class='gcs_tablecell' style='width:" +
                            __len +
                            "px;' title='" +
                            _fldv +
                            ";'><span>" +
                            _fldv +
                            "</span></div></td>";
                    }

                    if (_tis.enabled && _tis.cfg["opcols"]) {
                        _htr +=
                            "<td fld='op' style='width:" +
                            _opcolwidth +
                            ";text-align:center;'>";
                        for (var x = 0; x < _tis.cfg["opcols"].length; x++) {
                            var _name = _tis.cfg["opcols"][x]["name"];
                            var _title = _tis.cfg["opcols"][x]["title"];
                            var _code = _tis.cfg["opcols"][x]["code"];
                            var _cls = _tis.cfg["opcols"][x]["ico"];
                            var _clsstr = _cls ? "fa fa-" + _cls : "fa fa-home";
                            //
                            _htr +=
                                "<span class='" +
                                _clsstr +
                                " gcs_btn' title='" +
                                _title +
                                "' code='" +
                                _code +
                                "'>" +
                                _name +
                                "</span>";
                        }
                        _htr += _tis._getajustsortht();
                        _htr += "</td>";
                    }
                    _htr += "</tr>";
                }
                //
                _htr += _tis.getnewrowht();
                $(_dom).find(".gcs_body_inner table").html(_htr);
                //
            } else {
                $(_dom).find(".gcs_body_inner table").html(_tis.getnewrowht());
            }
            _ajfg = true;
            // }
        };
        this._getajustsortht = function () {
            var _tis = this;
            if (!_tis.cfg["ajustsort"]) {
                return "";
            }
            return "<span class=\"fa fa-long-arrow-up gcs_btn\" code='m_up'></span><span class=\"fa fa-long-arrow-down gcs_btn\" code='m_down'></span><span class=\"fa fa-remove gcs_btn\" code='drop'></span>";
        };
        this.inittable = function (overwrite) {
            _inittable(overwrite);
        };
        var _inittable = function (overwrite) {
            var _tis = _this;
            var _dom = _tis.dom;
            if (
                $(_dom).find(".gcs_body_inner table").length == 1 &&
                !overwrite
            ) {
                return;
            }
            if ($(_dom).find(".gcs_body_inner").length == 0) {
                $(_dom).append(
                    " <div class='gcs_head'> <div class='gcs_head_inner'> </div> </div> <div class='gcs_body'> <div class='gcs_body_inner'> </div> </div> <div class='gcs_footer'><div class='gcs_paging'></div></div>"
                );
            }
            if (!_tis.cfg["cols"]) {
                _tis.cfg = $.extend(false, _tis.defaultcfg, _tis.cfg || {});
            }
            if (_tis.cfg["body-height"]) {
                $(_dom)
                    .find(">.gcs_body")
                    .css("height", _tis.cfg["body-height"]);
            }
            //
            var _page = _tis.cfg["paging"];
            if (_page) {
                // $(_dom).find(".gcs_footer").html("");
                if (!_tis.paging) {
                    var _pgdom = $(_dom).find(".gcs_paging").get(0);
                    _tis.paging = new GsPaging(_pgdom);
                    _tis.paging.init();
                    if (_tis.pgfn) {
                        _tis.paging.eventpaging = _tis.pgfn;
                    }
                }
                $(_tis.dom).find(".gcs_footer").show();
            } else {
                if (_tis.paging) {
                    $(_tis.paging.dom).empty();
                    _tis.paging = null;
                }
                $(_tis.dom).find(".gcs_footer").hide();
            }
            var _tbcfg = _tis.cfg;
            //
            if (_tbcfg) {
                var _cols = _tbcfg["cols"];
                var _ht0 =
                    " <table cellpadding='0' cellspacing='0' border='0'><tr>";
                var _ht1 = "<table cellpadding='0' cellspacing='0'>";
                if (_tis.cfg["multisel"]) {
                    _ht0 +=
                        "<th fld='gs_multisel'><div style='width:30px;text-align:center;'><input type='checkbox'/></div></th>";
                } else {
                    if (_tis.cfg["radio"]) {
                        _ht0 +=
                            "<th fld='gs_selradio'><div style='width:30px;text-align:center;'></div></th>";
                    }
                }
                for (var i = 0; i < _cols.length; i++) {
                    var _name = _cols[i]["name"];
                    var _length = _cols[i]["length"];
                    var _disp = _cols[i]["disp"];
                    var _lenstyle = "";
                    var _sort = _cols[i]["sort"];
                    var _sortstr = _sort
                        ? '<span class="gcs_order"><i class="gcs_up"></i><i class="gcs_down gcs_cur"></i></span>'
                        : "";
                    if (_length <= 0) {
                        _lenstyle = "display:none;";
                    }
                    //
                    _ht0 +=
                        "<th fld='" +
                        _name +
                        "' style='" +
                        _lenstyle +
                        "'><div style=\"width:" +
                        _length +
                        'px;"><span>' +
                        _disp +
                        "</span>" +
                        _sortstr +
                        "</div></th>";
                }
                if (
                    _tis.enabled &&
                    _tbcfg &&
                    _tbcfg["opcols"] &&
                    _tbcfg["opcols"].length > 0
                ) {
                    var _opcolwidth = _tis.cfg["opcol-width"]
                        ? _tis.cfg["opcol-width"]
                        : "200px";
                    _ht0 +=
                        "<th fld='op' style='width:" +
                        _opcolwidth +
                        ";'><div><spantext-align:center;'>操作列</span></div></th>";
                }
                _ht0 += "</tr></table>";
                //
                if (_tis.cfg["newrowallow"]) {
                    _ht1 += _tis.getnewrowht();
                }
                _ht1 += "</table>";
                //
                $(dom).find(".gcs_head_inner").html(_ht0);
                $(dom).find(".gcs_body_inner").html(_ht1);
            }
        };
        this.getnewrowht = function (cm) {
            var _tis = this;
            var _ht1 = "";
            if (_tis.cfg["newrowallow"]) {
                _ht1 += '<tr class="gsds_gridrow"';
                if (!cm) {
                    _ht1 += ' fld="_gsds_new">';
                } else {
                    _ht1 += ">";
                }
                if (_tis.cfg["multisel"]) {
                    _ht1 +=
                        "<td fld='gs_multisel'><div class='gcs_tablecell' style='width:30px;text-align:center;'><input type='checkbox'/></div></td>";
                } else {
                    if (_tis.cfg["radio"]) {
                        _ht1 +=
                            "<td fld='gs_selradio'><div class='gcs_tablecell' style='width:30px;text-align:center;'><input type='radio' name='" +
                            _tis.name +
                            "'/></div></td>";
                    }
                }
                for (var i = 0; i < _tis.cfg["cols"].length; i++) {
                    var __fld = _tis.cfg["cols"][i]["name"];
                    var __len = _tis.cfg["cols"][i]["length"];
                    var __dftval = _tis.cfg["cols"][i]["default"] || "";
                    var _fldv = "";
                    var _lenstyle = "";
                    if (__len <= 0) {
                        _lenstyle = "display:none;";
                    }
                    _ht1 +=
                        "<td fld='" +
                        __fld +
                        "' style='" +
                        _lenstyle +
                        "'><div class='gcs_tablecell' style='width:" +
                        __len +
                        "px;' title='" +
                        _fldv +
                        "'><span>" +
                        _fldv +
                        "</span></div></td>";
                }

                if (_tis.enabled && _tis.cfg["opcols"]) {
                    var _opcolwidth = _tis.cfg["opcol-width"]
                        ? _tis.cfg["opcol-width"]
                        : "200px";
                    _ht1 +=
                        "<td fld='op' style='width:" +
                        _opcolwidth +
                        ";text-align:center;'>";
                    if (cm) {
                        for (var x = 0; x < _tis.cfg["opcols"].length; x++) {
                            var _name = _tis.cfg["opcols"][x]["name"];
                            var _title = _tis.cfg["opcols"][x]["title"];
                            var _code = _tis.cfg["opcols"][x]["code"];
                            var _cls = _tis.cfg["opcols"][x]["ico"];
                            var _clsstr = _cls ? "fa fa-" + _cls : "fa fa-home";
                            //
                            _ht1 +=
                                "<span class='" +
                                _clsstr +
                                " gcs_btn' title='" +
                                _title +
                                "' code='" +
                                _code +
                                "'>" +
                                _name +
                                "</span>";
                        }
                        _ht1 += _tis._getajustsortht();
                    }

                    _ht1 += "</td>";
                }
                _ht1 += "</tr>";
            }
            return _ht1;
        };
        this.update = function (keyobj, valueobj) {
            var _tis = this;
            var _dom = dom;
            var _keyfld = keyobj["fld"];
            var _keyvalue = keyobj["value"];
            $(_dom)
                .find(".gcs_body_inner>table tr td[fld='" + _keyfld + "']")
                .each(function () {
                    var lpkeyval = $(this).find(">div>span").html();
                    if (lpkeyval == _keyvalue) {
                        for (var i in valueobj) {
                            $(this)
                                .closest("tr")
                                .find("td[fld='" + i + "']>div>span")
                                .html(valueobj[i]);
                        }
                        return false;
                    }
                });
        };
    };
    GsTable.prototype = {
        eventbind: function () {
            var _tis = this;
            var _dom = this.dom;
            $(_dom).delegate(
                ".gcs_head_inner th[fld='gs_multisel'] input",
                "change",
                function (e) {
                    var ev = e || window.event;
                    var selrs = $(this).prop("checked");
                    selrs
                        ? $(_dom).find(".gcs_body_inner tr").addClass("gcs_cur")
                        : $(_dom)
                              .find(".gcs_body_inner tr")
                              .removeClass("gcs_cur");
                    $(_dom)
                        .find(".gcs_body_inner td[fld='gs_multisel'] input")
                        .prop("checked", selrs);
                    if (_tis.eventrowselect) {
                        var gsselectedrows = _tis.selectrows();
                        ev.gsselectrows = gsselectedrows;
                        _tis.eventrowselect(ev);
                    }
                    ev.stopPropagation();
                }
            );
            $(_dom).delegate(
                ".gcs_head_inner th[fld='gs_selradio'] input",
                "change",
                function (e) {
                    var ev = e || window.event;
                    var selrs = $(this).prop("checked");
                    selrs
                        ? $(_dom).find(".gcs_body_inner tr").addClass("gcs_cur")
                        : $(_dom)
                              .find(".gcs_body_inner tr")
                              .removeClass("gcs_cur");
                    $(_dom)
                        .find(".gcs_body_inner td[fld='gs_selradio'] input")
                        .prop("checked", selrs);
                    if (_tis.eventrowselect) {
                        var gsselectedrows = _tis.selectrows();
                        ev.gsselectrows = gsselectedrows;
                        _tis.eventrowselect(ev);
                    }
                    ev.stopPropagation();
                }
            );
            $(_dom).delegate(".gcs_body_inner tr", "click", function (e) {
                var ev = e || window.event;
                var tar = ev.target;
                if ($(this).attr("fld") == "_gsds_new") {
                    var _rowht = _tis.getnewrowht(true);
                    $(this).before(_rowht);
                    return;
                }
                if (
                    tar.tagName == "INPUT" &&
                    $(tar).closest("[fld='gs_multisel']").length == 1
                ) {
                    ev.stopPropagation();
                    return;
                }
                if (
                    tar.tagName == "INPUT" &&
                    $(tar).closest("[fld='gs_selradio']").length == 1
                ) {
                    ev.stopPropagation();
                    return;
                }
                var _tbenabled = _tis.cfg["enable"];
                if (_tbenabled) {
                    if (!_tis.cfg["multisel"]) {
                        $(_dom)
                            .find(".gcs_body_inner tr")
                            .removeClass("gcs_cur");
                        $(this).addClass("gcs_cur");
                        if (_tis.cfg["radio"]) {
                            $(_dom)
                                .find(".gcs_body_inner tr input[type='radio']")
                                .prop("checked", false);
                            $(this)
                                .find("td[fld='gs_selradio'] input")
                                .prop("checked", true);
                        }
                    } else {
                        var _selrst = !$(this)
                            .find("td[fld='gs_multisel'] input")
                            .prop("checked");
                        _selrst
                            ? $(this).addClass("gcs_cur")
                            : $(this).removeClass("gcs_cur");
                        $(this)
                            .find("td[fld='gs_multisel'] input")
                            .prop("checked", _selrst);
                    }
                    if (_tis.eventrowselect) {
                        var gsselectedrows = _tis.selectrows();
                        ev.gsselectrows = gsselectedrows;
                        _tis.eventrowselect(ev);
                    }
                }
                var _tbedit = _tis.cfg["edit"];
                if (!_tbedit) {
                    ev.stopPropagation();
                    return;
                }
                if (_tis.cfg["editmod"] == "row") {
                    if (_tis._allreadonly()) {
                        return;
                    }
                    var _tbbody = $(this).closest("div.gcs_body").get(0);
                    var _sL = _tbbody.scrollLeft;
                    var _sT = _tbbody.scrollTop;
                    //
                    var _editordominfo = getElCoordinate(this);
                    var _mydomfinfo = getElCoordinate(_tis.dom);
                    var $_editht = $(_dom).find(".gcs_tb_editrow");
                    // if (_tis.$tbeditorsourcedom && $_editht) {
                    //     _tis.updtbeditrow($_editht, _tis.$tbeditorsourcedom);
                    // }
                    _tis.$tbeditorsourcedom = $(this);
                    //
                    if ($_editht.length == 0) {
                        var _editorht = _tis.gettbeditor(this);
                        if (!_editorht) {
                            return;
                        }
                        $_editht = $(_editorht);

                        $(_dom).append($_editht);
                        //TODO: gsui.build(_dom);
                    }
                    _tis.settbeditorval($_editht, this);
                    $_editht.show();
                    $_editht.css({
                        width: _editordominfo.width + "px",
                        height: _editordominfo.height + "px",
                        left:
                            _editordominfo.left - _sL - _mydomfinfo.left + "px",
                        top: _editordominfo.top - _sT - _mydomfinfo.top + "px",
                    });
                    $_editht
                        .find("table")
                        .css("height", _editordominfo.height + "px");
                    $_editht.find(".gcs_tablecell input:first").focus();
                } else {
                    if (_tis.cfg["editmod"] == "cell") {
                        // edit cell
                        var $cell = $(tar).closest("td[fld]");
                        var _cellfld = $cell.attr("fld");
                        var _readonly = false;
                        for (var i = 0; i < _tis.cfg["cols"].length; i++) {
                            if (_tis.cfg["cols"][i]["name"] == _cellfld) {
                                _readonly =
                                    _tis.cfg["cols"][i]["readonly"] || false;
                                break;
                            }
                        }
                        if (_readonly) {
                            ev.stopPropagation();
                            return;
                        }
                        var _cellwidth = $cell.find(">div").width();
                        var $celledit = $(
                            "<input type='text' style='width:" +
                                (_cellwidth - 0) +
                                "px;'/>"
                        );
                        var _cellvalue = $cell.find(">div").attr("title");
                        $cell.find(">div>span").html("");
                        $cell.find(">div>span").append($celledit);
                        $celledit.val(_cellvalue);
                        $celledit.focus();

                        $celledit.on("blur", function () {
                            var _val = $celledit.val();
                            $cell.find(">div>span").html(_val);
                            $cell.find(">div").attr("title", _val);
                            $celledit.remove();
                        });
                    }
                }
                ev.stopPropagation();
            });
            $(_dom).delegate(
                ".gcs_tb_editrow .gcs_tbeditor_txt",
                "change",
                function () {
                    //console.log("changed!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                    var $_editht = $(_dom).find(".gcs_tb_editrow");
                    if (_tis.$tbeditorsourcedom && $_editht) {
                        _tis.updtbeditrow($_editht, _tis.$tbeditorsourcedom);
                    }
                }
            );
            $(_dom).delegate(
                ".gcs_body_inner td[fld='gs_multisel'] input",
                "change",
                function (e) {
                    var _trdom = $(this).closest("tr");
                    var _selrst = $(_trdom)
                        .find("td[fld='gs_multisel'] input")
                        .prop("checked");
                    _selrst
                        ? $(_trdom).addClass("gcs_cur")
                        : $(_trdom).removeClass("gcs_cur");
                    $(_trdom)
                        .find("td[fld='gs_multisel'] input")
                        .prop("checked", _selrst);
                }
            );

            $(_dom).delegate(
                ".gcs_body_inner td[fld='gs_selradio'] input",
                "change",
                function (e) {
                    var _trdom = $(this).closest("tr");
                    var _selrst = $(_trdom)
                        .find("td[fld='gs_selradio'] input")
                        .prop("checked");
                    if (_selrst) {
                        $(_dom)
                            .find(".gcs_body_inner tr input[type='radio']")
                            .prop("checked", false);
                        $(_dom)
                            .find(".gcs_body_inner tr")
                            .removeClass("gcs_cur");
                        $(_trdom).addClass("gcs_cur");
                        $(this).prop("checked", _selrst);
                    } else {
                        $(_dom)
                            .find(".gcs_body_inner tr input[type='radio']")
                            .prop("checked", false);
                        $(_trdom).removeClass("gcs_cur");
                    }
                }
            );

            $(_dom).delegate(
                ".gcs_body_inner td[fld] .gcs_tablecell",
                "click",
                function (e) {
                    if (_tis.eventcellclick) {
                        var ev = e || window.event;
                        var _rowdom = $(this).closest("tr").get(0);
                        var _row = {};
                        $(_rowdom)
                            .find("td[fld]")
                            .each(function () {
                                var _fld = $(this).attr("fld");
                                if (_fld != "op" && _fld != "gs_multisel") {
                                    _row[_fld] = $(this)
                                        .find(".gcs_tablecell span")
                                        .html();
                                }
                            });
                        //
                        ev.gsdata = $(this).closest("td").attr("fld");
                        ev.gsrowdata = _row;
                        _tis.eventcellclick(ev);
                    }
                }
            );

            $(_dom).delegate(
                ".gcs_body_inner td[fld='op'] .gcs_btn",
                "click",
                function (e) {
                    var ev = e || window.event;
                    var _opcode = $(this).attr("code");
                    var _rowdom = $(this).closest("tr").get(0);
                    var _row = {};
                    $(_rowdom)
                        .find("td[fld]")
                        .each(function () {
                            var _fld = $(this).attr("fld");
                            if (_fld != "op" && _fld != "gs_multisel") {
                                _row[_fld] = $(this)
                                    .find(".gcs_tablecell span")
                                    .html();
                            }
                        });
                    //
                    ev.gsopcode = _opcode;
                    ev.gsdata = _row;
                    ev.gsrow = $(this).closest("tr");
                    //
                    switch (_opcode) {
                        case "m_up":
                            var $prev = $(this).closest("tr").prev();
                            if ($prev) {
                                $(this).closest("tr").insertBefore($prev);
                            }
                            break;
                        case "m_down":
                            var $next = $(this).closest("tr").next();
                            if ($next.attr("fld") == "_gsds_new") {
                                ev.stopPropagation;
                                return;
                            }
                            if ($next) {
                                $(this).closest("tr").insertAfter($next);
                            }
                            break;
                        default:
                            break;
                    }
                    //
                    if (_tis.eventopclick) {
                        _tis.eventopclick(ev);
                    }

                    ev.stopPropagation();
                }
            );
            //
        },
        settbeditorval: function ($editor, rowdom) {
            var _tis = this;
            var _cfgcols = _tis.cfg["cols"];
            for (var it = 0; it < _cfgcols.length; it++) {
                var _ctype = _cfgcols[it]["ctype"];
                var _val = $(rowdom)
                    .find("td[fld='" + _cfgcols[it]["name"] + "']>div")
                    .attr("title");
                switch (_ctype) {
                    case "text":
                        $editor
                            .find(
                                "td[fld='" +
                                    _cfgcols[it]["name"] +
                                    "'] input.gcs_tbeditor_txt"
                            )
                            .val(_val);
                        break;
                    case "num":
                        $editor
                            .find(
                                "td[fld='" +
                                    _cfgcols[it]["name"] +
                                    "'] input.gcs_tbeditor_txt"
                            )
                            .val(_val);
                        break;
                    case "ddl":
                        //TODO:
                        break;
                    default:
                        break;
                }
            }
        },
        updtbeditrow: function ($editor, $row) {
            var _tis = this;
            var _cfgcols = _tis.cfg["cols"];
            //
            $row.find("td").each(function () {
                var _fld = $(this).attr("fld");
                if (_fld) {
                    var $_rowcell = $(this).find("div.gcs_tablecell");
                    var _editval;
                    //
                    var _name = _fld;
                    //
                    for (var it = 0; it < _cfgcols.length; it++) {
                        if (_cfgcols[it]["name"] == _name) {
                            var _ctype = _cfgcols[it]["ctype"];
                            switch (_ctype) {
                                case "text":
                                    _editval = $editor
                                        .find(
                                            "td[fld='" +
                                                _fld +
                                                "'] > div > span input"
                                        )
                                        .val();
                                    break;
                                case "num":
                                    _editval = $editor
                                        .find(
                                            "td[fld='" +
                                                _fld +
                                                "'] > div > span input"
                                        )
                                        .val();
                                    break;
                                case "ddl":
                                    _editval = gsddl00.getvalue(
                                        $editor
                                            .find(
                                                "td[fld='" +
                                                    _fld +
                                                    "'] > div > span > span"
                                            )
                                            .get(0)
                                    );
                                    break;
                                default:
                                    break;
                            }
                            //
                            $_rowcell.find("span:first").html(_editval);
                            $_rowcell.attr("title", _editval);
                        }
                    }
                }
            });
        },
        dropcurrow: function (e) {
            $(e.target).closest("tr").remove();
        },
        _allreadonly: function () {
            var _tis = this;
            var allreadonly = true;
            var _cfgcols = _tis.cfg["cols"];
            for (var it = 0; it < _cfgcols.length; it++) {
                var _ctype = _cfgcols[it]["ctype"];
                var _readonly = _cfgcols[it]["readonly"];
                if (!_readonly) {
                    return false;
                }
            }
            return allreadonly;
        },
        gettbeditor: function (row) {
            var _tis = this;
            var _allreadonly = _tis._allreadonly();
            if (_allreadonly) {
                return null;
            }
            var rtn =
                "<div class='gcs_tb_editrow'><table cellpadding='0' cellspacing='0' border='0'><tr>";
            var _cfgcols = _tis.cfg["cols"];
            //
            if (!_cfgcols) {
                return null;
            }
            //
            if (_tis.cfg["multisel"]) {
                rtn +=
                    "<td fld='gs_multisel'><div class='gcs_tablecell' style='width:30px;text-align:center;'></div></td>";
            } else {
                if (_tis.cfg["radio"]) {
                    rtn +=
                        "<td fld='gs_selradio'><div class='gcs_tablecell' style='width:30px;text-align:center;'></div></td>";
                }
            }
            $(row)
                .find("td")
                .each(function () {
                    var _w = $(this).width();
                    var _cw = _w - 3;
                    var _fld = $(this).attr("fld");
                    if (_fld) {
                        var $_rowcell = $(this).find("div.gcs_tablecell");
                        var _cellval = $_rowcell.find("span:first").html();
                        //
                        var _name = _fld;
                        var _value = _cellval;
                        //

                        for (var it = 0; it < _cfgcols.length; it++) {
                            if (_cfgcols[it]["name"] == _name) {
                                var _ctype = _cfgcols[it]["ctype"];
                                var _readonly = _cfgcols[it]["readonly"];
                                rtn += "<td fld='" + _fld + "'>";
                                switch (_ctype) {
                                    case "text":
                                        rtn +=
                                            "<div class='gcs_tablecell'><span style='width:" +
                                            _w +
                                            "px' >" +
                                            (_readonly
                                                ? ""
                                                : "<input type='text' class='gcs_tbeditor_txt' style='width:" +
                                                  _cw +
                                                  "px' value='" +
                                                  _value +
                                                  "'/>") +
                                            "</span></div>";
                                        break;
                                    case "num":
                                        rtn +=
                                            "<div class='gcs_tablecell'><span>" +
                                            (_readonly
                                                ? ""
                                                : "<span gsat=\"cty: 'numtext', ak: '', fk: '', dk: ''\" class=\"gcs_num_tbeditor gcs_hidden\" style='width:" +
                                                  _cw +
                                                  "px' ></span>") +
                                            "</span></div>";
                                        break;
                                    case "ddl":
                                        var _dtsource =
                                            _cfgcols[it]["ctypecfg"]["dtskey"];
                                        var _cajax = _cfgcols[it]["ctypeajx"];
                                        if (_cajax && _cajax != "") {
                                            rtn +=
                                                "<div class='gcs_tablecell'><span>" +
                                                (_readonly
                                                    ? ""
                                                    : "<span gsat=\"cty: 'combox', source: '" +
                                                      _dtsource +
                                                      "',ajsource:'" +
                                                      _cajax +
                                                      "', ak: 'areakey1', fk: 'funckey1', dk: 'detailkey1'\" class=\"gcs_tbeditor_ddl gcs_hidden\" style='width:" +
                                                      _cw +
                                                      "px'></span>") +
                                                "</span></div>";
                                        } else {
                                            rtn +=
                                                "<div class='gcs_tablecell'><span>" +
                                                (_readonly
                                                    ? ""
                                                    : "<span gsat=\"cty: 'combox', source: '" +
                                                      _dtsource +
                                                      "', ak: 'areakey1', fk: 'funckey1', dk: 'detailkey1'\" class=\"gcs_tbeditor_ddl gcs_hidden\" style='width:" +
                                                      _cw +
                                                      "px'></span>") +
                                                "< /span></div>";
                                        }
                                        break;
                                    default:
                                        break;
                                }
                                rtn += "</td>";
                            }
                        }
                    }
                });
            rtn += "</tr></table></div>";
            //
            return rtn;
        },
        getrowdata: function (rowdom) {
            var _row = {};
            $(rowdom)
                .find("td[fld]")
                .each(function () {
                    var _fld = $(this).attr("fld");
                    if (
                        _fld != "op" &&
                        _fld != "gs_selradio" &&
                        _fld != "gs_multisel"
                    ) {
                        _row[_fld] = $(this)
                            .find(".gcs_tablecell")
                            .attr("title");
                    }
                });
            return _row;
        },
        setcfg: function (cfg) {
            var _tis = this;
            if (cfg) {
                _tis.cfg = $.extend(
                    false,
                    _tis.defaultcfg,
                    _tis.cfg || {},
                    cfg || {}
                );
            }
        },
        __s9_gettablerowdata: function (rowdom) {
            var rtn = {};
            $(rowdom)
                .find("td[fld]")
                .each(function () {
                    var _key = $(this).attr("fld");
                    var _val = $(this).find(".gcs_tablecell>span").html();
                    rtn[_key] = _val;
                });
            return rtn;
        },
        selectrows: function (rows) {
            var _tis = this;
            var _dom = _tis.dom;
            if (typeof rows == "undefined") {
                // get
                if (_tis.cfg["multisel"]) {
                    var rtn0 = [];
                    $(_dom)
                        .find(".gcs_body_inner table tr")
                        .each(function () {
                            var _cboxsel = $(this)
                                .find(
                                    "td[fld='gs_multisel'] input[type='checkbox']"
                                )
                                .prop("checked");
                            if (_cboxsel) {
                                var _rowdata = _tis.__s9_gettablerowdata(this);
                                rtn0.push(_rowdata);
                            }
                        });
                    return rtn0;
                } else {
                    var _selrow = $(_dom)
                        .find(".gcs_body_inner table tr.gcs_cur")
                        .get(0);
                    var rtn1 = null;
                    if (_selrow) {
                        rtn1 = _tis.__s9_gettablerowdata(_selrow);
                    }
                    return [rtn1];
                }
            } else {
                if (rows instanceof Array) {
                    $(_tis.dom)
                        .find(".gcs_body_inner tr")
                        .removeClass("gcs_cur");
                    if (_tis.cfg["multisel"]) {
                        $(_tis.dom)
                            .find(".gcs_body_inner td[fld='gs_multisel'] input")
                            .prop("checked", false);
                    }
                    $(_tis.dom)
                        .find(".gcs_head_inner td[fld='gs_multisel'] input")
                        .prop("checked", false);
                    //
                    $(_tis.dom)
                        .find(".gcs_body_inner tr")
                        .each(function () {
                            var _rowdata = _tis.getrowdata(this);
                            for (var i = 0; i < rows.length; i++) {
                                _tis.selectonerow(rows[i]);
                            }
                        });
                } else {
                    if (typeof rows == "object") {
                        _tis.selectonerow(rows);
                    } else {
                        if (typeof rows == "string") {
                            //TODO: identify ony one column
                            var _identifys = _tis.getidentify();
                            if (_identifys && _identifys.length == 1) {
                                var identifyvals = rows.split(",");
                                for (var x = 0; x < identifyvals.length; x++) {
                                    var _fld = _identifys[0]["name"];

                                    $(_tis.dom)
                                        .find(".gcs_body_inner tr")
                                        .each(function () {
                                            var _rowdata = _tis.getrowdata(
                                                this
                                            );
                                            if (
                                                _rowdata[_fld] ==
                                                identifyvals[x]
                                            ) {
                                                _tis.selectonerow(_rowdata);
                                            }
                                        });
                                }
                            }
                        }
                    }
                }
            }
        },
        getidentify: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var rtn = [];
            for (var i = 0; i < _tis.cfg["cols"].length; i++) {
                if (_tis.cfg["cols"][i]["identify"]) {
                    rtn.push(_tis.cfg["cols"][i]);
                }
            }
            return rtn;
        },
        selectonerow: function (row) {
            var _tis = this;
            var _dom = _tis.dom;
            $(_tis.dom)
                .find(".gcs_body_inner tr")
                .each(function () {
                    var _rowdata = _tis.getrowdata(this);
                    var _eq = true;
                    for (var j = 0; j < _tis.cfg["cols"].length; j++) {
                        var _name = _tis.cfg["cols"][j]["name"];
                        var _identify = _tis.cfg["cols"][j]["identify"];
                        //
                        if (_identify) {
                            if (_rowdata[_name] != row[_name]) {
                                _eq = false;
                                break;
                            }
                        }
                    }
                    if (_eq == true) {
                        $(this).addClass("gcs_cur");
                        if (!_tis.cfg["multisel"]) {
                            return false;
                        } else {
                            $(this)
                                .find("td[fld='gs_multisel'] input")
                                .prop("checked", true);
                        }
                    }
                });
        },
        getdata: function () {
            var _tis = this;
            var _dom = _tis.dom;
            var _rtn = [];
            $(_dom)
                .find(".gcs_body_inner tr")
                .each(function () {
                    if ($(this).attr("fld") != "_gsds_new") {
                        _rtn.push(_tis.getrowdata(this));
                    }
                });
            return _rtn;
        },
        reset: function () {
            var _tis = this;
            _tis.cfg = {};
            _tis.inittable(true);
        },
    };
    GsTable.prototype.defaultcfg = {
        cols: [
            {
                name: "col1",
                length: "200",
                disp: "列1",
                ntype: "string",
                ctype: "text",
                sort: true,
                identify: true,
            },
            {
                name: "col2",
                length: "120",
                disp: "列2",
                ntype: "number",
                ctype: "num",
                sort: false,
            },
            {
                name: "col3",
                length: "120",
                disp: "列3",
                ntype: "string",
                ctype: "ddl",
                cdtsource: "ds_gender",
            },
        ],
        opcols: [
            { code: "edit", name: "edit", title: "编辑", ico: "" },
            {
                code: "drop",
                name: "drop",
                title: "删除",
                ico: "",
            },
        ],
        paging: true,
        enable: true,
        edit: false,
        multisel: true,
        radio: false,
        editmode: "cell",
        newrowallow: false,
        ajustsort: false,
    };

    var GsNewsList = function (dom, cfg) {
        this.dom = dom;
        this.cfg = $.extend(false, this.defaultcfg, cfg || {});
        this.data;
        this.eventitemclick;
        this.init = function () {
            var _t = this;
            _t.eventbind();
        };
    };

    GsNewsList.prototype = {
        eventbind: function () {
            var _t = this;
            $(_t.dom).delegate(".gcs_newslist_item", "click", function (e) {
                $(_t.dom).find(".gcs_newslist_item").removeClass("gcs_cur");
                $(this).addClass("gcs_cur");
                var ev = e || window.event;
                var _id = $(this).attr("cid");
                var _data = _t._getdata(_id);
                ev["gsdata"] = _data;
                if (_t.eventitemclick) {
                    _t.eventitemclick(ev);
                }
            });
        },
        _getdata: function (id) {
            var _t = this;
            if (_t.data && _t.data.length > 0) {
                for (var i = 0; i < _t.data.length; i++) {
                    var _id = _t.data[i][_t.cfg["map"]["idfld"]];
                    if (_id == id) {
                        return _t.data[i];
                    }
                }
            }
            return null;
        },
        select: function (id) {
            var _t = this;
            if (typeof id == "undefined") {
                var _id = $(_t.dom)
                    .find(".gcs_newslist_item.gcs_cur")
                    .attr("cid");
                if (typeof _id == "undefined" || _id == "" || _id == null) {
                    return null;
                }
                return _t._getdata(_id);
            } else {
                $(_t.dom).find(".gcs_newslist_item").removeClass("gcs_cur");
                $(_t.dom)
                    .find(".gcs_newslist_item[cid='" + id + "']")
                    .addClass("gcs_cur");
            }
        },
        data: function (data, cfg) {
            var _t = this;
            if (typeof data == "undefined") {
                return _t.data;
            } else {
                var _ht = "";
                _t.cfg = $.extend(
                    false,
                    _t.defaultcfg,
                    _t.cfg || {},
                    cfg || {}
                );
                var _titlefld = _t.cfg["map"]["titlefld"];
                var _datefld = _t.cfg["map"]["datefld"];
                var _idfld = _t.cfg["map"]["idfld"];
                var _linkfld = _t.cfg["map"]["linkfld"] || "javascript:";
                var _navcode = cfg["navcode"];
                var _navname = cfg["navname"];

                if (data) {
                    for (var i = 0; i < data.length; i++) {
                        var _title = data[i][_titlefld];
                        var _time = new Date(
                            Date.parse(data[i][_datefld].replace(/-/g, "/"))
                        ).Format(_t.cfg["dtfmt"]);
                        var _id = data[i][_idfld];
                        var _top = data[i]["IsTop"];
                        var _file = "";

                        if ("JTYW" == _navcode || "SZXW" == _navcode) {
                            _top = 0;
                        }

                        var _linkfld =
                            "/Portal/NewsCenter/NewsDetail?Code=" +
                            _navcode +
                            "&ID=" +
                            _id +
                            "&navigation=" +
                            _navname;
                        if (_navcode == "SZXW") {
                            _linkfld = data[i]["OtherWebAddres"];
                        }
                        _ht +=
                            "<div class='gcs_newslist_item' cid='" +
                            _id +
                            "' title='" +
                            _title +
                            "'><span class='gcs_title bgtitle'>" +
                            (_top != null && _top > 0
                                ? "<i class='top'></i>"
                                : "") +
                            "<a href='" +
                            _linkfld +
                            "' target='_blank'>" +
                            _title +
                            "</a></span><span class='gcs_dt'>" +
                            _time +
                            "</span></div>";
                    }
                }
                $(_t.dom).html(_ht);
            }
        },
    };

    GsNewsList.prototype.defaultcfg = {
        map: {
            titldfld: "title",
            datefld: "time",
            idfld: "idfld",
            linkfld: "url",
        },
        dtfmt: "MM-dd",
    };

    var HbmXmzxUnit = function (dom) {
        this.dom = dom;
        this._data;
        var _this = this;
        this.cfg;
        this.$linect;
        this.eventnodeclick;
        this.eventmoduleclick;
        var searchresults = [];
        var searchkey;
        var searchcurnum = 0;
        var _afterht =
            "<div class='gcs_xmzx_xmlevel_head_row'> <div class='gcs_xmzx_xmlevel_head_cell gcs_xmzx_xmlevel_headcell_a'>一级节点</div> <div class='gcs_xmzx_xmlevel_head_cell gcs_xmzx_xmlevel_headcell_a'>二级节点</div> <div class='gcs_xmzx_xmlevel_head_cell gcs_xmzx_xmlevel_headcell_a'>三级节点</div> <div class='gcs_xmzx_xmlevel_head_cell gcs_xmzx_xmlevel_headcell_b'>模块</div></div><div class='gcs_xmzx_xmlevel_body_ct'></div>";
        var _xmsearch02 = function (xmitem, contdom, key) {
            var _codefld = _this.cfg["codefld"];
            var _namefld = _this.cfg["txtfld"];
            var _childfld = _this.cfg["childfld"];
            var _name = xmitem[_namefld];
            var _child = xmitem[_childfld];
            if (_name && _name.indexOf(key) > -1) {
                searchresults.add(xmitem);
            }
            if (_child && _child.length > 0) {
                for (var i = 0; i < _child.length; i++) {
                    _xmsearch02(_child[i], contdom, key);
                }
            }
        };
        this._resetsearch = function () {
            var _t = this;
            searchresults = [];
            $(_t.dom).find(".gcs_xmzx_node").removeClass("gcs_search_rs");
            $(_t.dom).find(".gcs_xmzx_node").removeClass("gcs_search_focus");
            $(_t.dom)
                .find(".gcs_xmzx_module_singleline_item")
                .removeClass("gcs_search_rs");
            $(_t.dom)
                .find(".gcs_xmzx_module_singleline_item")
                .removeClass("gcs_search_focus");
        };
        this.search = function (key) {
            if (!key) {
                return;
            }
            var _t = this;
            if (searchkey && searchkey == key) {
                _t._searchtonext();
                return;
            }
            _t._resetsearch();
            searchkey = key;
            //
            if (_t._data && _t._data.length > 0) {
                for (var i = 0; i < _t._data.length; i++) {
                    _xmsearch02(_t._data[i], _t.dom, key);
                }
            }
            //
            if (searchresults && searchresults.length > 0) {
                var _codefld = _t.cfg["codefld"];
                for (var i = 0; i < searchresults.length; i++) {
                    var _code = searchresults[i][_codefld];
                    $(_t.dom)
                        .find(".gcs_xmzx_node[cid='" + _code + "']")
                        .addClass("gcs_search_rs");
                    $(_t.dom)
                        .find(
                            ".gcs_xmzx_module_singleline_item[cid='" +
                                _code +
                                "']"
                        )
                        .addClass("gcs_search_rs");
                }
                _t._searchto(searchresults[0][_codefld]);
            }
        };
        this._searchtonext = function () {
            var _t = this;
            var _codefld = _t.cfg["codefld"];
            if (isNaN(searchcurnum)) {
                return;
            }
            if (!searchresults || searchresults.length == 0) {
                return;
            }
            searchcurnum++;
            if (searchcurnum >= searchresults.length || searchcurnum < 0) {
                searchcurnum = 0;
            }
            var _item = searchresults[searchcurnum];
            if (_item) {
                var _code = _item[_codefld];
                _t._searchto(_code);
            }
        };
        this._searchto = function (code) {
            var _t = this;
            $(_t.dom).find(".gcs_xmzx_node").removeClass("gcs_search_focus");
            $(_t.dom)
                .find(".gcs_xmzx_module_singleline_item")
                .removeClass("gcs_search_focus");
            var _nodedom = $(_t.dom)
                .find(".gcs_xmzx_node[cid='" + code + "']")
                .get(0);
            if (!_nodedom) {
                _nodedom = $(_t.dom).find(
                    ".gcs_xmzx_module_singleline_item[cid='" + code + "']"
                );
                if (_nodedom == null) {
                    return;
                }
            }
            $(_nodedom).addClass("gcs_search_focus");
            $("html").animate({ scrollTop: _nodedom.offsetTop - 100 }, "slow");
        };
        this.init = function () {
            var _t = this;
            _tab_xmzx = new GsTab($(_t.dom).find(".gcs_tab_xmzx").get(0));
            _tab_xmzx.init();

            _t.$linect = document.createElementNS(
                "http://www.w3.org/2000/svg",
                "svg"
            );
            $(_t.dom).find(".gcs_xmzx_xmlevelfwk").prepend(this.$linect);
            // $(_t.dom).find(".gcs_xmzx_xmlevelfwk").append(_afterht);

            _t.eventbind();
        };

        this.eventbind = function () {
            var _t = this;
            $(_t.dom).delegate(".gcs_xmzx_node", "click", function (e) {
                var ev = e || window.event;
                if (_t.eventnodeclick) {
                    var _id = $(this).attr("cid");
                    var _data = _t._getdata(_t._data, _id);
                    ev["hbmdata"] = {
                        code: _id,
                        name: _data[_t.cfg["txtfld"]],
                    };
                    ev["hbmdatainner"] = _data;
                    _t.eventnodeclick(ev);
                }
            });

            $(_t.dom).delegate(
                ".gcs_xmzx_module_singleline_item",
                "click",
                function (e) {
                    var ev = e || window.event;
                    if (_t.eventmoduleclick) {
                        var _id = $(this).attr("cid");
                        var _data = _t._getdata(_t._data, _id);
                        ev["hbmdata"] = {
                            code: _id,
                            name: _data[_t.cfg["txtfld"]],
                        };
                        ev["hbmdatainner"] = _data;
                        _t.eventmoduleclick(ev);
                    }
                }
            );
        };
        this._getdata = function (data, id) {
            var _t = this;
            if (!data || data.length == 0) {
                return null;
            }
            for (var i = 0; i < data.length; i++) {
                var _id = data[i][_t.cfg["codefld"]];
                if (_id == id) {
                    return data[i];
                }
                var _child = data[i][_t.cfg["childfld"]];
                if (_child && _child.length > 0) {
                    var _rtn = _t._getdata(_child, id);
                    if (_rtn) {
                        return _rtn;
                    }
                }
            }
            return null;
        };
        this.data = function (data, cfg) {
            var _t = this;
            _t.cfg = $.extend(false, _t.defaultcfg, _t.cfg || {}, cfg || {});
            var _ht = _getht(data);
            _t._data = data;
            $(_t.dom)
                .find(".gcs_xmzx_xmlevelfwk .gcs_xmzx_xmlevel_body_ct")
                .html(_ht);
            var $thetabitem = $(_t.dom).closest(".gcs_tabitem");
            var _hid = $thetabitem.is(":hidden");
            if (_hid) {
                $thetabitem.show();
            }
            _t._resize();
            //
            var _level = 1;
            for (var i = 0; i < _t._data.length; i++) {
                var _code = _t._data[i][_t.cfg["codefld"]];
                var _child = _t._data[i][_t.cfg["childfld"]];
                _t.drawline(_code, _child, _level);
            }
            if (_hid) {
                $thetabitem.hide();
            }
        };
        this.setpaneltitle = function (titleinfo) {
            var _t = this;
            var _p1title = titleinfo["l1"];
            if (typeof _p1title != "undefined") {
                $(_t.dom)
                    .find(".gcs_xmzx_xmlevel_head_row div:nth-child(1)")
                    .html(_p1title);
            }
            var _p2title = titleinfo["l2"];
            if (typeof _p2title != "undefined") {
                $(_t.dom)
                    .find(".gcs_xmzx_xmlevel_head_row div:nth-child(2)")
                    .html(_p2title);
            }
            var _p3title = titleinfo["l3"];
            if (typeof _p3title != "undefined") {
                $(_t.dom)
                    .find(".gcs_xmzx_xmlevel_head_row div:nth-child(3)")
                    .html(_p3title);
            }
            var _p4title = titleinfo["l4"];
            if (typeof _p4title != "undefined") {
                $(_t.dom)
                    .find(".gcs_xmzx_xmlevel_head_row div:nth-child(4)")
                    .html(_p4title);
            }
        };
        this.drawline = function (code, chlddata, level) {
            var _t = this;
            var _dockerdom = $(_t.dom).find(".gcs_xmzx_xmlevelfwk").get(0);
            var _dockerposi = getElCoordinate(_dockerdom);
            if (chlddata && chlddata.length > 0) {
                for (var i = 0; i < chlddata.length; i++) {
                    var _code = chlddata[i][_t.cfg["codefld"]];
                    var _child = chlddata[i][_t.cfg["childfld"]];
                    //
                    var _nodedm1 = $(_t.dom)
                        .find(".gcs_xmzx_node[cid='" + code + "']")
                        .get(0);
                    var _nodedm2 = $(_t.dom)
                        .find(".gcs_xmzx_node[cid='" + _code + "']")
                        .get(0);

                    if (level == 3) {
                        _nodedm2 = $(_t.dom)
                            .find(".gcs_xmzx_module[pid='" + code + "']")
                            .get(0);
                    }
                    if (!_nodedm1 || !_nodedm2) {
                        continue;
                    }
                    var _posi1 = getElCoordinate(_nodedm1);
                    var _posi2 = getElCoordinate(_nodedm2);

                    // var _x1 = _posi1.left + $(_nodedm1).outerWidth() / 2 - _dockerposi.left;
                    // var _y1 = _posi1.top + $(_nodedm1).outerHeight() / 2 - _dockerposi.top;
                    // var _x2 = _posi2.left + $(_nodedm2).outerWidth() / 2 - _dockerposi.left;
                    // var _y2 = _posi2.top + $(_nodedm2).outerHeight() / 2 - _dockerposi.top;
                    var _x1 =
                        _posi1.left +
                        $(_nodedm1).outerWidth() -
                        1 -
                        _dockerposi.left;
                    var _y1 =
                        _posi1.top +
                        $(_nodedm1).outerHeight() / 2 -
                        _dockerposi.top;
                    var _x2 = _posi2.left + 1 - _dockerposi.left;
                    var _y2 =
                        _posi2.top +
                        $(_nodedm2).outerHeight() / 2 -
                        _dockerposi.top;
                    //
                    _t._drawline({ x: _x1, y: _y1 }, { x: _x2, y: _y2 });
                    if (_child && _child.length > 0) {
                        _t.drawline(_code, _child, level + 1);
                    }
                }
            }
        };
        this._resize = function () {
            var _t = this;
            $(_t.dom)
                .find(".gcs_xmzx_node")
                .each(function () {
                    var $_cell = $(this).closest(".gcs_xmzx_cell");
                    var _h = $_cell.height();
                    var _th = $(this).height();
                    $(this).css("margin-top", (_h - _th) / 2 + "px");
                });

            $(_t.dom)
                .find(".gcs_xmzx_module")
                .each(function () {
                    var $_cell = $(this).closest(".gcs_xmzx_cell");
                    var _h = $_cell.height();
                    var _th = $(this).outerHeight();
                    $(this).css("margin-top", (_h - _th) / 2 + "px");
                });

            var _th = 0;
            $(_t.dom)
                .find(".gcs_xmzx_xmlevel1_row")
                .each(function () {
                    _th += $(this).height();
                });
            $(_t.dom)
                .find(".gcs_xmzx_xmlevelfwk")
                .height(_th + 60);
        };
        var _getht = function (data) {
            var _txtfld = _this.cfg["txtfld"];
            var _codefld = _this.cfg["codefld"];
            var _imgfld = _this.cfg["imgfld"];
            var _childfld = _this.cfg["childfld"];
            //
            var _ht = "";
            //
            for (var i = 0; i < data.length; i++) {
                var _code = data[i][_codefld];
                var _name = data[i][_txtfld];
                var _imgurl = data[i][_imgfld];
                var _url = data[i]["Url"];
                var _havechld =
                    data[i][_childfld] && data[i][_childfld].length > 0;
                var _havechldcls = _havechld ? "" : " gcs_nonechld ";
                var _haveurlcls = _havechld
                    ? _url
                        ? ""
                        : " gcs_hchld_nurl"
                    : "";
                _ht +=
                    "<div class='gcs_xmzx_xmlevel1_row'><div class='gcs_xmzx_cell gcs_xmzx_xmlevel1_cell gcs_xmzx_xmlevel_cell_a1'>";
                // _ht += "<div class='gcs_xmzx_xmlevelfwk_item gcs_xmzx_xmlevelfwk_itema'> <div class='gcs_xmzx_node gcs_xmzx_node_l1" + _havechldcls + "' cid='" + _code + "'> <span class='gcs_xmzx_node_ico'><img src='" + _imgurl + "'></span> <span class='gcs_xmzx_node_name'>" + _name + "</span> </div></div>";
                _ht +=
                    "<div class='gcs_xmzx_xmlevelfwk_item gcs_xmzx_xmlevelfwk_itema'> <div class='gcs_xmzx_node gcs_xmzx_node_l1" +
                    _havechldcls +
                    _haveurlcls +
                    "' cid='" +
                    _code +
                    "'> <span class='gcs_xmzx_node_ico'></span> <span class='gcs_xmzx_node_name'>" +
                    _name +
                    "</span> </div></div>";
                _ht += "</div>";
                _ht += "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel1_cell'>";
                if (_havechld) {
                    var _child1 = data[i][_childfld];
                    for (var j = 0; j < _child1.length; j++) {
                        _ht += "<div class='gcs_xmzx_xmlevel2_row'>";
                        //
                        var _code1 = _child1[j][_codefld];
                        var _name1 = _child1[j][_txtfld];
                        var _imgurl1 = _child1[j][_imgfld];
                        var _url1 = _child1[j]["Url"];
                        var _havechld1 =
                            _child1[j][_childfld] &&
                            _child1[j][_childfld].length > 0;
                        var _havechldcls1 = _havechld1 ? "" : " gcs_nonechld ";
                        var _haveurlcls1 = _havechld1
                            ? _url1
                                ? ""
                                : " gcs_hchld_nurl"
                            : "";
                        // _ht += "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel2_cell gcs_xmzx_xmlevel_cell_a'> <div class='gcs_xmzx_xmlevelfwk_item gcs_xmzx_xmlevelfwk_itema'> <div class='gcs_xmzx_node gcs_xmzx_node_l2" + _havechldcls1 + "' cid='" + _code1 + "'> <span class='gcs_xmzx_node_ico'><img src='" + _imgurl1 + "'></span> <span class='gcs_xmzx_node_name' title='" + _name1 + "'>" + _name1 + "</span> </div> </div></div>";
                        _ht +=
                            "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel2_cell gcs_xmzx_xmlevel_cell_a'> <div class='gcs_xmzx_xmlevelfwk_item gcs_xmzx_xmlevelfwk_itema'> <div class='gcs_xmzx_node gcs_xmzx_node_l2" +
                            _havechldcls1 +
                            _haveurlcls1 +
                            "' cid='" +
                            _code1 +
                            "'>  <span class='gcs_xmzx_node_name' title='" +
                            _name1 +
                            "'><span class='gcs_xmzx_node_ico'></span>" +
                            _name1 +
                            "</span> </div> </div></div>";
                        //
                        _ht +=
                            "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel2_cell gcs_xmzx_xmlevel_cell_p'>";
                        if (_havechld1) {
                            var _child2 = _child1[j][_childfld];
                            for (var k = 0; k < _child2.length; k++) {
                                _ht += "<div class='gcs_xmzx_xmlevel3_row'>";
                                //
                                var _code2 = _child2[k][_codefld];
                                var _name2 = _child2[k][_txtfld];
                                var _url2 = _child2[k]["Url"];
                                var _havechld2 =
                                    _child2[k][_childfld] &&
                                    _child2[k][_childfld].length > 0;
                                var _havechldcls2 = _havechld2
                                    ? ""
                                    : " gcs_nonechld ";
                                var _haveurlcls2 = _havechld2
                                    ? _url2
                                        ? ""
                                        : " gcs_hchld_nurl"
                                    : "";
                                //
                                _ht +=
                                    "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel3_cell gcs_xmzx_xmlevel_cell_a'> <div class='gcs_xmzx_node gcs_xmzx_node_l3" +
                                    _havechldcls2 +
                                    _haveurlcls2 +
                                    "' cid='" +
                                    _code2 +
                                    "'> <span class='gcs_xmzx_node_name' title='" +
                                    _name2 +
                                    "'>" +
                                    _name2 +
                                    "</span> </div></div>";
                                //
                                _ht +=
                                    "<div class='gcs_xmzx_cell gcs_xmzx_xmlevel4_cell gcs_xmzx_xmlevel_cell_b'>";
                                if (_havechld2) {
                                    var _child3 = _child2[k][_childfld];
                                    _ht +=
                                        "<div class='gcs_xmzx_module' pid='" +
                                        _code2 +
                                        "'>";
                                    var _close = true;
                                    for (var x = 0; x < _child3.length; x++) {
                                        //var _auth = _child3[x]["auth"];
                                        //if (!_auth) {
                                        //    continue;
                                        //}
                                        //
                                        var _code3 = _child3[x][_codefld];
                                        var _name3 = _child3[x][_txtfld];
                                        var _url = _child3[x]["Url"];
                                        var _noneurlst = _url
                                            ? ""
                                            : " gcs_xmzx_module_urlnone ";
                                        if ((x + 1) % 3 == 1) {
                                            _ht +=
                                                "<div class='gcs_xmzx_module_singleline'>";
                                            _close = false;
                                        }
                                        _ht +=
                                            "<div class='gcs_xmzx_module_singleline_item" +
                                            _noneurlst +
                                            "' cid='" +
                                            _code3 +
                                            "' title='" +
                                            _name3 +
                                            "'> <span>" +
                                            _name3 +
                                            "</span><span>>></span></div>";
                                        if ((x + 1) % 3 == 0) {
                                            _ht += "</div>";
                                            _close = true;
                                        }
                                        //
                                    }
                                    if (!_close) {
                                        _ht += "</div>";
                                    }
                                    _ht += "</div>";
                                }
                                _ht += "</div>";
                                //
                                _ht += "</div>";
                            }
                        }
                        _ht += "</div>";
                        //
                        _ht += "</div>";
                    }
                }
                _ht += "</div>";
                _ht += "</div>";
            }
            return _ht;
        };

        this._drawline = function (from, to) {
            var _t = this;
            var line = document.createElementNS(
                "http://www.w3.org/2000/svg",
                "g"
            );
            var path = document.createElementNS(
                "http://www.w3.org/2000/svg",
                "path"
            );
            var xmid = to["x"] + (from["x"] - to["x"]) / 1.3;
            var mida = { x: xmid, y: from["y"] };
            var midb = { x: xmid, y: to["y"] };
            //
            line.setAttribute("from", from["x"] + "," + from["y"]);
            line.setAttribute("to", to["x"] + "," + to["y"]);
            path.setAttribute(
                "d",
                "M " +
                    from["x"] +
                    " " +
                    from["y"] +
                    " L " +
                    mida["x"] +
                    " " +
                    mida["y"] +
                    " L " +
                    midb["x"] +
                    " " +
                    midb["y"] +
                    " L " +
                    to["x"] +
                    " " +
                    to["y"]
            );
            path.setAttribute("stroke-width", 1);
            path.setAttribute("stroke-linecap", "round");
            path.setAttribute("fill", "none");
            path.setAttribute("style", "stroke-dasharray:6,5");
            path.setAttribute("stroke", "#909CA9");
            line.appendChild(path);
            _t.$linect.appendChild(line);
        };
    };
    HbmXmzxUnit.prototype.defaultcfg = {
        txtfld: "Name",
        codefld: "Id",
        childfld: "Children",
    };
    var _scgetopflag = function () {
        var rtn = {};
        var _skey = _getquerystring("sKey");
        var _stepkey = _getquerystring("StepKey");
        var _id =
            _getquerystring("Id") ||
            _getquerystring("ID") ||
            _getquerystring("id");
        var _templetcode = _getquerystring("TempletCode");
        if (_skey) {
            rtn["sKey"] = _skey;
        }
        if (_stepkey) {
            rtn["StepKey"] = _stepkey;
        }
        if (_id) {
            rtn["Id"] = _id;
        }
        if (_templetcode) {
            rtn["TempletCode"] = _templetcode;
        }
        return rtn;
    };
    var _scgetopflag1 = function () {
        var rtn = {};
        var _id =
            _getquerystring("Id") ||
            _getquerystring("ID") ||
            _getquerystring("id");
        if (_id) {
            rtn["Id"] = _id;
        }
        return rtn;
    };
    var HbmFileSc = function (dom) {
        this.dom = dom;
        var _this = this;
        var _getfileinfo = function (scdom) {
            var $_lk;
            if (scdom.tagName == "A") {
                $_lk = $(scdom);
            } else {
                $_lk = $(scdom).next();
            }
            var _lk = $_lk.attr("href");
            var _filename = "";
            if (!_lk) {
                $_lk = $(scdom).parent().find("a");
                _lk = $_lk.attr("href");
            }
            var _fwrmflg = false;
            $(scdom)
                .parent()
                .find("a")
                .each(function () {
                    if ($(this).html() == "点击此处打开正文内容") {
                        _fwrmflg = true;
                        return false;
                    }
                });
            _filename = $_lk.html() || "";
            var _aliasname = "";
            if (!_lk) {
                var $old_lk = $_lk.closest(".custom-attachment-list-old");
                if ($old_lk.length == 1) {
                    _filename = $_lk.data("attachment");
                    _aliasname = "attachmentold";
                    _lk = $_lk.data("attachment");
                    //var _url = "http://10.10.1.29/Portal/Home/download?name=" + encodeURI()
                    ////待定
                } else {
                    // new_lk  待定
                    _filename = $_lk.data("attachment");
                    _aliasname = "attachmentnew";
                    _lk = $_lk.data("attachment");
                }
            }
            return [_lk, _aliasname, _fwrmflg];
        };
        var sc = function (dom, cgroup) {
            var _fileinfo = _getfileinfo(dom);
            //
            var _opfalg = _scgetopflag1();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/scmyfav/",
                {
                    fileKey: encodeURI(_fileinfo[0]),
                    fileName: _fileinfo[0],
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    group: "",
                    opflag: _fileinfo[2] ? "" : JSON.stringify(_opfalg),
                    aliasName: _fileinfo[1],
                },
                null,
                null,
                function (dat) {
                    console.log(dat["msg"]);
                    $(dom).addClass("hbm-file-sced");
                    $(dom).attr("title", "已收藏(点击取消收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var cancelsc = function (dom) {
            var _fileinfo = _getfileinfo(dom);
            var _lk = _fileinfo[0];
            //
            var _opfalg = _scgetopflag();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/cancelscmyfav/",
                {
                    fileKey: encodeURI(_lk),
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    opflag: JSON.stringify(_opfalg),
                },
                null,
                null,
                function (dat) {
                    $(dom).removeClass("hbm-file-sced");
                    $(dom).attr("title", "未收藏(点击收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var initscstate = function () {
            var _lks = "";
            var _lksarr = [];
            $(_this.dom)
                .find(".hbm-file-sc")
                .each(function () {
                    var _lpFileinfo = _getfileinfo(this);
                    var _lk = _lpFileinfo[0];
                    _lks += _lk + ",";
                    _lksarr.push(_lk);
                });
            _cmaj(
                null,
                "get",
                serverroot00 + "files/getscflags",
                {
                    fileKeys: _lks,
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                },
                null,
                null,
                function (dat) {
                    var _data = dat["data"];
                    var _lkflagifo = {};
                    if (_data && _data.length > 0) {
                        for (var i = 0; i < _data.length; i++) {
                            var _lk = _lksarr[i];
                            var _lkflag = _data[i];
                            _lkflagifo[_lk] = _lkflag;
                        }
                    }
                    $(_this.dom)
                        .find(".ke-insertfile")
                        .each(function () {
                            var _aimlk = $(this).attr("href");
                            var _aimlkflag = _lkflagifo[_aimlk];
                            if (_aimlkflag) {
                                $(this).prev().addClass("hbm-file-sced");
                                $(this)
                                    .prev()
                                    .attr("title", "已收藏(点击取消收藏文件)");
                            } else {
                                $(this).prev().removeClass("hbm-file-sced");
                                $(this)
                                    .prev()
                                    .attr("title", "未收藏(点击收藏文件)");
                            }
                        });
                    $(_this.dom)
                        .find("a")
                        .each(function () {
                            if (!$(this).hasClass("ke-insertfile")) {
                                var _aimfileinfo = _getfileinfo(this);
                                var _aimlk = _aimfileinfo[0];
                                var _aimlkflag = _lkflagifo[_aimlk];
                                var _scdom = null;
                                if (
                                    $(this).prev().hasClass("hbm-file-sc") ||
                                    $(this).prev().hasClass("hbm-file-sced")
                                ) {
                                    _scdom = $(this).prev().get(0);
                                } else {
                                    var _dom1 = $(this)
                                        .parent()
                                        .find(".hbm-file-sced")
                                        .get(0);
                                    var _dom2 = $(this)
                                        .parent()
                                        .find(".hbm-file-sc")
                                        .get(0);
                                    //
                                    _scdom = _dom1 || _dom2;
                                }
                                if (_scdom) {
                                    if (_aimlkflag) {
                                        $(_scdom).addClass("hbm-file-sced");
                                        $(_scdom).attr(
                                            "title",
                                            "已收藏(点击取消收藏文件)"
                                        );
                                    } else {
                                        $(_scdom).removeClass("hbm-file-sced");
                                        $(_scdom).attr(
                                            "title",
                                            "未收藏(点击收藏文件)"
                                        );
                                    }
                                }
                            }
                        });
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        this.build = function () {
            var _t = this;
            $(_t.dom)
                .find("a.ke-insertfile")
                .each(function () {
                    var _text = $(this).text();
                    if (
                        !$(this).prev().hasClass("hbm-file-sc") &&
                        _text &&
                        _text.trim()
                    ) {
                        $(this).before("<span class='hbm-file-sc'></span>");
                    }
                });
            $(_t.dom)
                .find("a.ke-insertfile")
                .parent()
                .addClass("hbm-filepanel");
            $(_t.dom).find("#filename").parent().addClass("hbm-filepanel");

            $(_t.dom)
                .find("a")
                .each(function () {
                    if (!$(this).hasClass("ke-insertfile")) {
                        if ($(this).html() == "点击此处打开正文内容") {
                            // $(_t.dom).prepend("<span class='hbm-file-sc'></span>");
                        } else {
                            var _text = $(this).text();
                            if (
                                $(this).attr("href") &&
                                $(this).attr("href").indexOf("PageOffice") > -1
                            ) {
                                if (
                                    !$(this).prev().hasClass("hbm-file-sc") &&
                                    _text &&
                                    _text.trim()
                                ) {
                                    $(this).before(
                                        "<span class='hbm-file-sc'></span>"
                                    );
                                }
                            }
                            if ($(_t.dom).attr("id") == "detail") {
                                if (
                                    !$(this).prev().hasClass("hbm-file-sc") &&
                                    _text &&
                                    _text.trim()
                                ) {
                                    $(this).before(
                                        "<span class='hbm-file-sc'></span>"
                                    );
                                }
                            }
                            if ($(this).attr("id") == "filename") {
                                if (
                                    $(this).prev().length == 0 &&
                                    _text &&
                                    _text.trim()
                                ) {
                                    $(this).before(
                                        "<span class='hbm-file-sc'></span>"
                                    );
                                }
                            }
                            if (
                                $(this).attr("href") &&
                                $(this).attr("href").indexOf("ftp:") > -1
                            ) {
                                if (
                                    !$(this).prev().hasClass("hbm-file-sc") &&
                                    _text &&
                                    _text.trim()
                                ) {
                                    $(this).before(
                                        "<span class='hbm-file-sc'></span>"
                                    );
                                }
                            }
                            if ($(this).data("attachment")) {
                                if (
                                    !$(this).prev().hasClass("hbm-file-sc") &&
                                    _text &&
                                    _text.trim()
                                ) {
                                    $(this).before(
                                        "<span class='hbm-file-sc'></span>"
                                    );
                                }
                            }
                        }
                    }
                });
            //
            initscstate();
            //
            _t.eventbind();
        };

        this.eventbind = function () {
            var _t = this;
            $(_t.dom)
                .find(".hbm-file-sc")
                .on("click", function () {
                    if ($(this).hasClass("hbm-file-sced")) {
                        cancelsc(this);
                    } else {
                        sc(this);
                    }
                });
        };
    };
    var HbmFileSc02 = function (dom) {
        this.dom = dom;
        var _this = this;
        var sc = function (dom, cgroup) {
            var $_lk = $(dom).prev().find("a");
            var _lkfile = $_lk.attr("file");
            //
            var _opfalg = _scgetopflag();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/scmyfav/",
                {
                    fileKey: encodeURI(_lkfile),
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    group: cgroup || "kd",
                    opflag: JSON.stringify(_opfalg),
                },
                null,
                null,
                function (dat) {
                    $(dom).addClass("hbm-miniupload-file-sced");
                    $(dom).attr("title", "已收藏(点击取消收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var cancelsc = function (dom) {
            var $_lk = $(dom).prev().find("a");
            var _lkfile = $_lk.attr("file");
            //
            var _opfalg = _scgetopflag();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/cancelscmyfav/",
                {
                    fileKey: encodeURI(_lkfile),
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    opflag: JSON.stringify(_opfalg),
                },
                null,
                null,
                function (dat) {
                    $(dom).removeClass("hbm-miniupload-file-sced");
                    $(dom).attr("title", "未收藏(点击收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var initscstate = function () {
            var _lks = "";
            var _lksarr = [];
            $(_this.dom)
                .find(".hbm-miniupload-file-sc")
                .each(function () {
                    var $_lk = $(this).prev().find("a");
                    var _lkfile = $_lk.attr("file");
                    _lks += _lkfile + ",";
                    _lksarr.push(_lkfile);
                });

            _cmaj(
                null,
                "get",
                serverroot00 + "files/getscflags",
                {
                    fileKeys: _lks,
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                },
                null,
                null,
                function (dat) {
                    var _data = dat["data"];
                    var _lkflagifo = {};
                    if (_data == null || _data.length == 0) {
                        return;
                    }
                    for (var i = 0; i < _data.length; i++) {
                        var _lk = _lksarr[i];
                        var _lkflag = _data[i];
                        _lkflagifo[_lk] = _lkflag;
                    }
                    $(_this.dom)
                        .find(".hbm-miniupload-file-sc")
                        .each(function () {
                            var $_lk = $(this).prev().find("a");
                            var _aimlk = $_lk.attr("file");
                            var _aimlkflag = _lkflagifo[_aimlk];
                            if (_aimlkflag) {
                                $(this).addClass("hbm-miniupload-file-sced");
                                $(this).attr(
                                    "title",
                                    "已收藏(点击取消收藏文件)"
                                );
                            } else {
                                $(this).removeClass("hbm-miniupload-file-sced");
                                $(this).attr("title", "未收藏(点击收藏文件)");
                            }
                        });
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        this.build = function () {
            var _t = this;
            //
            initscstate();
            //
            _t.eventbind();
        };

        this.eventbind = function () {
            var _t = this;
            $(_t.dom).delegate(".hbm-miniupload-file-sc", "click", function () {
                if ($(this).hasClass("hbm-miniupload-file-sced")) {
                    cancelsc(this);
                } else {
                    sc(this);
                }
            });
        };
    };
    var HbmFileSc03 = function (dom) {
        this.dom = dom;
        var _this = this;
        var sc = function (dom, cgroup) {
            var $_lk = $(dom).closest(".mini-singlefile").find("input");
            var _lkfile = $_lk.attr("file");
            //
            var _opfalg = _scgetopflag();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/scmyfav/",
                {
                    fileKey: encodeURI(_lkfile),
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    group: cgroup || "kd",
                    opflag: JSON.stringify(_opfalg),
                },
                null,
                null,
                function (dat) {
                    $(dom).addClass("hbm-singlefile-sced");
                    $(dom).attr("title", "已收藏(点击取消收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var cancelsc = function (dom) {
            var $_lk = $(dom).closest(".mini-singlefile").find("input");
            var _lkfile = $_lk.attr("file");
            //
            var _opfalg = _scgetopflag();
            _cmaj(
                null,
                "post",
                serverroot00 + "files/cancelscmyfav/",
                {
                    fileKey: encodeURI(_lkfile),
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                    opflag: JSON.stringify(_opfalg),
                },
                null,
                null,
                function (dat) {
                    console.log(dat["msg"]);
                    $(dom).removeClass("hbm-singlefile-sced");
                    $(dom).attr("title", "未收藏(点击收藏文件)");
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        var initscstate = function () {
            var _lks = "";
            var _lksarr = [];
            $(_this.dom)
                .find(".hbm-singlefile-sc")
                .each(function () {
                    var $_lk = $(this)
                        .closest(".mini-singlefile")
                        .find("input");
                    var _lkfile = $_lk.attr("file");
                    _lks += _lkfile + ",";
                    _lksarr.push(_lkfile);
                });
            _cmaj(
                null,
                "get",
                serverroot00 + "files/getscflags",
                {
                    fileKeys: _lks,
                    userId: _userid || userStore.getUser.id,
                    deptId: _deptid || userStore.getUser.deptId,
                },
                null,
                null,
                function (dat) {
                    var _data = dat["data"];
                    var _lkflagifo = {};
                    if (_data == null || _data.length == 0) {
                        return;
                    }
                    for (var i = 0; i < _data.length; i++) {
                        var _lk = _lksarr[i];
                        var _lkflag = _data[i];
                        _lkflagifo[_lk] = _lkflag;
                    }
                    $(_this.dom)
                        .find(".hbm-singlefile-sc")
                        .each(function () {
                            var $_lk = $(this)
                                .closest(".mini-singlefile")
                                .find("input");
                            var _aimlk = $_lk.attr("file");
                            var _aimlkflag = _lkflagifo[_aimlk];
                            if (_aimlkflag) {
                                $(this).addClass("hbm-singlefile-sced");
                                $(this).attr(
                                    "title",
                                    "已收藏(点击取消收藏文件)"
                                );
                            } else {
                                $(this).removeClass("hbm-singlefile-sced");
                                $(this).attr("title", "未收藏(点击收藏文件)");
                            }
                        });
                },
                function (err) {
                    console.log(err);
                }
            );
        };
        this.build = function () {
            var _t = this;
            //
            initscstate();
            //
            _t.eventbind();
        };

        this.eventbind = function () {
            var _t = this;
            $(_t.dom).delegate(".hbm-singlefile-sc", "click", function () {
                if ($(this).hasClass("hbm-singlefile-sced")) {
                    cancelsc(this);
                } else {
                    sc(this);
                }
            });
        };
    };


    var GsFileUpload = function (dom) {
        this.dom = dom;
        var _ht = "<div class='gcs_fileupload_ctinner'><input type='file' class='hbm-h5file' style='display:none;'/><span class='gcs_uploadintpuval'></span><span class='gcs_fileupload_buttonsp'>浏览</span>&nbsp;<span class='gcs_buttonsp'><input type='button' class='gcs_btn_upload icon-upload' value='上传' /></span><span><input type='button' class='gcs_btn_download icon-download'value='下载'/></span></div>";
        this.uploadprogresspanel;
        this.eventuploadsuccess;
        this.eventupload;
        this.eventdownload;
        this.eventuploadcompleted;
        this.eventfileselect;
        this.uploadurl;
        this.$upload;
        var _this = this;
        //
        var _uploadfilesingle = function (file) {
            var oData = new FormData();
            oData.append("file", file);
            oData.append("filesize", file["size"]);
            oData.append("Src", _getquerystring("Src"));
            oData.append("RelateId", _getquerystring("RelateId"));
            oData.append("IsLog", _getquerystring("IsLog"));

            //上传进度监听方法
            var xhrOnProgress = function (fun) {
                xhrOnProgress.onprogress = fun; //绑定监听
                //使用闭包实现监听绑
                return function () {
                    //通过$.ajaxSettings.xhr();获得XMLHttpRequest对象
                    var xhr = $.ajaxSettings.xhr();
                    //判断监听函数是否为函数
                    if (typeof xhrOnProgress.onprogress !== 'function')
                        return xhr;
                    //如果有监听函数并且xhr对象支持绑定时就把监听函数绑定上去
                    if (xhrOnProgress.onprogress && xhr.upload) {
                        xhr.upload.onprogress = xhrOnProgress.onprogress;
                    }
                    return xhr;
                }
            }
            $.ajax({
                type: "post",
                url: _this.uploadurl,
                processData: false,
                contentType: false,
                data: oData,
                xhr: xhrOnProgress(function (e) {
                    //关键数据
                    /*稍微处理一下，看看控制台的打印数据就是上传的进度，
                     想要看明白，就控制一下你的网速，在浏览器控制台的
                     Network选项里面可以选择网速，选择fast 3g就可以啦。*/
                    var percent = e.loaded / e.total;
                    var num = percent * 100;
                    var updedvalue = file["size"] * percent;
                    var updtotal = file["size"];
                    //$(".hbm-fileuploadprocess>span:nth-child(1)").html(updedvalue);
                    //$(".hbm-fileuploadprocess>span:nth-child(3)").html(updtotal);
                    _this.showuploadprogress(num);

                }),
                success: function (r) {
                    $(_this.dom).find(".gcs_uploadingpanel").remove();
                    if (_this.eventuploadcompleted) {
                        _this.eventuploadcompleted(r);
                    }
                }
            });
        };
        this.showuploadprogress = function (num) {
            var _t = this;
            var _dom = _t.dom;
            if ($(_dom).find(".gcs_uploadingpanel").length == 0) {
                $(_dom).append("<div class='gcs_uploadingpanel'><div class='gcs_uploadprogress'><span class='gcs_uploadprogressval'></span></div><span class='gcs_uploadprogresslabel'></span></div>");               
            }
            $(_dom).find(".gcs_uploadingpanel").css({ "width": $(_dom).width() + "px", "height": $(_dom).height() + "px" });
            $(_dom).find(".gcs_uploadprogressval").css("width", num + "%");
            $(_dom).find(".gcs_uploadprogresslabel").html(num + "%");
        };
        this.text = function (text) {
            var _t = this;
            if (typeof text == "undefined") {
                return $(_t.dom).find(".gcs_uploadintpuval").html();
            }
            else {
                $(_t.dom).find(".gcs_uploadintpuval").html(text);
            }
        };
        this.init = function () {
            var _t = this;
            var _dom = _t.dom;
            var _acpt = $(_dom).attr("accept") || ".*";
            var _multi = $(_dom).attr("multi") == "true" ? "multiple" : "";
            $(_dom).addClass("gcs_h5fileupload");
            $(_dom).html(_ht);
            //
            _t.$upload = $(_dom).find("input[type='file']");
            _t.$upload.attr("accept", _acpt);
            if (_multi) {
                _t.$upload.attr("multiple", _multi);
            }            
            this.eventbind();
        };
        this.startUpload = function () {
            var _t = this;
            var _dom = _t.dom;
            var _files = _t.$upload.get(0).files;
            for (var i = 0; i < _files.length; i++) {
                _uploadfilesingle(_files[i]);
            }            
        };
        this.setUploadUrl = function (url) {
            var _t = this;
            _t.uploadurl = url;
        };

        this.eventbind = function () {
            var _t = this;
            var _dom = _t.dom;
            $(_dom).find(".gcs_fileupload_buttonsp").on("click", function () {
                _t.$upload.trigger("click");
            });
            $(_dom).find(".gcs_btn_upload").on("click", function (e) {
                var _files = _t.$upload.get(0).files;
                if (!_files || _files.length == 0) {
                    alert("无可上传的文件,操作取消");
                    return;
                }
                if (_t.eventupload) {
                    _t.eventupload(e);
                }
            });

            $(_dom).find(".gcs_btn_download").on("click", function (e) {
                if (_t.eventdownload) {
                    _t.eventdownload(e);
                }
            });

            _t.$upload.on("change", function (e) {
                $(_dom).find(".gcs_uploadintpuval").html(_t.$upload.get(0).files[0]["name"]);
                if (_t.eventfileselect) {
                    var ev = e || window.event;
                    ev["file"] = _t.$upload.get(0).files[0];
                    _t.eventfileselect(e);
                }
            });
        };
    };
    var _correctmenulistposibydocker = function (dockerdom, menulistdom) {
        var dockerposiinfo = getElCoordinate(dockerdom);
        var _wh = $(window).height();
        var _ww = $(window).width();
        var _cleft = dockerposiinfo.left + dockerposiinfo.width;
        var _ctop = dockerposiinfo.top;
        var _mw = $(menulistdom).width();
        var _mh = $(menulistdom).height();
        if (dockerposiinfo.left + dockerposiinfo.width + _mw > _ww) {
            _cleft = _cleft - dockerposiinfo.width - _mw;
        }
        if (dockerposiinfo.top + _mh > _wh) {
            _ctop = _ctop - _mh;
        }
        $(menulistdom).css({ left: _cleft + "px", top: _ctop + "px" });
    };
    var _correctywlistposibydocker = function (dockerdom, menulistdom) {
        var dockerposiinfo = getElCoordinate(dockerdom);
        var _wh = $(window).height();
        var _ww = $(window).width();
        var _cleft = dockerposiinfo.left + dockerposiinfo.width;
        var _ctop = dockerposiinfo.top;
        var _mw = $(menulistdom).width();
        var _mh = $(menulistdom).height();
        if (dockerposiinfo.left + dockerposiinfo.width + _mw > _ww) {
            _cleft = _cleft - dockerposiinfo.width - _mw;
        }
        if (dockerposiinfo.top + _mh > _wh) {
            _ctop = _ctop - _mh;
        }
        if (_ctop <= 60) {
            _ctop = 70;
        }
        $(menulistdom).css({ left: _cleft + "px", top: _ctop + "px" });
    };
    var masterItempage;
    var masterLeftMenu;
    var masterTopMenu;

    var _geturl = function (content, url) {
        var _url = url ? url : content;
        if (_IsUrl(content)) {
            if (_IsAbsUrl(content)) {
                _url = content;
                content = "";
            } else {
                var _reg = /^\//g;
                _url = _reg.test(content)
                    ? serverroot03 + content
                    : serverroot03 + "/" + content;
                content = "";
            }
        } else {
            _url = null;
        }

        if (_url) {
            _url +=
                _url.indexOf("?") > -1
                    ? "&token=" + _token
                    : "?token=" + _token;
        }
        return _url;
    };
    $(document).bind("mousedown", function (e) {
        var ev = e || window.event;
        var _tar = ev.target;
        //
        var $_rballooon = $(_tar).closest(clsBalloonSel);
        if ($_rballooon.length == 0) {
            $(clsBalloonSel).hide();
        }

        var $_rhbmenus = $(_tar).closest("." + clsMenus);
        if ($_rhbmenus.length == 0 && $_rballooon.length == 0) {
            $("." + clsMenus).each(function () {
                if ($(this).closest(clsBalloonSel).length == 0) {
                    $(this).hide();
                }
            });
        }
        var $_dockerxlyw = $(_tar).closest("." + clsDockerYw);
        var $_dockeryw = $(_tar).closest("." + cmpxDockerLxyw);
        if ($_dockerxlyw.length == 0 && $_dockeryw.length == 0) {
            $("." + clsDockerYw).hide();
        }
    });

    var _getdata = function (dom, key) {
        return $(dom).data(key);
    };
    var _setdata = function (dom, key, val) {
        $(dom).data(key, val);
    };
    var HbmMaster = function () {
        this.security = {};
        //
        this.eventtoolbarclick = null;

        this.eventheadsearch = null;

        this.eventaddockerclick = null;
        this.eventpageloaded = null;
        this.init = function () {
            var tabdom = $("." + clsRoot + " .body-page-tab").get(0);
            var lftmenudom = $(
                "." + clsRoot + " ." + clsBody + " ." + clsBodyLeftMenu
            ).get(0);
            var topmenudom = $(clsHeadSel + " .sys_tpmenu_right").get(0);
            var htab = new HbmItempageTab(tabdom);
            htab.init();
            masterItempage = htab;
            masterLeftMenu = new HbmLeftMenu();
            masterLeftMenu.init(lftmenudom);
            masterTopMenu = new HbmTopMenu();
            masterTopMenu.init(topmenudom);
            //
            this.eventbind();
        };

        this._hdsearch = function (ev) {
            var _tis = this;
            if (_tis.eventheadsearch) {
                var _searchkey = $(clsHeadSel + " .sys_tp_search input").val();
                ev["hbmdata"] = _searchkey;
                _tis.eventheadsearch(ev);
            }
        };
    };

    HbmMaster.prototype = {
        eventbind: function () {
            var _tis = this;
            var $_domheadctrl = $(clsHeadControlSel);
            $_domheadctrl.delegate("ul li", "click", function (e) {
                var ev = e || window.event;
                var _tar = ev.target;
                var _tn = _tar.tagName;
                var _aimtar = null;
                switch (_tn) {
                    case "I":
                        _aimtar = $(_tar).closest("li").get(0);
                        break;
                    case "LI":
                        _aimtar = _tar;
                        break;
                    case "A":
                        _aimtar = $(_tar).closest("li").get(0);
                        break;
                    case "SPAN":
                        _aimtar = $(_tar).closest("li").get(0);
                        break;
                }
                if (_aimtar) {
                    if (_tis.eventtoolbarclick) {
                        var _code = $(_aimtar).attr("code");
                        var _name = $(_aimtar).find("i").attr("title");
                        var _data = { code: _code, name: _name };
                        ev.hbmdata = _data;
                        ev.hbmaim = _aimtar;
                        _tis.eventtoolbarclick(ev);
                    }
                }
            });

            $(clsHeadSel + " .hbm-searchpanel").on("click", function (e) {
                if (_tis.eventheadsearch) {
                    var ev = e || window.event;
                    _tis._hdsearch(ev);
                }
            });
            $(clsHeadSel + " .sys_tp_search input").on("keydown", function (e) {
                var ev = e || window.event;
                //
                if (ev.keyCode == 13) {
                    if (_tis.eventheadsearch) {
                        var ev = e || window.event;
                        _tis._hdsearch(ev);
                    }
                }
            });

            $(clsHeadSel + " .header-curinfo").on("click", function (e) {
                var _bdom = $("#hbm-custom-balloon-curuser").get(0);
                _tis.balloon(_bdom).show(this);
            });

            $(clsAdDockerItemSel).on("click", function (e) {
                var ev = e || window.event;
                var _code = $(this).attr("atr");
                _correctywlistposibydocker(
                    $("." + cmpxDockerLxyw).get(0),
                    $("." + clsDockerYw).get(0)
                );
                if (_code == "lxyw") {
                    $("." + clsDockerYw).is(":hidden")
                        ? $("." + clsDockerYw).show()
                        : $("." + clsDockerYw).hide();
                }
                ev["hbmdata"] = _code;
                if (_tis.eventaddockerclick) {
                    _tis.eventaddockerclick(ev);
                }
            });
        },
        correctoaurl: function (url) {
            return _correctoaurl(url);
        },
        addurlparam: function (url, pm, val) {
            return _urladdparam(url, pm, val);
        },
        updateAdDocker: function (cfg) {
            var _left = cfg["left"];
            var _top = cfg["top"];
            var _right = cfg["right"];
            var _bottom = cfg["bottom"];
            if (!isNaN(_left)) {
                $(clsAdDockerSel).css("left", _left + "px");
            }
            if (!isNaN(_top)) {
                $(clsAdDockerSel).css("top", _top + "px");
            }
            if (!isNaN(_right)) {
                $(clsAdDockerSel).css("right", _right + "px");
            }
            if (!isNaN(_bottom)) {
                $(clsAdDockerSel).css("bottom", _bottom + "px");
            }
        },
        title: function (title) {
            if (title) {
                $(clsHeadSel + " .sys_title h3").html(title);
            }
            return $(clsHeadSel + " .sys_title h3").html();
        },
        logo: function (url) {
            if (url) {
                $(clsHeadSel + " .logo img").attr("src", url);
            }
        },
        toolbars: function (toolbars) {
            // [{"code":"","name":"","memo":"","ico":""}]
            if (toolbars && toolbars.length > 0) {
                var _htm = "<ul>";
                for (var i = 0; i < toolbars.length; i++) {
                    var _code = toolbars[i]["code"];
                    var _name = toolbars[i]["name"];
                    var _memo = toolbars[i]["memo"];
                    var _ico = toolbars[i]["ico"];
                    //
                    _htm +=
                        '<li class="ctrl_' +
                        _code +
                        '" code="' +
                        _code +
                        '"><span class="' +
                        _ico +
                        '" title="' +
                        _name +
                        '"></span><span>' +
                        _name +
                        "</span></li>";
                }
                _htm += "</ul>";
                //
                $(clsHeadSel + " .header-control").html(_htm);
            }
        },
        addtool: function (toolitem) {
            var _code = toolitem["code"];
            var _name = toolitem["name"];
            var _memo = toolitem["memo"];
            var _ico = toolitem["ico"];
            var _posi = arguments[1];

            _ht +=
                '<li class="ctrl_' +
                _code +
                '" code="' +
                _code +
                '"><span class="' +
                _ico +
                '" title="' +
                _name +
                '"></span><span>' +
                _name +
                "</span></li>";
            _posi == "first"
                ? $(clsHeadSel + " .header-control ul").prepend(_ht)
                : $(clsHeadSel + " .header-control ul").append(_ht);
        },
        usercenterballoon: function () {
            var _dom = $(clsUserCenterPopSel);
            return _dom.data(httypeobj);
        },
        usercentermenus: function () {
            var _dom = $(clsUserCenterPopSel + " ." + clsMenus);
            return _getdata(_dom, httypeobj);
        },
        menu: function (dom) {
            return _getdata(dom, httypeobj);
        },
        indextab: function (dom) {
            return _getdata(dom, httypeobj);
        },
        balloon: function (dom) {
            return _getdata(dom, httypeobj);
        },
        pagedailog: function (dom) {
            return _getdata(dom, httypeobj);
        },
        alert: function (dom) {
            return _getdata(dom, httypeobj);
        },
        curuserinfo: function (cur) {
            if (cur) {
                var _code = cur["code"];
                var _name = cur["name"];
                $(".ctrl_curinfo a").html(_name).parent().attr("code", _code);
            } else {
                var rtn = {};
                rtn["name"] = $(".ctrl_curinfo a").html();
                rtn["code"] = $(".ctrl_curinfo a").parent().attr("code");
                return rtn;
            }
        },
        loadTopMenus: function () {
            masterTopMenu.load.apply(masterTopMenu, arguments);
            //
            for (var i = 0; i < masterTopMenu.data.length; i++) {
                var _lpUrl = masterTopMenu.data[i]["Url"];
                if (
                    window.location.href.indexOf(masterTopMenu.data[i]["Url"]) >
                        -1 &&
                    _lpUrl &&
                    _lpUrl.length > 0
                ) {
                    var _code = masterTopMenu.data[i]["ResID"];
                    masterTopMenu.active.apply(masterTopMenu, [_code]);
                    break;
                }
            }
        },
        topMenuActive: function (fn) {
            if (fn) {
                masterTopMenu.eventitemactive = fn;
            }
        },
        topMenuClick: function (fn) {
            if (fn) {
                masterTopMenu.eventclick = fn;
            }
        },
        headToolBarClick: function (fn) {
            var _tis = this;
            if (fn) {
                _tis.eventtoolbarclick = fn;
            }
        },
        adDockerItemClick: function (fn) {
            var _tis = this;
            if (fn) {
                _tis.eventaddockerclick = fn;
            }
        },
        addItemPage: function () {
            masterItempage.add.apply(masterItempage, arguments);
        },
        removeItemPage: function () {
            masterItempage.remove.apply(masterItempage, arguments);
        },
        activeItemPage: function () {
            masterItempage.active.apply(masterItempage, arguments);
        },
        headsearch: function (fn) {
            var _tis = this;
            if (fn) {
                _tis.eventheadsearch = fn;
            }
        },
        hideheadsearch: function (hide) {
            var _tis = this;
            hide ? $(".sys_tp_search").hide() : $(".sys_tp_search").show();
        },
        loadLeftMenus: function () {
            masterLeftMenu.load.apply(masterLeftMenu, arguments);
        },
        leftMenuSelected: function (fn) {
            if (fn) {
                masterLeftMenu.eventselected = fn;
            }
        },
        showleftmenu: function (show) {
            show ? $(clsBodyLeftJqsel).show() : $(clsBodyLeftJqsel).hide();
        },
        toggleindex: function () {
            index_mode = index_mode == 0 ? 1 : 0;
            changeindexmode(index_mode);
        },
        activeindex: function () {
            // $(clsBodyLeftJqsel).hide();
            index_mode = 0;
            $(clsBodyContentJqsel).hide();
            $(clsBodyIndexSel).show();
        },
        activeitempagemode: function () {
            // $(clsBodyLeftJqsel).show();
            $(clsBodyIndexSel).hide();
            $(clsBodyContentJqsel).show();
            index_mode = 1;
            resizeme();
        },
        loadvirtualmenu: function (code, content, name, triggertype) {
            var _tis = this;
            var _code = code;
            var _content = content;
            var _name = name;
            var _triggertype = triggertype;
            var _url = null;
            var _ico = "list-alt";
            var _title = arguments[4] ? arguments[4] : _name;
            //
            var _loadtype = _GetExecType(_triggertype);
            switch (_loadtype) {
                case 0:
                    var _content = _url ? _url : _content ? _content : _name;
                    _tis.addItemPage(_code, _name, _content, true, true, _ico);
                    _tis.activeitempagemode();
                    break;
                case 1:
                    var aimUrl = _geturl(_content, _url);
                    aimUrl = aimUrl ? aimUrl : "about:blank";
                    window.open(aimUrl);
                    break;
                case 2:
                    var aimUrl = _geturl(_content, _url);
                    aimUrl = aimUrl ? aimUrl : "about:blank";
                    var _dialogdom = $(pagedialogsel).get(0);
                    var _pagedialog = _getdata(_dialogdom, httypeobj);
                    _pagedialog.thecontent(
                        "<iframe src='" + aimUrl + "'></iframe>"
                    );
                    _pagedialog.title(_title);
                    _pagedialog.config({ width: "900", height: "500" });
                    _pagedialog.show();
                    break;
                default:
                    break;
            }
        },
        settaskcount: function (num) {
            if (isNaN(num)) {
                num = 0;
            }
            if (num <= 0) {
                $(
                    "header .header-control ul li.ctrl_tasks .gcs_taskcount"
                ).remove();
            } else {
                var _numstr = num > 99 ? "99+" : num;
                if (
                    $("header .header-control ul li.ctrl_tasks").find(
                        ".gcs_taskcount"
                    ).length == 0
                ) {
                    if (num > 99) {
                        $("header .header-control ul li.ctrl_tasks").append(
                            "<span class='gcs_taskcount' style='right:-16px;' title='待办:" +
                                num +
                                "'>" +
                                _numstr +
                                "</span>"
                        );
                    } else {
                        $("header .header-control ul li.ctrl_tasks").append(
                            "<span class='gcs_taskcount' title='待办:" +
                                num +
                                "'>" +
                                _numstr +
                                "</span>"
                        );
                    }
                }
            }
        },
        setmsgcount: function (num) {
            if (isNaN(num)) {
                num = 0;
            }
            if (num <= 0) {
                $(
                    "header .header-control ul li.ctrl_message .gcs_msgcount"
                ).remove();
            } else {
                var _numstr = num > 99 ? "99+" : num;
                if (
                    $("header .header-control ul li.ctrl_message").find(
                        ".gcs_msgcount"
                    ).length == 0
                ) {
                    if (num > 99) {
                        $("header .header-control ul li.ctrl_message").append(
                            "<span class='gcs_msgcount' style='right:-13px;' title='消息:" +
                                num +
                                "'>" +
                                _numstr +
                                "</span>"
                        );
                    } else {
                        $("header .header-control ul li.ctrl_message").append(
                            "<span class='gcs_msgcount' title='消息:" +
                                num +
                                "'>" +
                                num +
                                "</span>"
                        );
                    }
                }
            }
        },
        loadleftmenuclick: function (menu) {
            var _tis = this;
            // console.log(menu);
            var _code = menu["code"];
            var _content = menu["content"];
            var _name = menu["name"];
            var _triggertype = menu["triggertype"];
            var _url = menu["url"];
            //
            var _loadtype = _GetExecType(_triggertype);
            switch (_loadtype) {
                case 0:
                    var _content = menu["url"]
                        ? menu["url"]
                        : menu["content"]
                        ? menu["content"]
                        : menu[_leftmenucfgs["name"]];
                    _tis.addItemPage(
                        menu["code"],
                        menu["name"],
                        _content,
                        true,
                        true,
                        menu["ico"]
                    );
                    _tis.activeitempagemode();
                    break;
                case 1:
                    var aimUrl = _geturl(_content, _url);
                    aimUrl = aimUrl ? aimUrl : "about:blank";
                    window.open(aimUrl);
                    break;
                case 2:
                    var aimUrl = _geturl(_content, _url);
                    aimUrl = aimUrl ? aimUrl : "about:blank";
                    var _dialogdom = $(pagedialogsel).get(0);
                    var _pagedialog = _getdata(_dialogdom);
                    _pagedialog.content("<ifram src='" + aimUrl + "'></ifram>");
                    _pagedialog.show();
                    break;
                    break;
                default:
                    break;
            }
        },
        userCenterMenuClick: function (fn) {
            var _tis = this;
            if (fn) {
                var _customballoon1menudom = $(
                    "#hbm-custom-balloon-curuser .hbm-menus"
                ).get(0);
                _tis.menu(_customballoon1menudom).menuitemclick = fn;
            }
        },
        closeUserCenterMenus: function () {
            var _tis = this;
            _tis.balloon($("#hbm-custom-balloon-curuser").get(0)).hide();
        },
    };

    var HbmMasterAttach = function () {};

    HbmMasterAttach.prototype = {
        loadtasknew: function (page, limit, token, succfn) {
            var _dom = $("#datagrid_taskdaiban").get(0);
            var _url = serverroot00 + "oaindexrpt/queryrwlb";
            _cmaj(
                _dom,
                "GET",
                _url,
                { token: _token, taskType: "0", page: 1, limit: 8 },
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadtaskduban: function (page, limit, token, succfn) {
            var _dom = $("#datagrid_taskduban").get(0);
            var _url = serverroot00 + "oaindexrpt/queryrwlb";
            _cmaj(
                _dom,
                "GET",
                _url,
                { token: _token, taskType: "1", page: 1, limit: 8 },
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadtaskfbgongshi: function (page, limit, token, succfn) {
            var _dom = $("#datagrid_taskfbgongshi").get(0);
            var _url = serverroot00 + "oaindexrpt/queryrwlb";
            _cmaj(
                _dom,
                "GET",
                _url,
                { token: _token, taskType: "2", page: 1, limit: 8 },
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loaddelegatetasks: function (page, limit, token, succfn) {
            var _dom = $("#datagrid_delegatetask").get(0);
            var _url = serverroot00 + "oataskdeleg/querydelegatetasks";
            //
            _cmaj(
                _dom,
                "GET",
                _url,
                {
                    dbKey: "oa173",
                    dbName: "OA",
                    token: token,
                    page: 1,
                    limit: 8,
                },
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadpicnewslist: function (page, limit, succfn) {
            var _dom = $(".body-index-right-zxdt-bd-item[code='tpxw']").get(0);
            var _url = serverroot00 + "/cm/queryex";
            var _pmdata = {};
            _pmdata["dbKey"] = "oa11";
            _pmdata["dbName"] = "XxzxWork";
            _pmdata["tbName"] = "XxzxMainMsg";
            _pmdata["condStr"] =
                "SmallFl = @pm1 AND BigFl = @pm2 AND IsDeleted IS NULL AND FlowPhase = @pm3";
            _pmdata["jsonFields"] = "";
            _pmdata["jsonParam"] = JSON.stringify([
                {
                    pmname: "pm1",
                    pmvalue: "图片新闻",
                    pmtype: "string",
                },
                { pmname: "pm2", pmvalue: "中心网", pmtype: "string" },
                {
                    pmname: "pm3",
                    pmvalue: "Finish",
                    pmtype: "string",
                },
            ]);
            _pmdata["jsonPaging"] = JSON.stringify({ page: 1, limit: "9" });
            _pmdata["jsonOrderBy"] = JSON.stringify([
                { name: "SendTime", order: "DESC" },
            ]);
            //
            _cmaj(
                _dom,
                "POST",
                _url,
                _pmdata,
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadyuanneifwlist: function (page, limit, succfn) {
            var _dom = $("#datagrid_newsyuanneifw").get(0);
            var _url = serverroot00 + "/cm/queryex";
            var _pmdata = {};
            _pmdata["dbKey"] = "oa11";
            _pmdata["dbName"] = "XxzxWork";
            _pmdata["tbName"] = "XxzxMainMsg";
            _pmdata["condStr"] =
                "SmallFl = @pm1 AND BigFl = @pm2 AND IsDeleted IS NULL";
            _pmdata["jsonFields"] = "";
            _pmdata["jsonParam"] = JSON.stringify([
                {
                    pmname: "pm1",
                    pmvalue: "院内发文",
                    pmtype: "string",
                },
                { pmname: "pm2", pmvalue: "中心网", pmtype: "string" },
            ]);
            _pmdata["jsonPaging"] = JSON.stringify({ page: 1, limit: "8" });
            _pmdata["jsonOrderBy"] = JSON.stringify([
                { name: "SendTime", order: "DESC" },
            ]);
            //
            _cmaj(
                _dom,
                "POST",
                _url,
                _pmdata,
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadyuanneintc: function (page, limit, succfn) {
            var _dom = $("#datagrid_newsyuanneintc").get(0);
            var _url = serverroot00 + "/cm/queryex";
            var _pmdata = {};
            _pmdata["dbKey"] = "oa11";
            _pmdata["dbName"] = "XxzxWork";
            _pmdata["tbName"] = "XxzxMainMsg";
            _pmdata["condStr"] =
                "SmallFl = @pm1 AND BigFl = @pm2 AND IsDeleted IS NULL";
            _pmdata["jsonFields"] = "";
            _pmdata["jsonParam"] = JSON.stringify([
                {
                    pmname: "pm1",
                    pmvalue: "院内通知",
                    pmtype: "string",
                },
                { pmname: "pm2", pmvalue: "中心网", pmtype: "string" },
            ]);
            _pmdata["jsonPaging"] = JSON.stringify({ page: 1, limit: "8" });
            _pmdata["jsonOrderBy"] = JSON.stringify([
                { name: "SendTime", order: "DESC" },
            ]);
            //
            _cmaj(
                _dom,
                "POST",
                _url,
                _pmdata,
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadbumenfw: function (page, limit, succfn) {
            var _dom = $("#datagrid_newsbumenfw").get(0);
            var _url = serverroot00 + "/cm/queryex";
            var _pmdata = {};
            _pmdata["dbKey"] = "oa11";
            _pmdata["dbName"] = "XxzxWork";
            _pmdata["tbName"] = "XxzxMainMsg";
            _pmdata["condStr"] =
                "SmallFl = @pm1 AND BigFl = @pm2 AND IsDeleted IS NULL";
            _pmdata["jsonFields"] = "";
            _pmdata["jsonParam"] = JSON.stringify([
                {
                    pmname: "pm1",
                    pmvalue: "部门发文",
                    pmtype: "string",
                },
                { pmname: "pm2", pmvalue: "中心网", pmtype: "string" },
            ]);
            _pmdata["jsonPaging"] = JSON.stringify({ page: 1, limit: "8" });
            _pmdata["jsonOrderBy"] = JSON.stringify([
                { name: "SendTime", order: "DESC" },
            ]);
            //
            _cmaj(
                _dom,
                "POST",
                _url,
                _pmdata,
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadnewssecurityntc: function (page, limit, succfn) {
            var _dom = $("#datagrid_newssecurityntc").get(0);
            var _url = serverroot00 + "/cm/queryex";
            var _pmdata = {};
            _pmdata["dbKey"] = "oa11";
            _pmdata["dbName"] = "NewBmOa";
            _pmdata["tbName"] = "CommTable_BmGw2017";
            _pmdata["condStr"] =
                "StepKey = @pm1 AND IsDel = @pm2 AND v50_3 = @pm3 AND State = @pm4";
            _pmdata["jsonFields"] = "";
            _pmdata["jsonParam"] = JSON.stringify([
                {
                    pmname: "pm1",
                    pmvalue: "xxzxsolft531282738-1896.Step",
                    pmtype: "string",
                },
                { pmname: "pm2", pmvalue: "否", pmtype: "string" },
                {
                    pmname: "pm3",
                    pmvalue: "安全",
                    pmtype: "string",
                },
                { pmname: "pm4", pmvalue: "已完成", pmtype: "string" },
            ]);
            _pmdata["jsonPaging"] = JSON.stringify({ page: 1, limit: "8" });
            _pmdata["jsonOrderBy"] = JSON.stringify([
                { name: "GwCreateTime", order: "DESC" },
            ]);
            //
            _cmaj(
                _dom,
                "POST",
                _url,
                _pmdata,
                "",
                "",
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {
                    console.log(err);
                }
            );
        },
        loadleftmenus: function (token, workno, rootcode, succfn) {
            var url =
                serverroot00 +
                "hrindex/getmenus?token=" +
                token +
                "&workNo=" +
                workno +
                "&rootCode=" +
                rootcode;
            var _dom = $("." + clsBodyLeft + " ." + clsBodyLeftMenu).get(0);
            _cmaj(
                _dom,
                "GET",
                url,
                null,
                null,
                null,
                function (dat) {
                    if (succfn) {
                        succfn(dat);
                    }
                },
                function (err) {}
            );
            // $.ajax({
            //     type: "GET",
            //     url: url,
            //     success: function (dat) {
            //         if (succfn) {
            //             succfn(dat);
            //         }
            //     },
            //     error: function (err) {
            //         console.log(err);
            //     }
            //
            // });
        },
    };

    var masterAttach = new HbmMasterAttach();
    w.hbmasteratt = masterAttach;
    w.GsTab = GsTab;
    w.GsNewsList = GsNewsList;
    w.GsTable = GsTable;
    w.GsXmzxUnit = HbmXmzxUnit;
    w.GsFileSc = HbmFileSc;
    w.GsMiniUploadFileSc = HbmFileSc02;
    w.GsMiniUploadSingleSc = HbmFileSc03;
    w.GsFileUpload = GsFileUpload;

    //自定义转化函数
    Date.prototype.Format = function (fmt) {
        let o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
            fmt = fmt.replace(
                RegExp.$1,
                (this.getFullYear() + "").substr(4 - RegExp.$1.length)
            );
        for (let k in o)
            if (new RegExp("(" + k + ")").test(fmt))
                fmt = fmt.replace(
                    RegExp.$1,
                    RegExp.$1.length == 1
                        ? o[k]
                        : ("00" + o[k]).substr(("" + o[k]).length)
                );
        return fmt;
    };
})(window);

// (function ($) {
//     var NivoSlider = function (element, options) {
//         //Defaults are below
//         var settings = $.extend({}, $.fn.nivoSlider.defaults, options);

//         //Useful variables. Play carefully.
//         var vars = {
//             currentSlide: 0,
//             currentImage: "",
//             totalSlides: 0,
//             randAnim: "",
//             running: false,
//             paused: false,
//             stop: false,
//         };

//         //Get this slider
//         var slider = $(element);
//         slider.data("nivo:vars", vars);
//         slider.css("position", "relative");
//         slider.addClass("nivoSlider");

//         //Find our slider children
//         var kids = slider.children();
//         kids.each(function () {
//             var child = $(this);
//             var link = "";
//             if (!child.is("img")) {
//                 if (child.is("a")) {
//                     child.addClass("nivo-imageLink");
//                     link = child;
//                 }
//                 child = child.find("img:first");
//             }
//             //Get img width & height
//             var childWidth = child.width();
//             if (childWidth == 0) childWidth = child.attr("width");
//             var childHeight = child.height();
//             if (childHeight == 0) childHeight = child.attr("height");
//             //Resize the slider
//             if (childWidth > slider.width()) {
//                 // slider.width(childWidth);
//             }
//             if (childHeight > slider.height()) {
//                 //slider.height(childHeight);
//             }
//             if (link != "") {
//                 link.css("display", "none");
//             }
//             child.css("display", "none");
//             vars.totalSlides++;
//         });

//         //Set startSlide
//         if (settings.startSlide > 0) {
//             if (settings.startSlide >= vars.totalSlides)
//                 settings.startSlide = vars.totalSlides - 1;
//             vars.currentSlide = settings.startSlide;
//         }

//         //Get initial image
//         if ($(kids[vars.currentSlide]).is("img")) {
//             vars.currentImage = $(kids[vars.currentSlide]);
//         } else {
//             vars.currentImage = $(kids[vars.currentSlide]).find("img:first");
//         }

//         //Show initial link
//         if ($(kids[vars.currentSlide]).is("a")) {
//             $(kids[vars.currentSlide]).css("display", "block");
//         }

//         //Set first background
//         slider.css(
//             "background",
//             "url(" + vars.currentImage.attr("src") + ") no-repeat"
//         );

//         //Add initial slices
//         for (var i = 0; i < settings.slices; i++) {
//             var sliceWidth = Math.round(slider.width() / settings.slices);
//             if (i == settings.slices - 1) {
//                 slider.append(
//                     $('<div class="nivo-slice"></div>').css({
//                         left: sliceWidth * i + "px",
//                         width: slider.width() - sliceWidth * i + "px",
//                     })
//                 );
//             } else {
//                 slider.append(
//                     $('<div class="nivo-slice"></div>').css({
//                         left: sliceWidth * i + "px",
//                         width: sliceWidth + "px",
//                     })
//                 );
//             }
//         }

//         //Create caption
//         slider
//             .append
//             // $('<div class="nivo-caption"><p></p></div>').css({ display:'none', opacity:settings.captionOpacity })
//             ();
//         //Process initial  caption
//         if (vars.currentImage.attr("title") != "") {
//             var title = vars.currentImage.attr("title");
//             if (!title) {
//                 title = "";
//             }
//             if (title.substr(0, 1) == "#") title = $(title).html();
//             $(".nivo-caption p", slider).html(title);
//             $(".nivo-caption", slider).fadeIn(settings.animSpeed);
//         }

//         //In the words of Super Mario "let's a go!"
//         var timer = 0;
//         if (!settings.manualAdvance && kids.length > 1) {
//             timer = setInterval(function () {
//                 nivoRun(slider, kids, settings, false);
//             }, settings.pauseTime);
//         }

//         //Add Direction nav
//         if (settings.directionNav) {
//             slider.append(
//                 '<div class="nivo-directionNav"><a class="nivo-prevNav">Prev</a><a class="nivo-nextNav">Next</a></div>'
//             );

//             //Hide Direction nav
//             if (settings.directionNavHide) {
//                 $(".nivo-directionNav", slider).hide();
//                 slider.hover(
//                     function () {
//                         $(".nivo-directionNav", slider).show();
//                     },
//                     function () {
//                         $(".nivo-directionNav", slider).hide();
//                     }
//                 );
//             }

//             $("a.nivo-prevNav", slider).on("click", function () {
//                 if (vars.running) return false;
//                 clearInterval(timer);
//                 timer = "";
//                 vars.currentSlide -= 2;
//                 nivoRun(slider, kids, settings, "prev");
//             });

//             $("a.nivo-nextNav", slider).on("click", function () {
//                 if (vars.running) return false;
//                 clearInterval(timer);
//                 timer = "";
//                 nivoRun(slider, kids, settings, "next");
//             });
//         }

//         //Add Control nav
//         if (settings.controlNav) {
//             var nivoControl = $('<div class="nivo-controlNav"></div>');
//             slider.append(nivoControl);
//             for (var i = 0; i < kids.length; i++) {
//                 if (settings.controlNavThumbs) {
//                     var child = kids.eq(i);
//                     if (!child.is("img")) {
//                         child = child.find("img:first");
//                     }
//                     if (settings.controlNavThumbsFromRel) {
//                         nivoControl.append(
//                             '<a class="nivo-control" rel="' +
//                                 i +
//                                 '"><img src="' +
//                                 child.attr("rel") +
//                                 '" alt="" /></a>'
//                         );
//                     } else {
//                         nivoControl.append(
//                             '<a class="nivo-control" rel="' +
//                                 i +
//                                 '"><img src="' +
//                                 child
//                                     .attr("src")
//                                     .replace(
//                                         settings.controlNavThumbsSearch,
//                                         settings.controlNavThumbsReplace
//                                     ) +
//                                 '" alt="" /></a>'
//                         );
//                     }
//                 } else {
//                     nivoControl.append(
//                         '<a class="nivo-control" rel="' +
//                             i +
//                             '">' +
//                             (i + 1) +
//                             "</a>"
//                     );
//                 }
//             }
//             //Set initial active link
//             $(
//                 ".nivo-controlNav a:eq(" + vars.currentSlide + ")",
//                 slider
//             ).addClass("active");

//             $(".nivo-controlNav a", slider).on("click", function () {
//                 if (vars.running) return false;
//                 if ($(this).hasClass("active")) return false;
//                 clearInterval(timer);
//                 timer = "";
//                 slider.css(
//                     "background",
//                     "url(" + vars.currentImage.attr("src") + ") no-repeat"
//                 );
//                 vars.currentSlide = $(this).attr("rel") - 1;
//                 nivoRun(slider, kids, settings, "control");
//             });
//         }

//         //Keyboard Navigation
//         if (settings.keyboardNav) {
//             $(window).keypress(function (event) {
//                 //Left
//                 if (event.keyCode == "37") {
//                     if (vars.running) return false;
//                     clearInterval(timer);
//                     timer = "";
//                     vars.currentSlide -= 2;
//                     nivoRun(slider, kids, settings, "prev");
//                 }
//                 //Right
//                 if (event.keyCode == "39") {
//                     if (vars.running) return false;
//                     clearInterval(timer);
//                     timer = "";
//                     nivoRun(slider, kids, settings, "next");
//                 }
//             });
//         }

//         //For pauseOnHover setting
//         if (settings.pauseOnHover) {
//             slider.hover(
//                 function () {
//                     vars.paused = true;
//                     clearInterval(timer);
//                     timer = "";
//                 },
//                 function () {
//                     vars.paused = false;
//                     //Restart the timer
//                     if (timer == "" && !settings.manualAdvance) {
//                         timer = setInterval(function () {
//                             nivoRun(slider, kids, settings, false);
//                         }, settings.pauseTime);
//                     }
//                 }
//             );
//         }

//         //Event when Animation finishes
//         slider.bind("nivo:animFinished", function () {
//             vars.running = false;
//             //Hide child links
//             $(kids).each(function () {
//                 if ($(this).is("a")) {
//                     $(this).css("display", "none");
//                 }
//             });
//             //Show current link
//             if ($(kids[vars.currentSlide]).is("a")) {
//                 $(kids[vars.currentSlide]).css("display", "block");
//             }
//             //Restart the timer
//             if (timer == "" && !vars.paused && !settings.manualAdvance) {
//                 timer = setInterval(function () {
//                     nivoRun(slider, kids, settings, false);
//                 }, settings.pauseTime);
//             }
//             //Trigger the afterChange callback
//             settings.afterChange.call(this);
//         });

//         // Private run method
//         var nivoRun = function (slider, kids, settings, nudge) {
//             //Get our vars
//             var vars = slider.data("nivo:vars");

//             //Trigger the lastSlide callback
//             if (vars && vars.currentSlide == vars.totalSlides - 1) {
//                 settings.lastSlide.call(this);
//             }

//             // Stop
//             if ((!vars || vars.stop) && !nudge) return false;

//             //Trigger the beforeChange callback
//             settings.beforeChange.call(this);

//             //Set current background before change
//             if (!nudge) {
//                 slider.css(
//                     "background",
//                     "url(" + vars.currentImage.attr("src") + ") no-repeat"
//                 );
//             } else {
//                 if (nudge == "prev") {
//                     slider.css(
//                         "background",
//                         "url(" + vars.currentImage.attr("src") + ") no-repeat"
//                     );
//                 }
//                 if (nudge == "next") {
//                     slider.css(
//                         "background",
//                         "url(" + vars.currentImage.attr("src") + ") no-repeat"
//                     );
//                 }
//             }
//             vars.currentSlide++;
//             //Trigger the slideshowEnd callback
//             if (vars.currentSlide == vars.totalSlides) {
//                 vars.currentSlide = 0;
//                 settings.slideshowEnd.call(this);
//             }
//             if (vars.currentSlide < 0) vars.currentSlide = vars.totalSlides - 1;
//             //Set vars.currentImage
//             if ($(kids[vars.currentSlide]).is("img")) {
//                 vars.currentImage = $(kids[vars.currentSlide]);
//             } else {
//                 vars.currentImage = $(kids[vars.currentSlide]).find(
//                     "img:first"
//                 );
//             }

//             //Set acitve links
//             if (settings.controlNav) {
//                 $(".nivo-controlNav a", slider).removeClass("active");
//                 $(
//                     ".nivo-controlNav a:eq(" + vars.currentSlide + ")",
//                     slider
//                 ).addClass("active");
//             }

//             //Process caption
//             if (vars.currentImage.attr("title") != "") {
//                 var title = vars.currentImage.attr("title");
//                 if (!title) {
//                     title = "";
//                 }
//                 if (title.substr(0, 1) == "#") title = $(title).html();

//                 if ($(".nivo-caption", slider).css("display") == "block") {
//                     $(".nivo-caption p", slider).fadeOut(
//                         settings.animSpeed,
//                         function () {
//                             $(this).html(title);
//                             $(this).fadeIn(settings.animSpeed);
//                         }
//                     );
//                 } else {
//                     $(".nivo-caption p", slider).html(title);
//                 }
//                 $(".nivo-caption", slider).fadeIn(settings.animSpeed);
//             } else {
//                 $(".nivo-caption", slider).fadeOut(settings.animSpeed);
//             }

//             //Set new slice backgrounds
//             var i = 0;
//             $(".nivo-slice", slider).each(function () {
//                 var sliceWidth = Math.round(slider.width() / settings.slices);
//                 $(this).css({
//                     height: "0px",
//                     opacity: "0",
//                     background:
//                         "url(" +
//                         vars.currentImage.attr("src") +
//                         ") no-repeat -" +
//                         (sliceWidth + i * sliceWidth - sliceWidth) +
//                         "px 0%",
//                 });
//                 i++;
//             });

//             if (settings.effect == "random") {
//                 var anims = new Array(
//                     "sliceDownRight",
//                     "sliceDownLeft",
//                     "sliceUpRight",
//                     "sliceUpLeft",
//                     "sliceUpDown",
//                     "sliceUpDownLeft",
//                     "fold",
//                     "fade"
//                 );
//                 vars.randAnim =
//                     anims[Math.floor(Math.random() * (anims.length + 1))];
//                 if (vars.randAnim == undefined) vars.randAnim = "fade";
//             }

//             //Run random effect from specified set (eg: effect:'fold,fade')
//             if (settings.effect.indexOf(",") != -1) {
//                 var anims = settings.effect.split(",");
//                 vars.randAnim = $.trim(
//                     anims[Math.floor(Math.random() * anims.length)]
//                 );
//             }

//             //Run effects
//             vars.running = true;
//             if (
//                 settings.effect == "sliceDown" ||
//                 settings.effect == "sliceDownRight" ||
//                 vars.randAnim == "sliceDownRight" ||
//                 settings.effect == "sliceDownLeft" ||
//                 vars.randAnim == "sliceDownLeft"
//             ) {
//                 var timeBuff = 0;
//                 var i = 0;
//                 var slices = $(".nivo-slice", slider);
//                 if (
//                     settings.effect == "sliceDownLeft" ||
//                     vars.randAnim == "sliceDownLeft"
//                 )
//                     slices = $(".nivo-slice", slider)._reverse();
//                 slices.each(function () {
//                     var slice = $(this);
//                     slice.css("top", "0px");
//                     if (i == settings.slices - 1) {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed,
//                                 "",
//                                 function () {
//                                     slider.trigger("nivo:animFinished");
//                                 }
//                             );
//                         }, 100 + timeBuff);
//                     } else {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed
//                             );
//                         }, 100 + timeBuff);
//                     }
//                     timeBuff += 50;
//                     i++;
//                 });
//             } else if (
//                 settings.effect == "sliceUp" ||
//                 settings.effect == "sliceUpRight" ||
//                 vars.randAnim == "sliceUpRight" ||
//                 settings.effect == "sliceUpLeft" ||
//                 vars.randAnim == "sliceUpLeft"
//             ) {
//                 var timeBuff = 0;
//                 var i = 0;
//                 var slices = $(".nivo-slice", slider);
//                 if (
//                     settings.effect == "sliceUpLeft" ||
//                     vars.randAnim == "sliceUpLeft"
//                 )
//                     slices = $(".nivo-slice", slider)._reverse();
//                 slices.each(function () {
//                     var slice = $(this);
//                     slice.css("bottom", "0px");
//                     if (i == settings.slices - 1) {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed,
//                                 "",
//                                 function () {
//                                     slider.trigger("nivo:animFinished");
//                                 }
//                             );
//                         }, 100 + timeBuff);
//                     } else {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed
//                             );
//                         }, 100 + timeBuff);
//                     }
//                     timeBuff += 50;
//                     i++;
//                 });
//             } else if (
//                 settings.effect == "sliceUpDown" ||
//                 settings.effect == "sliceUpDownRight" ||
//                 vars.randAnim == "sliceUpDown" ||
//                 settings.effect == "sliceUpDownLeft" ||
//                 vars.randAnim == "sliceUpDownLeft"
//             ) {
//                 var timeBuff = 0;
//                 var i = 0;
//                 var v = 0;
//                 var slices = $(".nivo-slice", slider);
//                 if (
//                     settings.effect == "sliceUpDownLeft" ||
//                     vars.randAnim == "sliceUpDownLeft"
//                 )
//                     slices = $(".nivo-slice", slider)._reverse();
//                 slices.each(function () {
//                     var slice = $(this);
//                     if (i == 0) {
//                         slice.css("top", "0px");
//                         i++;
//                     } else {
//                         slice.css("bottom", "0px");
//                         i = 0;
//                     }

//                     if (v == settings.slices - 1) {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed,
//                                 "",
//                                 function () {
//                                     slider.trigger("nivo:animFinished");
//                                 }
//                             );
//                         }, 100 + timeBuff);
//                     } else {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { height: "100%", opacity: "1.0" },
//                                 settings.animSpeed
//                             );
//                         }, 100 + timeBuff);
//                     }
//                     timeBuff += 50;
//                     v++;
//                 });
//             } else if (settings.effect == "fold" || vars.randAnim == "fold") {
//                 var timeBuff = 0;
//                 var i = 0;
//                 $(".nivo-slice", slider).each(function () {
//                     var slice = $(this);
//                     var origWidth = slice.width();
//                     slice.css({ top: "0px", height: "100%", width: "0px" });
//                     if (i == settings.slices - 1) {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { width: origWidth, opacity: "1.0" },
//                                 settings.animSpeed,
//                                 "",
//                                 function () {
//                                     slider.trigger("nivo:animFinished");
//                                 }
//                             );
//                         }, 100 + timeBuff);
//                     } else {
//                         setTimeout(function () {
//                             slice.animate(
//                                 { width: origWidth, opacity: "1.0" },
//                                 settings.animSpeed
//                             );
//                         }, 100 + timeBuff);
//                     }
//                     timeBuff += 50;
//                     i++;
//                 });
//             } else if (settings.effect == "fade" || vars.randAnim == "fade") {
//                 var i = 0;
//                 $(".nivo-slice", slider).each(function () {
//                     $(this).css("height", "100%");
//                     if (i == settings.slices - 1) {
//                         $(this).animate(
//                             { opacity: "1.0" },
//                             settings.animSpeed * 2,
//                             "",
//                             function () {
//                                 slider.trigger("nivo:animFinished");
//                             }
//                         );
//                     } else {
//                         $(this).animate(
//                             { opacity: "1.0" },
//                             settings.animSpeed * 2
//                         );
//                     }
//                     i++;
//                 });
//             }
//         };

//         // For debugging
//         var trace = function (msg) {
//             if (this.console && typeof console.log != "undefined")
//                 console.log(msg);
//         };

//         // Start / Stop
//         this.stop = function () {
//             if (!$(element).data("nivo:vars").stop) {
//                 $(element).data("nivo:vars").stop = true;
//                 trace("Stop Slider");
//             }
//         };

//         this.start = function () {
//             if ($(element).data("nivo:vars").stop) {
//                 $(element).data("nivo:vars").stop = false;
//                 trace("Start Slider");
//             }
//         };

//         //Trigger the afterLoad callback
//         settings.afterLoad.call(this);
//     };

//     $.fn.nivoSlider = function (options) {
//         return this.each(function () {
//             var element = $(this);
//             // Return early if this element already has a plugin instance
//             if (element.data("nivoslider")) return;
//             // Pass options to plugin constructor
//             var nivoslider = new NivoSlider(this, options);
//             // Store plugin object in this element's data
//             element.data("nivoslider", nivoslider);
//         });
//     };

//     //Default settings
//     $.fn.nivoSlider.defaults = {
//         effect: "random",
//         slices: 15,
//         animSpeed: 500,
//         pauseTime: 3000,
//         startSlide: 0,
//         directionNav: true,
//         directionNavHide: true,
//         controlNav: true,
//         controlNavThumbs: false,
//         controlNavThumbsFromRel: false,
//         controlNavThumbsSearch: ".jpg",
//         controlNavThumbsReplace: "_thumb.jpg",
//         keyboardNav: true,
//         pauseOnHover: true,
//         manualAdvance: false,
//         captionOpacity: 1,
//         beforeChange: function () {},
//         afterChange: function () {},
//         slideshowEnd: function () {},
//         lastSlide: function () {},
//         afterLoad: function () {},
//     };

//     $.fn._reverse = [].reverse;
// })(jQuery);
