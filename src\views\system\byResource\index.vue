<template>

      <el-row>
        <el-col :span="6">
          <ContentWrap class="h-1/1">
            <Tree v-if="treeDataLoaded" :expandedKeys="expandedKeys" :data="treeData" @node-clicked="nodeClicked" @node-contextmenu="nodeContextmenu" />
          </ContentWrap>
        </el-col>
        <el-col :span="18">
          <ContentWrap >
            <div style="display: flex">
              <div>
                <el-button type="primary" plain @click="openAuthToRole">
                  <Icon icon="ep:plus" class="mr-5px" /> 授权到角色
                </el-button>
                <el-button type="primary" plain @click="openAuthToOrg">
                  <Icon icon="ep:plus" class="mr-5px" /> 授权到组织
                </el-button>
              </div>
              <div style="position: absolute; right: 10px">
                <el-checkbox v-model="isAuthLower" label="对下级节点进行授权" size="large" />
              </div>
            </div>
          </ContentWrap>
          <ContentWrap>
            <el-table style="margin-top: 20px" v-loading="loading" :data="list">
              <el-table-column
                    align="center"
                    label="类型"
                    prop="groupType"
                    :formatter="formatterTag"/>

              <el-table-column align="center" label="名称" prop="name" show-overflow-tooltip />
              <el-table-column align="center" label="描述" prop="description" width="300" />
              <el-table-column align="center" label="操作">
                <template #default="scope">
                  <el-button
                    link
                    type="danger"
                    @click="handleDelete(scope.row.id)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <Pagination
              v-model:limit="queryParams.pageSize"
              v-model:page="queryParams.pageNo"
              :total="total"
              @pagination="getList"
            />
          </ContentWrap>
        </el-col>
      </el-row>
  <AuthToRole ref="authToRoleRef" @success="getTreeData"/>
  <AuthToOrg ref="authToOrgRef" @success="getTreeData"/>
</template>

<script lang="ts" setup>
import {Tree} from '@/components/tree';
import * as ByResourceApi from '@/api/system/byResource'
import AuthToRole from './authToRole.vue'
import AuthToOrg from './authToOrg.vue'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
//授权到角色是否显示
const authToRoleRef = ref(null)
//授权到组织是否显示
const authToOrgRef = ref(null)
// 列表的总页数
const total = ref(0)
// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const expandedKeys = ref([])
const nodeId = ref('')

const isAuthLower = ref(false)

// 列表的加载中
const loading = ref(false)
// 列表的数据
const list = ref()
//查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const openAuthToOrg = async () => {
  if (!nodeId.value) {
    await message.warning("请先选择左侧入口")
    return;
  }
  authToOrgRef.value.open(nodeId, isAuthLower)
}

const openAuthToRole = async () => {
  if (!nodeId.value) {
    // 删除的二次确认
    await message.warning("请先选择左侧入口")
    return;
  }
  authToRoleRef.value.open(nodeId, isAuthLower)
}
/** 获取列表数据 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      id: nodeId.value,
      ...queryParams
    }
    const data = await ByResourceApi.getAuthRole(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 获取树数据 */
const getTreeData = async () => {
  try {
    const response = await ByResourceApi.getResTree2({ id: 'MenuRoot', name: null });
    treeData.value = eval("(" + response + ")");
    expandedKeys.value = treeData.value.map(item => item.id);
    treeDataLoaded.value = true;
  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
};
//格式化数据
const formatterTag = (row, column) => {
  if (row.groupType == 'Role'){
    return "角色"
  }
  if (row.groupType == 'Org'){
    return "组织"
  }


}
//树鼠标右键
const nodeContextmenu = (node) => {
  nodeId.value = node.key
  getList();
}
//树鼠标左键
const nodeClicked = (node) => {
  nodeId.value = node.key
  getList();
}
//删除关联关系
const handleDelete = async (id) => {
  const params = {
    resId: nodeId.value,
    groupId: id,
  }
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ByResourceApi.deleteDeptOrRole(params)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}

}


onMounted(async () => {
  await getTreeData();
})


</script>
