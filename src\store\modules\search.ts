// themeStyle.js
import { defineStore } from 'pinia';

export const searchStore = defineStore('search', {
  state: () => ({
    searchValue: localStorage.getItem('searchValue') || '',
    toolbarValue: '',
    newTaskSearchValue: '',
  }),
  getters: {
    getNewTaskSearchValue: (state) => state.newTaskSearchValue,
    getSearchValue: (state) => state.searchValue,
    getToolbarValue: (state) => state.toolbarValue,
  },
  actions: {
    setNewTaskSearchValue(value) {
      this.newTaskSearchValue = value;
    },
    setSearchValue(value) {
      this.searchValue = value;
      localStorage.setItem('searchValue', value);
    },
    setToolbarValue(value) {
      this.toolbarValue = value;
    },
  }
});


/**
 *  引入方式
 *  import {searchStore} from "@/store/modules/search";
 *  const mySearchStore = searchStore();
 */



