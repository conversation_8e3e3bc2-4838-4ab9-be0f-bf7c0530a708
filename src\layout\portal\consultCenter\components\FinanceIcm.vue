<template>
  <!-- 财务资金专栏 -->
  <div class="hbm-zxzx-sec hbm-zxzx-sec4 wlaqsj">
    <div class="hbm-zxzx-row">
      <div class="hbm-zxzx-cell" style="width: 100%">
        <div class="gcs_tab_wlaqzthd gcs_tab_zxzx_single" cty="tab" grp="zxzx_wlaqzthd">
          <div class="gcs_tabhd" grp="zxzx_wlaqzthd">
            <ul>
              <li class="gcs_tabhd_item gcs_cur" atr="CWZJZL">
                财务资金专栏
                <div class="gcs_cur_inner"></div>
              </li>
            </ul>
          </div>

          <div class="gcs_tabbd" grp="zxzx_wlaqzthd">
            <div class="gcs_tabitem" grp="zxzx_wlaqzthd" atr="wlaqzthd">
              <div class="gcs_zxzx_wlaqzthd_childs_cont">
                <div class="gcs_zxzx_wlaqzthd_listitem" v-for="title in titleList" :key="title.id">
                  <div class="gcs_zxzx_wlaqzthd_zstz">
                    <div class="gcs_zxzx_ss_head" style="position: relative">
                      <span class="gcs_zxzx_ss_head_ico"></span>
                      <span class="gcs_zxzx_ss_head_title" :style="itemStyle.titleStyle">{{
                        title.title
                      }}</span>
                      <a
                        target="__blank"
                        class="hbm-moreico fa fa-list-ul"
                        @click="more(title.id)"
                      ></a>
                    </div>

                    <div class="gcs_zxzx_ss_body">
                      <ul class="article_list" v-if="title.data && title.data.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in title.data" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak> </i>
                          <a
                            class="link"
                            :href="detail_url(title.id, item.ID, title.name)"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                            :style="itemStyle.spanStyle"
                          ></a>
                          <span
                            :style="itemStyle.spanStyle"
                            class="datetime formate"
                            v-text="formatDate(item.CreateTime, itemStyle.dateFormate)"
                          ></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import { formatDate } from '@/utils/formatTime'
import { detail_url } from '@/layout/portal/admin'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'FinanceIcm'
})

interface itemType {
  titleList: []
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
    dateFormate: ''
  }
  isShow: {}
}
const props = defineProps<{ itemJson: itemType }>()

const itemStyle = ref({
  modelStyle: '',
  titleStyle: '',
  spanStyle: '',
  dateFormate: 'MM-DD'
})

// 标题列表
const titleList = ref([
  { id: 'CWZJ', title: '财务资金', name: '财务资金', data: ref() },
  { id: 'SWGL', title: '税务管理', name: '税务管理', data: ref() },
  { id: 'ZCYCQ', title: '资产与产权管理', name: '资产与产权管理', data: ref() }
])

//财务资金
const get_list = async (code: string, pageSize: number): Promise<[]> => {
  let result = await PortalApi.newsCenterGetNewsList({ code: code, pageSize: pageSize })
  return result.records
}

// 处理标题和数据
const handelTitleAndDataList = () => {
  // 处理title数据
  if (titleList.value) {
    titleList.value.forEach((item) => {
      const res = get_list(item.id, 5)
      res.then((result) => {
        item.data = result
      })
    })
  }
}

// 更多信息
const more = async (titleId: string) => {
  const url = '/Portal/NewsCenter/PublicInfoList?code=' + titleId
  window.open(url, '_blank')
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    titleList.value = itemJson.titleList
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
  }
}

onMounted(() => {
  //初始化数据
  init(props.itemJson)
  handelTitleAndDataList()
})

emitter.on('finance', (obj: itemType) => {
  console.log(obj.titleList)
  init(obj)
})
//解除绑定事件
onUnmounted(() => {
  emitter.off('finance')
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/admin.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/font-awesome.css');

.fa-list-ul {
  margin-top: 5px;
}
.formate {
  display: inline-block;
  text-align: right;
  white-space: nowrap;
}
</style>
