<template>
  <!-- 咨询中心组件 -->
  <div class="hbm-zxzx-cell toper">
    <div class="gcs_tab_jtywszxw">
      <div class="gcs_tabhd">
        <span v-if="isShow.showJtyw"
          ><b><i>HOT</i></b></span
        >
        <ul>
          <li
            v-if="isShow.showJtyw"
            @click="tab1_index = 1"
            :class="1 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
            atr="jtyw"
          >
          </li>
          <li
            v-if="isShow.showSzxw"
            @click="tab1_index = 2"
            :class="2 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
            style="margin-left: 50px"
            atr="szxw"
          ></li>
        </ul>
        <a
          :href="
            1 == tab1_index
              ? '/Portal/NewsCenter/PublicInfoList?code=JTYW'
              : '/Portal/NewsCenter/PublicInfoList?code=SZXW'
          "
          target="_blank"
          class="hbm-moreico fa fa-list-ul"
        ></a>
      </div>

      <div class="gcs_tabbd">
        <div v-if="1 == tab1_index" class="gcs_tabitem">
          <ul class="article_list top_news" v-if="jtyw && jtyw.length > 0" v-cloak>
            <li class="item has_icon" v-for="(item, index) in jtyw" :key="index">
              <i class="top" v-if="item.IsTop > 0" v-cloak></i>
              <a
                :style="itemStyle.spanStyle"
                class="link"
                :href="detail_url('XMRYRM', item.ID, '集团要闻')"
                target="_blank"
                :title="item.Title"
                v-text="item.Title"
              ></a>
              <span
                :style="itemStyle.spanStyle"
                class="datetime"
                v-text="formatDate(item.CreateTime, itemStyle.dateFormate)"
              ></span>
            </li>
          </ul>
          <el-empty v-else description="数据为空" />
        </div>

        <div v-if="2 == tab1_index" class="gcs_tabitem" v-cloak>
          <ul class="article_list top_news" v-if="szxw && szxw.length > 0" v-cloak>
            <li class="item has_icon" v-for="(item, index) in szxw" :key="index">
              <i class="top" v-if="item.IsTop > 0" v-cloak></i>
              <a
                :style="itemStyle.spanStyle"
                class="link"
                :href="item.OtherWebAddres"
                target="_blank"
                :title="item.Title"
                v-text="item.Title"
              ></a>
              <span
                :style="itemStyle.spanStyle"
                class="datetime"
                v-text="formatDate(item.CreateTime, itemStyle.dateFormate)"
              ></span>
            </li>
          </ul>
          <el-empty v-else description="数据为空" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/mitt'
defineOptions({
  name: 'JtSzList'
})

interface itemType {
  itemStyle: {
    modelStyle: ''
    spanStyle: ''
    dateFormate: ''
  }
  isShow: {
    showJtyw: boolean
    showSzxw: boolean
  }
}

const props = defineProps<{ itemJson: itemType }>()

const tab1_index = ref(1) //集团要闻&时政新闻tab切换索引

const jtyw = ref()
const szxw = ref()

const itemStyle = ref({
  modelStyle: {},
  spanStyle: {},
  dateFormate: 'MM-DD'
})
const isShow = ref({
  showJtyw: false,
  showSzxw: true
})

//集团要闻
const get_jtyw = async () => {
  let result = await PortalApi.newsCenterGetNewsList({ code: 'JTYW', pageSize: 7 })
  jtyw.value = result.records
}

//时政要闻
const get_szxw = async () => {
  let result = await PortalApi.newsCenterGetNewsList({ code: 'SZXW', pageSize: 7 })
  szxw.value = result.records
}

//新闻详情url
const detail_url = (code, id, cate) => {
  return (
    '/Portal/NewsCenter/NewsDetail?Code=' + code + '&ID=' + id + '&navigation=' + encodeURI(cate)
  ) //url中文编码
}

onMounted(() => {
  init(props.itemJson)
})

const init = (itemJson: itemType) => {
  if (itemJson) {
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
    if (itemJson.isShow) {
      isShow.value = itemJson.isShow
    }
  }
  if (isShow.value.showJtyw) {
    get_jtyw()
  }
  if (isShow.value.showSzxw) {
    get_szxw()
  }
}

emitter.on('news', (obj: any) => {
  init(obj)
})
//解除绑定事件
onUnmounted(() => {
  emitter.off('news')
})
//=====================
</script>

<style lang="scss" scoped>
@import url('@/assets/css/newsCenterMaster.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/newsCenterIndex.css');
@import url('@/assets/css/font-awesome.css');

.gcs_tab_jtywszxw {
  width: 100%;
  height: 100%;
}

.datetime {
  min-width: 90px;
  text-align: right;
}
</style>
