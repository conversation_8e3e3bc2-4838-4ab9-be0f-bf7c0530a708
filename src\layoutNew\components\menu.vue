<script setup lang="ts">
import {ref} from 'vue'
import * as Portal<PERSON>pi from '@/api/system/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import {useAppStore} from '@/store/modules/app'
import {getAccessToken} from '@/utils/auth'
import {CATEGORY, useLayoutCache} from '@/hooks/web/categoryCache'
import header_search from './search.vue'
import {useUserStore} from '@/store/modules/user'
import {ElLoading} from 'element-plus'
import { useLayoutStore } from '@/store/modules/layout'
const userStore = useUserStore()
const route = useRoute()
const topath = ref<any>()
const {push} = useRouter()
const {wsCache} = useLayoutCache()
const docInfo = useAppStore().docInfo
let menu = ref<any[]>([])
onMounted(async () => {
  getMenuData()
})
const getMenuData = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.1)'
  })
  try {
    menu.value = await PortalApi.GetSysMenu1l()
    if (route.name == undefined || route.name == 'PortalWorkIndex' || useAppStore().isLayout) {
      topath.value = menu.value[0]?.pathName
    } else {
      topath.value = route.name
    }
  } finally {
    useLayoutStore().setHeaderInitialized(true)
    loading.close()
  }
}
// 有newUrl的跳转使用打开新窗口的方式进行
const openNewWindow = (m) => {
  const objData = {
    workNo: userStore.user.workNo,
    pagename: m.name,
    pageurl: m.url,
    tag: '头部菜单'
  }
  LogApi.pageLog(objData)
  let url2 = m.url
  let pathName = m.pathName
  let url = m.newUrl
  let openType = m.openType
  topath.value = pathName
  //如果url2以http开头
  if (url2 != null) {
    if (url2.startsWith('http')) {
      if (url2.indexOf('?') != -1) {
        url2 = url2 + '&token=' + getAccessToken()
      } else {
        url2 = url2 + '?token=' + getAccessToken()
      }
    }
    if (url2.indexOf('{token}') != -1) {
      url2 = url2.replace('{token}', getAccessToken())
    }
    if (openType == '_blank') {
      window.open(url2, openType)
    } else {
      docInfo.title = m.name
      docInfo.routeName = m.pathName
      if (wsCache.get(CATEGORY.IsLayout)) {
        push('/Portal/newsLayout')
        if (url2.indexOf('WorkCenter') != -1) {
          useAppStore().category = 'workCenter'
        } else if (url2.indexOf('ServiceCenter') != -1) {
          useAppStore().category = 'serviceCenter'
        } else if (url2.indexOf('Home') != -1) {
          useAppStore().category = 'consultCenter'
        }
      } else {
        push(m.url)
      }
    }
    return
  }
  if (url != null) {
    push({path: url})
  }
}
</script>

<template>
  <div class="menu">
    <div class="list">
      <template v-for="(m, index) in menu" :key="index">
        <div class="item" @click="openNewWindow(m)">
          <a href="javascript:void(0)" class="link">
            <span class="zh">{{ m.name }}</span>
            <span class="eng">{{ m.eng }}</span>
          </a>
        </div>
        <span v-if="index != menu.length - 1" class="line"></span>
      </template>
    </div>
    <header_search/>
  </div>
</template>

<style scoped lang="scss">
.menu {
  width: 1252px;
  height: 64px;
  position: absolute;
  top: 106px;
  left: 50%;
  z-index: 3;
  margin-left: -626px;
  border-top: 1px solid #acb0b8;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .list {
    display: flex;
    align-items: center;

    .item {
      position: relative;

      .link {
        display: flex;
        flex-direction: column;
        font-weight: 700;
        text-decoration: none;
        color: #fff;
        font-size: 16px;
        position: relative;
        z-index: 2;
        padding-left: 10px;
        height: 38px;
        //width: 112px;
      }
    }

    .mult .zh {
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAECAYAAABCxiV9AAAALUlEQVQImWWNyQ0AMAjDTPffOSi9FAl/7UBJElBMtK5wkOyBpcngX3oyg/MCaA3MCgV48rJiAAAAAElFTkSuQmCC);
      background-size: 7px 4px;
      background-repeat: no-repeat;
      background-position: 80px center;
    }

    .zh {
      color: #fff;
    }

    .eng {
      font-size: 12px;
      font-weight: bold;
      //color: #98a4b5;
      color: #F0F0F0;
    }

    .line {
      width: 1px;
      height: 30px;
      background: #fff;
      margin: 0 4px;
    }

    ul {
      list-style: none;
      color: #02acff;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #000f2a;
      border-top: 5px solid #02acff;
      position: absolute;
      width: 130px;
      left: 0;
      top: -37px;
      z-index: 1;
      opacity: 0.8;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      padding: 62px 0 10px 0;
      display: none;
      transition: all 0.5s;

      .sline {
        height: 1px;
        width: 100%;
        background: linear-gradient(to right, #14233b, #0e6192, #92fef7, #0e6192, #14233b);
      }

      .i {
        width: 130px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
      }
    }

    .item:hover ul {
      display: flex;
    }
  }
}
</style>
