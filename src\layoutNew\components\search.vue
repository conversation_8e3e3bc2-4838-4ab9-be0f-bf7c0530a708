<template>
  <div class="search-container" v-show="searchBar">
    <div class="search-box active" :class="{ 'active': isActive, 'has-value': searchQuery }">
      <input
        type="text"
        class="search-input"
        v-model="searchQuery"
        @focus="isActive = true"
        @blur="isActive = false"
        @keyup.enter="handleEnter"
        :placeholder="searchPlaceholder"/>
      <button class="search-button" @click="handleEnter">
        <el-icon color="#fff" size="20px">
          <Search/>
        </el-icon>
      </button>
      <transition name="fade">
        <button v-if="searchQuery" class="clear-button" @click="clearSearch">
          <el-icon color="#fff">
            <Close/>
          </el-icon>
        </button>
      </transition>
    </div>
  </div>
</template>
<script setup lang="ts">
import {watch} from "vue";
import {searchStore} from "@/store/modules/search";
import {Close, Search} from "@element-plus/icons-vue";

const gSearchStore = searchStore()
const searchBar = ref(false)
const searchPlaceholder = ref('')
const route = useRoute()
const currentPath = computed(() => route.path)
const workName = ref(gSearchStore.getToolbarValue)
const searchQuery = ref('');
const isActive = ref(true);
const currentPathString = ref(["/Portal/ProjectCenter", "/Portal/ServiceCenter/Index", "/Portal/ServiceCenter", "/Portal/publicInfoList", "/Portal/workCenter/myWork"])
const clearSearch = () => {
  searchQuery.value = '';
};
watch(
  () => gSearchStore.toolbarValue,
  (newValue) => {
    if (newValue == 'newtask') {
      searchBar.value = true
      searchPlaceholder.value = '搜索我的待办'
    } else {
      if (!currentPathString.value.includes(currentPath.value)) {
        searchBar.value = false
      } else {
        searchPlaceholder.value = '请输入菜单名称、关键词'
      }
    }
  }
)
watch(
  () => currentPath.value,
  (newValue) => {
    if (workName.value == 'newtask') {
      searchBar.value = true
      searchPlaceholder.value = '搜索我的待办'
    } else if (currentPathString.value.includes(newValue)) {
      searchBar.value = true
      searchPlaceholder.value = '请输入菜单名称、关键词'
    } else {
      searchBar.value = false
    }
  }
)
onMounted(async () => {
  workName.value = gSearchStore.getToolbarValue
  if (workName.value == 'newtask') {
    searchBar.value = true
    searchPlaceholder.value = '搜索我的待办'
  } else if (currentPathString.value.includes(currentPath.value)) {
    searchBar.value = true
    searchPlaceholder.value = '请输入菜单名称、关键词'
  } else {
    searchBar.value = false
  }
})
const handleEnter = () => {
  workName.value = gSearchStore.getToolbarValue
  if (workName.value == 'newtask') {
    gSearchStore.setNewTaskSearchValue(searchQuery.value)
  } else {
    gSearchStore.setSearchValue(searchQuery.value)
  }
}
</script>
<style scoped lang="scss">
.search-container {
  position: absolute;
  right: 0;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 46px;
  height: 46px;
  border-radius: 25px;
  background: rgb(211 204 228 / 20%);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
  overflow: hidden;
}

.search-box.active,
.search-box.has-value {
  width: 300px;
}

.search-input {
  flex: 1;
  height: 100%;
  padding: 0 20px;
  border: none;
  background: transparent;
  font-size: 16px;
  outline: none;
  color: #fff;
}

.search-button {
  position: absolute;
  right: 0;
  width: 46px;
  height: 46px;
  border: none;
  background: rgb(178 172 217 / 0);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: scale(1.5);
}

.search-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.clear-button {
  position: absolute;
  right: 46px;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  transition: all 0.3s ease;
}

.clear-button:hover {
  color: #333;
  transform: scale(1.1);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

::-webkit-input-placeholder { /* WebKit browsers */
  //color: #a4daf5;
  color: #fff;
  font-size: 14px;
}

:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
  //color: #a4daf5;
  color: #fff;
  font-size: 14px;
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
  //color: #a4daf5;
  color: #fff;
  font-size: 14px;
}

:-ms-input-placeholder { /* Internet Explorer 10+ */
  //color: #a4daf5;
  color: #fff;
  font-size: 14px;
}

@media (max-width: 768px) {
  .search-box.active,
  .search-box.has-value {
    width: 200px;
  }
}
</style>
