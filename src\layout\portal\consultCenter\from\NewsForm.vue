<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-checkbox v-model="form.isShow.showJtyw" label="集团要闻" size="large" />
          <el-checkbox v-model="form.isShow.showSzxw" label="时政新闻" size="large" />
        </el-col>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.spanStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">日期格式</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="form.itemStyle.dateFormate" placeholder="请选择" clearable>
            <el-option
              v-for="item in dateOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { dateOption } from '@/utils/formatTime'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'NewsForm'
})

const form = ref({
  itemStyle: {
    modelStyle: '',
    spanStyle: '',
    dateFormate: ''
  },
  isShow: {
    showJtyw: true,
    showSzxw: true
  }
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('news', form.value)
  },
  { immediate: true, deep: true }
)
const props = defineProps<{ itemJson: any }>()
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
})
</script>
