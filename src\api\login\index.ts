import request from '@/config/axios'
import { getRefreshToken } from '@/utils/auth'
import type { UserLoginVO } from './types'

export interface SmsCodeVO {
  mobile: string
  scene: number
}

export interface SmsLoginVO {
  mobile: string
  code: string
}

// 登录
export const login = (data: UserLoginVO) => {
  return request.post({ url: '/system/auth/login', data })
}

// 模拟登录
export const mocklogin = (data: UserLoginVO) => {
  return request.post({ url: '/system/auth/mocklogin', data })
}

// 刷新访问令牌
export const refreshToken = () => {
  return request.post({ url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken() })
}

// 使用租户名，获得租户编号
export const getTenantIdByName = (name: string) => {
  return request.get({ url: '/system/tenant/get-id-by-name?name=' + name })
}

// 使用租户域名，获得租户信息
export const getTenantByWebsite = (website: string) => {
  return request.get({ url: '/system/tenant/get-by-website?website=' + website })
}

// 登出
export const loginOut = () => {
  return request.post({ url: '/system/auth/logout' })
}

export const authApiLogout = (token) => {
  return request.get({ url: import.meta.env.VITE_TOURL_PREFIX +'/AuthApi/Logout?token='+ token })
}

// 获取用户权限信息
export const getInfo = () => {
  return request.get({ url: '/system/auth/get-permission-info' })
}

// 获取用户权限信息
export const getSimplInfo = () => {
  return request.get({ url: '/system/auth/get-simpl-info' })
}

//获取登录验证码
export const sendSmsCode = (data: SmsCodeVO) => {
  return request.post({ url: '/system/auth/send-sms-code', data })
}

// 短信验证码登录
export const smsLogin = (data: SmsLoginVO) => {
  return request.post({ url: '/system/auth/sms-login', data })
}

// 社交快捷登录，使用 code 授权码
export function socialLogin(type: string, code: string, state: string) {
  return request.post({
    url: '/system/auth/social-login',
    data: {
      type,
      code,
      state
    }
  })
}

// 社交授权的跳转
export const socialAuthRedirect = (type: number, redirectUri: string) => {
  return request.get({
    url: '/system/auth/social-auth-redirect?type=' + type + '&redirectUri=' + redirectUri
  })
}
// 获取验证图片以及 token
export const getCode = (data) => {
  return request.postOriginal({ url: 'system/captcha/get', data })
}

// 滑动或者点选验证
export const reqCheck = (data) => {
  return request.postOriginal({ url: 'system/captcha/check', data })
}

// 获取手机号以及电建通账号
export const getUserInfo = (workNo) => {
  return request.get({ url: '/system/auth/getUserInfo?workNo=' + workNo })
}

// 修改密码
export const updatePassword = (data) => {
  return request.post({ url: '/system/auth/update-password', data })
}

// 获取手机验证码
export const getPhoneCode = () => {

}

// 获取电建通验证码
export const getAppVerCode = () => {

}
// 获取页面背景图
export const getLoginImage = () => {
  return request.get({ url: '/BasicApplication/Common/getLoginImage' })
}

// 获取登录二维码
export const getQRcCode = () => {
  return request.get({ url: '/system/auth/getQRcCode' })
}

// 查询二维码状态
export const getQRcCodeState = (params) => {
  return request.get({ url: '/system/auth/getQRcCodeState', params })
}


// 获取uuid
export const get_uuid = () => {
  return request.get({ url: '/system/auth/get_uuid' })
}

//检查登录状态
export const check_login_status = (params) => {
  return request.get({ url: '/system/auth/check_login_status', params })
}



