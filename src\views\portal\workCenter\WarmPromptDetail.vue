<template>
  <div class="uk-section-default" style="margin-top:6px;">
    <div class="uk-container uk-container-large">
      <div class="uk-grid uk-grid-medium" id="app">
        <div class="uk-width-6-6 custom-right-menu-content">
          <div class="uk-card uk-card-default uk-card-small uk-card-head">
            <span class="current-location-title">当前位置：</span>
            <el-breadcrumb separator-icon="ArrowRight" style="display: inline-block;">
              <el-breadcrumb-item>首页</el-breadcrumb-item>
              <el-breadcrumb-item>办公中心</el-breadcrumb-item>
              <el-breadcrumb-item>详情</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="uk-card uk-card-default uk-card-small uk-card-body">
            <div class="text-main border" v-if="!show_loading">
              <div class="title"
                   style="text-align: center; font-size: 20px;margin-top: 30px; font-family: '宋体'; font-weight: bold; ">
                {{ warmItem?.Title }}
              </div>
              <el-divider/>
              <div class="info" style="margin: 15px; text-align: center;">
                                <span id="ImageAuthor"
                                      style="font-size: 12px; font-family: 宋体;color: #656565;line-height: 28px;">
                                    创建时间: {{ warmItem?.CreateTime }}
                                </span>
              </div>
              <div class="warm-prompt-box-img"
                   v-if="warmItem?.CONTENT === '' || warmItem?.CONTENT === undefined">
                <img :src="getImg(warmItem?.PhotoAddress)"/>
              </div>
              <div class="text-content news-center-detail-content" id="divContent"
                   style="font-size:16px;font-family:宋体;" v-html="warmItem?.CONTENT">
              </div>
              <el-divider/>
            </div>

            <div class="emptybox" v-if="show_loading">
              <div class="line-scale loading_icon">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'

const warmItem = ref<warmPrompt>()
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
const show_loading = ref(false)
import $ from 'jquery'


interface warmPrompt {
  RID: string;
  ID: string;
  Code: string;
  Type: string;
  Title: string;
  OrderIndex: string;
  PhotoAddress: string;
  CONTENT: string;
  StartTime: string;
  EndTime: string;
  CreateTime: string;
}

// 获取温馨提示详情
const getWarmPromptDetail = async () => {
  show_loading.value = true
  try {
    const id = await getUrlParamId();
    const ret = await PortalApi.getWarmPromptDetail({"id": id})
    if (ret != undefined) {
      warmItem.value = ret;
    }
  } finally {
    show_loading.value = false
  }
}
const getImg = (img: any) => {
  return baseUrl + "/BasicApplication/DownloadFile?FileID=" + img;
}

onMounted(() => {
  getWarmPromptDetail()
  $('.text-content img').css({'text-align': 'center'});
})
const getUrlParamId = async () => {
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  return params.get('id');
}


</script>

<style lang="scss" scoped>
.uk-section-default {
  width: 1252px;
  margin: auto;
}

.uk-card {
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
}

.uk-card-head {
  height: 55px;

  span {
    margin-left: 20px;
    color: #60627a;
  }
}

.uk-card-body {
  height: auto;
  min-height: 540px;
  margin-top: 10px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;

  .text-main {
    width: 90%;
  }
}


.loading_icon {
  margin-top: 100px;
  margin-bottom: 120px;

  div {
    background-color: #888;
  }
}

.warm-prompt-box-img {
  width: 100%;
  height: 320px;
  /*180px*/
  margin: 5px 0;
  margin-top: 15px;
  text-align: center;
}
</style>
