<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="任务名称" prop="TaskName">
        <el-input
          v-model="queryParams.TaskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="TaskState">
        <el-select
          v-model="queryParams.TaskState"
          placeholder="请选择任务状态"
          clearable
          class="!w-240px"
        >
          <el-option label="运行" value="0" />
          <el-option label="暂停" value="4" />
          <el-option label="删除" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务分组" prop="TaskGroup">
        <el-select
          v-model="queryParams.TaskGroup"
          placeholder="请选择任务状态"
          clearable
          class="!w-240px"
        >
          <el-option label="系统平台" value="平台系统" />
          <el-option label="数据中台" value="数据中台" />
          <el-option label="电建通" value="电建通" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button type="danger" :icon="Delete" plain size="small" @click="delTaskJob">
          删除任务
        </el-button>
        <el-button type="success" plain size="small" @click="initTask"> 初始化 </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['infra:job:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
        <!-- <el-button type="info" plain @click="handleJobLog()" v-hasPermi="['infra:job:query']">
          <Icon icon="ep:zoom-in" class="mr-5px" /> 执行日志
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="selsChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="任务名称" prop="taskName" width="400">
        <template #default="scope">
          <span style="color: #0e60fe; cursor: pointer" @click="openDetail(scope.row.id)">{{
            scope.row.taskName
          }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务编码" prop="taskCode" width="150" />

      <el-table-column label="任务分组" prop="taskGroup" />

      <el-table-column label="任务状态" prop="taskState">
        <template #default="scope">
          <el-tag
            :type="scope.row.taskState == 0 ? 'primary' : 'danger'"
            effect="light"
            style="width: 40px"
          >
            {{ scope.row.stateStr }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="初始化" prop="taskState">
        <template #default="scope">
          <el-tag
            :type="scope.row.init == 1 ? 'primary' : 'danger'"
            effect="light"
            style="width: 40px"
          >
            {{ scope.row.init == 0 ? '否' : '是' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="CRON 表达式" prop="cron" />

      <el-table-column label="最后一次运行时间" prop="lastRunEndTime" width="400" align="center">
        <template #default="scope">
          {{ formatDate(scope.row.lastRunTime) }}~{{ formatDate(scope.row.lastRunEndTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleChangeStatus(scope.row)"
            v-hasPermi="['infra:job:update']"
          >
            {{
              scope.row.taskState == InfraJobStatusEnum.STOP
                ? '运行'
                : scope.row.taskState == InfraJobStatusEnum.NORMAL
                  ? '暂停'
                  : '启动'
            }}
          </el-button>
          <!-- <el-button
            type="danger"
            link
            @click="openForm(scope.row.ID)"
            v-hasPermi="['infra:job:delete']"
          >
            修改
          </el-button> -->
          <el-dropdown
            @command="(command) => handleCommand(command, scope.row)"
            v-hasPermi="['infra:job:trigger', 'infra:job:query']"
          >
            <el-button type="primary" link> <Icon icon="ep:d-arrow-right" /> 更多 </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="handleRun" v-if="checkPermi(['infra:job:trigger'])">
                  执行一次
                </el-dropdown-item>
                <el-dropdown-item command="openDetail" v-if="checkPermi(['infra:job:query'])">
                  任务详细
                </el-dropdown-item>
                <el-dropdown-item command="handleJobLog" v-if="checkPermi(['infra:job:query'])">
                  调度日志
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <JobForm ref="formRef" @success="getList" />
  <!-- 表单弹窗：查看 -->
  <JobDetail ref="detailRef" />
  <!-- 调度日志弹窗 -->
  <JobLogDialog ref="jobLogDialogRef" />
</template>
<script lang="ts" setup>
import { checkPermi } from '@/utils/permission'
import JobForm from './JobForm.vue'
import JobDetail from './JobDetail.vue'
import JobLogDialog from './JobLogDialog.vue'
import download from '@/utils/download'
import * as JobApi from '@/api/infra/job'
import { InfraJobStatusEnum } from '@/utils/constants'
import { formatDate } from '@/utils/formatTime'
import { Delete } from '@element-plus/icons-vue'
defineOptions({ name: 'InfraJob' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  TaskName: undefined,
  TaskState: undefined,
  TaskGroup: '平台系统'
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await JobApi.getTaskList(queryParams)
    list.value = data.records
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 处理选择数据
const selsData = ref<Array<String>>([])
const selsChange = (sels) => {
  selsData.value = []
  selsData.value = sels.map((item) => item.id)
  console.log(selsData.value)
}
// 删除任务
const delTaskJob = async () => {
  if (selsData.value.length == 0) {
    useMessage().warning('请选择处理任务')
    return
  }
  const res = await JobApi.delTask(selsData.value)
  if (res) {
    useMessage().success('操作成功')
  }
}

//初始化任务
const initTask = async () => {
  if (selsData.value.length == 0) {
    useMessage().warning('请选择处理任务')
    return
  }
  const res = await JobApi.taskInit(selsData.value)
  if (res) {
    useMessage().success('操作成功')
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await JobApi.exportJob(queryParams)
    download.excel(data, '定时任务.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id: string) => {
  formRef.value.open(type, id)
}

/** 修改状态操作 */
const handleChangeStatus = async (row: any) => {
  // 修改状态的二次确认
  const text =
    row.taskState == InfraJobStatusEnum.STOP
      ? '运行'
      : row.taskState == InfraJobStatusEnum.NORMAL
        ? '暂停'
        : '重启'
  await message.confirm(
    '确认要' + text + '定时任务编号为"' + row.taskCode + '"的数据项?',
    t('common.reminder')
  )

  if (row.taskState == InfraJobStatusEnum.STOP) {
    await JobApi.runTask(row.id)
  } else if (row.taskState == InfraJobStatusEnum.NORMAL) {
    await JobApi.pauseTask(row.id)
  }else if (row.taskState == InfraJobStatusEnum.DELETE) {
    await JobApi.startTask(row.id)
  }
  message.success(text + '成功')
  // 刷新列表
  await getList()
}

/** 删除按钮操作 */
const handleDelete = async (id: string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await JobApi.deleteJob(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** '更多'操作按钮 */
const handleCommand = (command, row) => {
  switch (command) {
    case 'handleRun':
      handleRun(row)
      break
    case 'openDetail':
      openDetail(row.id)
      break
    case 'handleJobLog':
      handleJobLog(row?.id)
      break
    default:
      break
  }
}

/** 执行一次 */
const handleRun = async (row: any) => {
  try {
    // 二次确认
    await message.confirm('确认要立即执行一次' + row.taskCode + '?', t('common.reminder'))
    // 提交执行
    await JobApi.triggerTask(row.id)
    message.success('执行成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 查看操作 */
const detailRef = ref()
const openDetail = (id: string) => {
  detailRef.value.open(id)
}

/** 调度日志弹窗 */
const jobLogDialogRef = ref()

/** 打开调度日志弹窗 */
const handleJobLog = (id?: string) => {
  jobLogDialogRef.value.open(id)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
