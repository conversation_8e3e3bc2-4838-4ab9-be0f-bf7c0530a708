<template>
  <el-card>

    <div class="card-header">
      <div class="warm-prompt-box-title">{{ props.warmItem.Title }}</div>
    </div>

    <div class="card-body-img">
      <img :src="getImg(props.warmItem.PhotoAddress)" class="warm-prompt-box-img" />
    </div>
    <div class="warm-prompt-box-content"><span class="warm-prompt-description"  @click="toWarmDetail">
        请点击查看详情...
      </span>
      <hr class="card-hr" />
    </div>
    <div class="warm-prompt-look">
      <span class="context" @click="toWarmDetail">阅读全文</span>
      <el-icon class="el-icon-arrow-right"  @click="toWarmDetail">
        <ArrowRight />
      </el-icon>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { ArrowRight } from '@element-plus/icons-vue'
const { push } = useRouter();
const baseUrl = import.meta.env.VITE_TOURL_PREFIX

interface warmPrompt {
  RID: string;
  ID: string;
  Code: string;
  Type: string;
  Title: string;
  OrderIndex: string;
  PhotoAddress: string;
  Content: string;
  StartTime: string;
  EndTime: string;
  CreateTime: string;
}

const props = defineProps<{ warmItem: warmPrompt }>();

const getImg = (img: any) => {
  return baseUrl + "/BasicApplication/DownloadFile?FileID=" + img;
}
// 查看详情
const toWarmDetail = () => {
  // push({ path: '/Portal/WarmPromptDetail', query: { "id": props.warmItem.ID } })
  window.open("/Portal/WarmPromptDetail?id="+props.warmItem.ID)
}

onMounted(() => {

})
</script>

<style lang="scss" scoped>
.el-card {
  width: 500px;
  margin-top: 10px;
  margin-bottom: 20px;
}

.card-hr {
  border-color: gray;
  border-width: 0.08px;
  /* 调整粗细，例如 2px */
  opacity: 0.2;
  /* 调整透明度，例如 0.5 为 50% 透明 */
}

.warm-prompt-box-content {
  width: 100%;
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 4;
  /* 控制最多显示几行 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  margin-top: 10px;
}

.warm-prompt-description {
  color: #ccc;
  font-size: 14px;
  display: block;
  // padding-bottom: 20px;
}

.warm-prompt-box-img {
  width: 100%;
  height: 220px;
  /*180px*/
  margin: 5px 0;
  margin-top: 15px;
}

.warm-prompt-look {
  display: flex;
  align-items: center;

  .context {
    font-size: 15px;
    /* 变成小手形状 */
    cursor: pointer;
    border-bottom: 1px solid #dcdfe6;
  }
}

.el-icon-arrow-right {
  margin-left: 80%;
  margin-right: 10px;
  size: 15px;
  cursor: pointer;
  /* 变成小手形状 */
}

.warm-prompt-box-title {
  font-size: 14px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 1;
  /* 控制最多显示几行 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
}
</style>