<template>
  <div
    class="banner"
    :style="{width: props.width+'px', height: props.height + 'px'}"
    @mouseenter="showNavigation = true"
    @mouseleave="showNavigation = false">
    <swiper
      class="swiper-container"
      :slides-per-view="1"
      :space-between="0"
      @swiper="onSwiper"
      @slide-change="onSlideChange"
      :modules="modules"
      :pagination="{ clickable: true }"
      :autoplay="{ delay: 6000000, disableOnInteraction: false }"
      :loop="true">
      <swiper-slide
        v-for="(item, index) in homeImag"
        :key="item.id"
        style="cursor: pointer"
        @click="openHref('TPXW', item.id, '图片新闻', item)">
        <div class="image-container" :class="['banner-'+number]" :key=item.id>
          <img
            :key="item.id"
            class="item"
            :src="visibleSlides.has(index) ? item.url : item.lowQualityUrl"
            @load="imageLoaded(index)"
          />
          <div class="btxt">
            <div class="swiper-bottom">
              <p class="text">{{ item.title }}</p>
              <div class="swiper-more" @click="moreClick('TPXW', '图片新闻')"> 更多</div>
            </div>
          </div>
        </div>
      </swiper-slide>
      <div class="swiper-button-prev" @click="prevSlide"></div>
      <div class="swiper-button-next" @click="nextSlide"></div>
    </swiper>
  </div>
</template>
<script lang="ts" setup>
import {ref, onBeforeUnmount, nextTick, watch} from 'vue'
import {Swiper, SwiperSlide} from 'swiper/vue'
import {Pagination, Autoplay} from 'swiper/modules'
import 'swiper/css';
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'
import * as LogApi from "@/api/system/pageAndFuncLog";
import {getAccessToken} from '@/utils/auth'
// 默认图片URL - 使用现有的banner图片
const modules = [Pagination, Autoplay]
const swiperInstance = ref<typeof Swiper | null>(null)
const props = defineProps<{
  list?: Array<any> | [],
  height?: number | 300,
  width?: number | 600
}>()
const bannerList = ref(props.list)
const homeImag = ref<Array<any> | null>([])
const loading = ref(true)
const imageObserver = ref<IntersectionObserver | null>(null)
const visibleSlides = ref<Set<number>>(new Set([0])) // 默认第一张是可见的
const showNavigation = ref(false)
const loadedImages = ref<Set<number>>(new Set())
const onSwiper = (swiper: typeof Swiper) => {
  swiperInstance.value = swiper
  // 初始化时预加载第一张图片
  if (homeImag.value && homeImag.value.length > 0) {
    preloadImage(homeImag.value[0].url)
  }
}
const number = ref(Math.floor(Math.random() * 10000))
const onSlideChange = () => {
  // 获取当前活动的幻灯片索引
  const currentIndex = swiperInstance.value?.activeIndex
  if (currentIndex !== undefined && homeImag.value) {
    // 标记当前幻灯片为可见
    visibleSlides.value.add(currentIndex)

    // 预加载下一张和前一张图片
    const nextIndex = (currentIndex + 1) % (homeImag.value.length || 1)
    const prevIndex = (currentIndex - 1 + homeImag.value.length) % homeImag.value.length

    if (homeImag.value[nextIndex]) {
      preloadImage(homeImag.value[nextIndex].url)
      visibleSlides.value.add(nextIndex)
    }

    if (homeImag.value[prevIndex]) {
      preloadImage(homeImag.value[prevIndex].url)
      visibleSlides.value.add(prevIndex)
    }
  }
}
const prevSlide = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slidePrev(); // 上一张
  }
};

const nextSlide = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slideNext(); // 下一张
  }
};
// 处理图片数据的通用函数
const handleImageData = async (list: Array<any>) => {
  if (list && list.length > 0) {
    bannerList.value = list
    await getSlideImgs()
    // 在图片加载后，观察所有图片元素
    nextTick(() => {
      const imgElements = document.querySelectorAll('.banner-' + number.value + 'img')
      imgElements.forEach((img, index) => {
        if (imageObserver.value) {
          imageObserver.value.observe(img)
        }
      })
    })
  }
}

// 监听props.list的变化
watch(() => props.list, async (newList) => {
  await handleImageData(newList)
  setupImageObserver()
}, {immediate: true}) // immediate: true 表示立即执行一次

onBeforeUnmount(() => {
  // 清理观察器
  if (imageObserver.value) {
    imageObserver.value.disconnect()
  }
})
const imageLoaded = (index: number) => {
  loadedImages.value.add(index)
  if (homeImag.value && loadedImages.value.size === homeImag.value.length) {
    loading.value = false
  }

  // 当图片加载完成时，添加淡入效果
  if (homeImag.value && homeImag.value[index]) {
    homeImag.value[index].loaded = true
  }
}
// 设置图片观察器，实现更智能的懒加载
const setupImageObserver = () => {
  if ('IntersectionObserver' in window) {
    imageObserver.value = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const index = Number(img.dataset.index || '0')

          // 如果图片进入视口，预加载它
          if (homeImag.value && homeImag.value[index]) {
            const url = homeImag.value[index].url
            if (url && !loadedImages.value.has(index)) {
              img.src = url
            }
          }

          imageObserver.value?.unobserve(img)
        }
      })
    }, {
      rootMargin: '0px 0px 200px 0px', // 提前200px加载
      threshold: 0.1 // 当10%的图片可见时触发
    })
  }
}

const getSlideImgs = async () => {
  loading.value = true
  homeImag.value = []
  try {
    //系统脚本库 - 资讯中心_首页_抬头banner图片
    const res = bannerList.value
    if (!res) return
    for (let i = 0; i < res.length; i++) {
      const imageUrl = res[i].PicFile
      if (homeImag.value) {
        homeImag.value.push({
          url: imageUrl,
          title: res[i].Title,
          id: res[i].ID,
          lowQualityUrl: imageUrl,
          loaded: false
        })
        // 预加载第一张图片
        if (i === 0) {
          preloadImage(imageUrl)
        }
      }
    }
  } finally {
    // 如果没有图片或加载失败，也要关闭加载状态
    if (!homeImag.value || homeImag.value.length === 0) {
      loading.value = false
    }
  }
}
const preloadImage = (url: string) => {
  if (!url) return

  // 使用Promise来跟踪图片加载
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}
const openHref = function (code, id, cate, item) {
  if (['TPXW', 'YNGG', 'YNTZ', 'YNFW', 'ZDBD', 'ZCYCQ', 'SWGL', 'CWZJ'].indexOf(code) > -1) {
    const url =
      '/Portal/NewsCenter/NewsDetail?Code=' + code + '&ID=' + id + '&navigation=' + encodeURI(cate)
    let objdata = {
      pagename: item.Title,
      pageurl: url,
      tag: cate,
      id: item.ID
    }
    LogApi.pageLog(objdata)
    window.open(url, '_blank')
  }
}
const moreClick = (type) => {
  window.open('/Portal/NewsCenter/PublicInfoList?code=' + type, '_blank')
}
</script>

<style scoped lang="scss">
.banner {
  opacity: 1;
  height: 100%;
  width: 100%;
  padding-top: 16px;

  :deep(.swiper) {
    width: 100%;
    height: 100%;
  }

  .btxt {
    height: 34px;
    line-height: 34px;
    width: 100%;
    position: absolute;
    padding: 0;
    margin: 0;
    bottom: 0;
    left: 0;
    text-align: left;
    font-size: 14px;
    color: rgb(255, 255, 255);
    background: rgba(0, 0, 0, 0.7);

    .swiper-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        height: 100%;
        line-height: 34px;
        font-size: 14px;
        color: rgb(255, 255, 255);
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin: 0 15px 0 15px;
      }

      .swiper-more {
        height: 100%;
        line-height: 34px;
        width: 40px;
        font-size: 14px;
        color: rgb(255, 255, 255);
      }
    }

  }

  :deep(.swiper-pagination-bullets) {
    width: 100%;
    text-align: right;
    margin-bottom: 0;
    height: 34px;
    line-height: 34px;
  }

  .swiper-pagination-bullets .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: #fff;
    opacity: .5;
  }

  :deep(.swiper-pagination-bullets) {
    margin-bottom: 30px;
  }

  :deep(.swiper-pagination-bullets .swiper-pagination-bullet) {
    width: 10px;
    height: 10px;
    background: #fff;
    opacity: 0.5;
  }

  :deep(.swiper-pagination-bullets .swiper-pagination-bullet-active) {
    opacity: 1;
  }
}


.swiper-pagination-bullets .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: #fff;
  opacity: 0.5;
}

.item {
  width: 100%;
  height: 450px;
  object-fit: cover; /* 保持图片比例并填充容器 */
  transition: opacity 0.5s ease; /* 平滑过渡效果 */
  //opacity: 0.6;
  //filter: blur(2px);

  &.loaded {
    opacity: 1;
    filter: blur(0);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0; /* 占位背景色 */
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.swiper-button-prev,
.swiper-button-next {
  opacity: 0;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 16px; /* 箭头大小 */
}

.banner:hover .swiper-button-prev, .banner:hover .swiper-button-next {
  opacity: 1;
  color: white; /* 按钮颜色 */
  background: rgba(0, 0, 0, 0.5); /* 背景色 */
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-prev {
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}
</style>
