<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="24">
          <el-checkbox
            label="我的工作"
            v-model="form.isShow.showMywork"
            :value="!form.isShow.showMywork"
          />
        </el-col>
      </el-row>
      <el-row v-if="form.isShow.showMywork">
        <el-col :span="18">
          <el-form-item>
            <el-select
              v-model="value"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择工作列表"
              @change="workChange"
            >
              <el-option
                v-for="item in option"
                :key="item.type"
                :label="item.title"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="18">
          <el-checkbox
            label="点击排行"
            v-model="form.isShow.showRanKing"
            :value="!form.isShow.showRanKing"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.spanStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">模块样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.modelStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'LeftNavForm'
})

const props = defineProps<{ itemJson: any }>()

const option = ref([
  { type: 'newtask', title: '我的待办' },
  { type: 'completetask', title: '我的已办' },
  { type: 'apply', title: '我的申请' },
  { type: 'myfile', title: '我的文件' },
  { type: 'focus', title: '我的关注' }
])
const value = ref<string[]>(['newtask', 'completetask', 'apply', 'myfile', 'focus'])

const form = ref({
  titleList: option.value,
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
  isShow: {
    showMywork: true,
    showRanKing: true
  }
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('updater', form.value)
  },
  { immediate: true, deep: true }
)
const workChange = () => {
  let list = value.value
  let data = option.value.filter((item) => list.indexOf(item.type) !== -1)
  form.value.titleList = data
}
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue()
})
const setValue = () => {
  value.value = form.value.titleList.map((item) => item.type)
}
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
