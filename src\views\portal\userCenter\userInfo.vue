<template>
  <div class="user_card">
    <div class="user_card_info">
      <el-row>
        <el-col :span="24">
          <div class="grid-content ep-bg-purple-dark">
            <span>个人设置</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" style="margin-top: 5%">
          <div class="grid-content ep-bg-purple">
            <!-- 用户信息表单 -->
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="姓名">
                <el-input v-model="formInline.name" placeholder="姓名" disabled />
              </el-form-item>
              <el-form-item label="性别">
                <el-select v-model="formInline.sex" placeholder="性别" disabled>
                  <el-option label="男" value="男" />
                  <el-option label="女" value="女" />
                </el-select>
              </el-form-item>
              <el-form-item label="工号">
                <el-input v-model="formInline.workNo" placeholder="工号" disabled />
              </el-form-item>

              <el-form-item label="出生日期">
                <el-date-picker
                  style="width: 300px"
                  v-model="formInline.birthday"
                  type="date"
                  placeholder="出生日期"
                  disabled
                />
              </el-form-item>

              <el-form-item label="邮箱">
                <el-input v-model="formInline.email" placeholder="邮箱" disabled />
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input v-model="formInline.mobilePhone" placeholder="联系电话" disabled />
              </el-form-item>
              <el-form-item label="办公室电话">
                <el-input v-model="formInline.phone" placeholder="办公室电话" disabled />
              </el-form-item>
              <el-form-item label="通讯地址">
                <el-input v-model="formInline.address" placeholder="通讯地址" disabled />
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="12" style="margin-top: 5%">
          <div class="grid-content ep-bg-purple-light" style="text-align: center">
            <div class="demo-image__preview" style="text-align: center">
              <el-image
                style="width: 90px; height: 120px"
                :src="image.HeadPortrait"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :initial-index="4"
                fit="cover"
              />
            </div>

            <div class="mt-4">
              <el-row style="margin-left: 10%">
                <el-input
                  v-model="HeadPortraitName"
                  style="max-width: 50%"
                  placeholder="请选择文件"
                  disabled
                />
                <el-upload
                  ref="HeadUpload"
                  class="upload-demo"
                  :limit="1"
                  :on-exceed="handleHeadExceed"
                  :auto-upload="false"
                  style="height: 30px"
                  :show-file-list="false"
                  @change="handleUploadHead"
                  accept=".jpg,.jpeg,.png,.bmp,.gif"
                >
                  <template #trigger>
                    <el-button style="background-color: #5bc0de; color: #fff">上传文件</el-button>
                  </template>
                </el-upload>
                <el-button style="margin-left: 20px" @click.stop="clearImg(1)">清空</el-button
                ><br />
              </el-row>

              <span style="color: red; font-size: 14px"
                >说明：个人头像图片的尺寸为90*120，请选择合适比例的图片文件上传。</span
              >
            </div>

            <div class="demo-image__preview" style="text-align: center; margin-top: 80px">
              <el-image
                style="width: 80px; height: 30px"
                :src="image.SignImg"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :initial-index="4"
                fit="cover"
              />
            </div>
            <div class="mt-4">
              <el-row style="margin-left: 10%">
                <el-input
                  v-model="SignImgName"
                  style="max-width: 50%"
                  placeholder="请选择文件"
                  disabled
                />
                <el-upload
                  ref="SignUpload"
                  class="upload-demo"
                  :limit="1"
                  :on-exceed="handleSignExceed"
                  :auto-upload="false"
                  style="height: 30px"
                  :show-file-list="false"
                  @change="handleUploadSing"
                  accept=".jpg,.jpeg,.png,.bmp,.gif"
                >
                  <template #trigger>
                    <el-button style="background-color: #5bc0de; color: #fff">上传签名</el-button>
                  </template>
                </el-upload>
                <el-button style="margin-left: 20px" @click.stop="clearImg(2)">清空</el-button>
              </el-row>
              <span style="color: red; font-size: 14px"
                >说明：个人签名图片的尺寸为80*30，请选择合适比例的图片文件上传。</span
              >
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="text-align: center">
          <el-button @click="sumitUserInfo">更新信息</el-button>
        </el-col>
      </el-row>
      <!-- 隐藏按钮 -->
      <el-row v-if="showHiddenButton">
        <el-col :span="24" style="text-align: center; margin-top: 20px">
          <el-button type="primary" @click="openHiddenDialog">隐藏功能</el-button>
        </el-col>
      </el-row>
    </div>
  </div>

  <!-- 隐藏功能对话框 -->
  <el-dialog v-model="hiddenDialogVisible" title="隐藏功能" width="500px">
    <div style="text-align: center; padding: 20px">
      <div style="margin-top: 20px">
        <el-form-item label="有效token">
          <el-input v-model="submitUserToken" placeholder="请输入有效token" clearable />
        </el-form-item>
        <br />
        <br />
        <div style="text-align: center; margin-top: 15px">
          <el-button type="success" :disabled="!submitUserToken.trim()" @click="callsetUserImgsApi"
            >批量处理用户签名和头像</el-button
          ></div
        >
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hiddenDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as portal from '@/api/portal'
const SignImgName = ref()
const HeadPortraitName = ref()
import { onMounted, ref, onUnmounted } from 'vue'
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile, UploadFile } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
const HeadUpload = ref<UploadInstance>()
const SignUpload = ref<UploadInstance>()
const message = useMessage()

// 隐藏功能相关变量
const showHiddenButton = ref(false)
const hiddenDialogVisible = ref(false)
const keySequence = ref('')
const targetSequence = 'DJKMY' //输入字母时显示隐藏按钮
const sequenceTimeout = ref<NodeJS.Timeout | null>(null)

const formInline = ref({
  id: '',
  name: '',
  sex: '',
  workNo: '',
  birthday: '',
  email: '',
  mobilePhone: '',
  phone: '',
  address: '',
  avatar: '',
  signImg: ''
})
const image = ref({
  HeadPortrait: '',
  SignImg: ''
})
const HeadPortraitFile = ref<UploadFile>()
const SignImgFile = ref<UploadFile>()
const userStore = useUserStore()
const submitUserToken = ref('')
// 获取当前用户信息
const getUserInfo = async () => {
  const ret = await portal.user_info()
  formInline.value = ret
  if (formInline.value.avatar != '' || formInline.value.avatar != undefined) {
    //十六进制数据转换成UTF-8
    formInline.value.avatar = formInline.value.avatar
    const src =
      import.meta.env.VITE_TOURL_PREFIX +
      '/BasicApplication/DownloadFile?FileID=' +
      formInline.value.avatar +
      '&token=' +
      getAccessToken()
    image.value.HeadPortrait = src
  }
  if (formInline.value.signImg != '' || formInline.value.signImg != undefined) {
    //十六进制数据转换成UTF-8
    formInline.value.signImg = formInline.value.signImg
    const src =
      import.meta.env.VITE_TOURL_PREFIX +
      '/BasicApplication/DownloadFile?FileID=' +
      formInline.value.signImg +
      '&token=' +
      getAccessToken()
    image.value.SignImg = src
  }
}

// 头像上传超出限制
const handleHeadExceed: UploadProps['onExceed'] = (files) => {
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  HeadUpload.value!.handleStart(file)
}
// 签名上传超出限制
const handleSignExceed: UploadProps['onExceed'] = (files) => {
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  SignUpload.value!.handleStart(file)
}

// 上传时处理图片数据
const handleUploadHead = async (uploadFile: UploadFile) => {
  HeadPortraitName.value = uploadFile.name // 添加这行
  // const ret = await portal.uploadImg({ "file": uploadFile.raw }, "Head")
  if (uploadFile) {
    fileToBase64(uploadFile.raw).then((bas) => {
      image.value.HeadPortrait = bas
    })
    HeadPortraitFile.value = uploadFile
  } else {
    message.success('文件上传失败')
  }
}
const handleUploadSing = async (uploadFile: UploadFile) => {
  SignImgName.value = uploadFile.name // 添加这行
  if (uploadFile) {
    fileToBase64(uploadFile.raw).then((bas) => {
      image.value.SignImg = bas
    })
    SignImgFile.value = uploadFile
  } else {
    message.success('文件上传失败')
  }
}

//根据类型清空图片
const clearImg = (type: number) => {
  if (type == 1) {
    HeadPortraitName.value = ''
    image.value.HeadPortrait = ''
    HeadPortraitFile.value = undefined
    HeadUpload.value?.clearFiles() // 清除上传组件的文件列表
  } else {
    SignImgName.value = ''
    image.value.SignImg = ''
    SignImgFile.value = undefined
    SignUpload.value?.clearFiles() // 清除上传组件的文件列表
  }
  getUserInfo()
}
// 刷新数据用户信息
const sumitUserInfo = async () => {
  await message.confirm('是否确定更新？')
  try {
    if (HeadPortraitFile.value?.raw != undefined) {
      //上传头像文件
      const headUploadRes = await portal.uploadImg({
        file: HeadPortraitFile.value.raw,
        RelateId: '_Nv200_3',
        Src: window.location.href,
        id: 'WU_FILE_0',
        name: HeadPortraitFile.value.name,
        type: HeadPortraitFile.value.raw.type,
        lastModifiedDate: Date.now(),
        size: HeadPortraitFile.value.raw.size
      })
      //更新本地头像
      userStore.setUserAvatarAction(headUploadRes.Name)
      //更新数据库头像
      await portal.updateUserInfo({
        type: 'Head',
        name: headUploadRes.Name,
        file: HeadPortraitFile.value?.raw
      })
      HeadPortraitFile.value = undefined
    }
    if (SignImgFile.value?.raw != undefined) {
      const signUploadRes = await portal.uploadImg({
        file: SignImgFile.value.raw,
        RelateId: '_Nv200_3',
        Src: window.location.href,
        id: 'WU_FILE_0',
        name: SignImgFile.value.name,
        type: SignImgFile.value.raw.type,
        lastModifiedDate: Date.now(),
        size: SignImgFile.value.raw.size
      })
      await portal.updateUserInfo({
        type: 'Sign',
        name: signUploadRes.Name,
        file: SignImgFile.value?.raw
      })
      SignImgFile.value = undefined
    }
  } catch (error) {
    // 处理错误
  } finally {
    await getUserInfo()
    message.success('更新成功')
  }
}

// file转换为base64
const fileToBase64 = (file): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })
}

// 16进制转换为base64
const hexToBase64 = (hexString: any) => {
  hexString = hexString.replace(/^0x/, '')
  var digits = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  var base64_rep = ''
  var ascv
  var bit_arr = 0
  var bit_num = 0

  for (var n = 0; n < hexString.length; ++n) {
    if (hexString[n] >= 'A' && hexString[n] <= 'Z') {
      ascv = hexString.charCodeAt(n) - 55
    } else if (hexString[n] >= 'a' && hexString[n] <= 'z') {
      ascv = hexString.charCodeAt(n) - 87
    } else {
      ascv = hexString.charCodeAt(n) - 48
    }

    bit_arr = (bit_arr << 4) | ascv
    bit_num += 4
    if (bit_num >= 6) {
      bit_num -= 6

      base64_rep += digits[bit_arr >>> bit_num]
      bit_arr &= ~(-1 << bit_num)
    }
  }
  if (bit_num > 0) {
    bit_arr <<= 6 - bit_num
    base64_rep += digits[bit_arr]
  }
  var padding = base64_rep.length % 4

  if (padding > 0) {
    for (var n = 0; n < 4 - padding; ++n) {
      base64_rep += '='
    }
  }
  return 'data:image/jpeg;base64,' + base64_rep
}

// 键盘事件监听
const handleKeyDown = (event: KeyboardEvent) => {
  const key = event.key.toUpperCase()

  // 清除之前的超时
  if (sequenceTimeout.value) {
    clearTimeout(sequenceTimeout.value)
  }

  // 添加按键到序列
  keySequence.value += key

  // 检查是否匹配目标序列
  if (keySequence.value === targetSequence) {
    showHiddenButton.value = true
    keySequence.value = ''
    return
  }

  // 检查是否是目标序列的开始
  if (!targetSequence.startsWith(keySequence.value)) {
    keySequence.value = ''
  }

  // 设置超时重置序列（2秒内没有继续按键就重置）
  sequenceTimeout.value = setTimeout(() => {
    keySequence.value = ''
  }, 2000)
}

// 打开隐藏对话框
const openHiddenDialog = () => {
  hiddenDialogVisible.value = true
}

const callsetUserImgsApi = async () => {
  message.success('正在处理用户签名和头像数据，预计耗时20分钟，请稍后...')

  try {
    const res = await portal.setUserImgsSync(submitUserToken.value)
    console.log('返回结果:', res)
    message.success('接口调用成功，请查看控制台输出')
  } catch (error) {
    console.error('接口调用失败:', error)
    message.error('接口调用失败')
  }
}

onMounted(() => {
  getUserInfo()
  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeyDown)
  // 清除超时
  if (sequenceTimeout.value) {
    clearTimeout(sequenceTimeout.value)
  }
})
</script>

<style lang="css" scoped>
.user_card {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  /* 添加阴影 */
  background-color: #fff;
  width: 81%;
  height: 560px;
  margin-top: 13px;
  float: right;
  overflow-y: auto;
  border-top: 25px solid #fff;
  /* 添加上边框 */
  border-bottom: 25px solid #fff;
  /* 添加下边框 */
}

.ep-bg-purple-dark {
  background-color: #f0f5ff;
  height: auto;
  height: 50px;
  font-size: 19px;
  font-weight: 550;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #327ef6;
}

.user_card_info {
  height: 620px;
  background-color: #fff;
  margin-left: 5%;
  margin-right: 5%;
  width: 90%;
}

.user_card::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.user_card::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.user_card::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1);
}

.el-form-item {
  /* width: 80%; */
  float: right;
  margin-right: 10%;
}

.el-button {
  background: linear-gradient(to right, #3ca1f7, #50b9f4);
  background-color: #5bc0de;
  background: linear-gradient(to right, #3ca1f7, #50b9f4);
  color: #fff;
}

.demo-form-inline .el-input {
  --el-input-width: 300px;
}

.demo-form-inline .el-select {
  --el-select-width: 300px;
}
</style>
