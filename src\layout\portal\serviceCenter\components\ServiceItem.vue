<template>
  <div
    class="uk-width-5-6 custom-right-menu-content"
    style="margin-bottom: 20px"
    :style="itemStyle.modelStyle"
  >
    <template v-for="(item, index) in list" :key="index">
      <div :id="'code' + item.ID" class="custom-card">
        <div class="custom-card-content" v-if="parseInt(item.Level) === 1">
          <img class="custom-one-level-img" :src="getFullUrl(item.Icon)" />
          <span class="custom-one-level-title" :style="itemStyle.titleStyle"
            >&nbsp;{{ item.Name }}</span
          >
        </div>
        <div class="uk-child-width-1-4@l custom-grid uk-grid" uk-grid>
          <div
            v-for="(secondItem, secondIndex) in item.SubMenu"
            :key="secondIndex"
            class="custom-grid-item"
            style="width: 25%"
          >
            <div class="custom-grid-child">
              <div class="custom-grid-content">
                <div class="custom-two-level">
                  <img class="custom-two-level-img" :src="getFullUrl(secondItem.Img)" />
                  <div class="custom-grid-content">
                    <img class="custom-small-img" :src="getFullUrl(secondItem.Icon)" />
                    <span class="custom-two-level-title" :style="itemStyle.titleStyle"
                      >&nbsp;{{ secondItem.Name }}</span
                    >
                  </div>
                  <hr class="custom-divider" />
                </div>
                <ul class="uk-list right-entry-list">
                  <!-- 显示列表 -->
                  <li
                    v-for="(thirdItem, thirdIndex) in secondItem.ShowList"
                    :key="thirdIndex"
                    class="custom-show-list"
                    style="max-width: 95%;"
                  >
                    <a
                      @click="
                        logRecord(
                          $event,
                          thirdItem.Name,
                          thirdItem.Url,
                          thirdItem.ServiceDirectoryID
                        )
                      "
                      class="jump-link"
                      :style="itemStyle.spanStyle"
                      :title="thirdItem.Name"
                    >
                      <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                      <div class="service-directory-modal">
                        <span
                          uk-toggle="target: #modal-sections"
                          @click="
                            onServiceDirectory(
                              $event,
                              thirdItem.ServiceDirectoryID,
                              thirdItem.Url,
                              thirdItem.Name
                            )
                          "
                          class="jump-link-modal"
                          v-show="thirdItem.IsEnable !== '0'"
                          >服务指南</span
                        >
                      </div>
                      <el-icon :size="10">
                        <ArrowRight />
                      </el-icon>
                    </a>
                  </li>
                  <!-- 展开列表 -->
                  <li
                    v-for="(thirdItem, thirdIndex) in secondItem.SwitchList"
                    :key="thirdIndex"
                    v-show="false"
                     style="max-width: 95%;"
                    :class="['custom-switch-list-' + item.ID + '-' + secondIndex]"
                  >
                    <a
                      @click="
                        logRecord(
                          $event,
                          thirdItem.Name,
                          thirdItem.Url,
                          thirdItem.ServiceDirectoryID
                        )
                      "
                      class="jump-link"
                      :title="thirdItem.Name"
                    >
                      <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                      <div class="service-directory-modal">
                        <span
                          uk-toggle="target: #modal-sections"
                          @click="
                            onServiceDirectory(
                              $event,
                              thirdItem.ServiceDirectoryID,
                              thirdItem.Url,
                              thirdItem.Name
                            )
                          "
                          class="jump-link-modal"
                          v-show="thirdItem.IsEnable !== '0'"
                          >服务指南</span
                        >
                      </div>
                      <span uk-icon="chevron-right" style="width: 10px; top: -16px">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <polyline
                            fill="none"
                            stroke="#000"
                            stroke-width="1.03"
                            points="7 4 13 10 7 16"
                          />
                        </svg>
                      </span>
                    </a>
                  </li>
                </ul>
                <div
                  class="view-more"
                  v-show="secondItem.IsMoreThanFour"
                  @click="onExpandAndCollapse($event, index, item.ID, secondIndex)"
                >
                  <div :class="['custom-expand-collapse-switch-' + item.ID + '-' + secondIndex]">
                    <span uk-icon="triangle-down"></span><span class="view-all">展开</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="emptybox" v-if="show_loading">
      <div class="line-scale loading_icon">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import { getFullUrl } from '@/layout/portal/admin'
import * as PortalApi from '@/api/system/portal'
import { getAccessToken } from '@/utils/auth'
import emitter from '@/utils/mitt'
import { ArrowRight } from '@element-plus/icons-vue'
import $ from 'jquery'

const userStore = useUserStore()
const message = useMessage()
const list = ref()
const searchContent = ref('')
const show_loading = ref(false)

defineOptions({
  name: 'ServiceItem'
})
interface itemType {
  titleList: {
    id: ''
    title: ''
  }
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {}
}

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {}
})
const isShow = ref()

const titleItem = ref({ id: '', title: '' })

const onSearch = (keyword) => {
  if (keyword === undefined || keyword === '') {
    window.location.reload()
  } else {
    searchContent.value = keyword
  }
}

const result = ref()
//获取服务中心右侧数据列表
const loadData = async (serviceList: any[]) => {
  show_loading.value = true
  // result.value = await PortalApi.serviceCenterloadData({ Search: titleItem.value.title })

  if (serviceList != undefined) {
    result.value = serviceList.filter((item) => item.Name == titleItem.value.title)
  } else {
    result.value = await PortalApi.serviceCenterloadData({ Search: titleItem.value.title })
  }

  list.value = []
  result.value.forEach((firstLevel) => {
    //*******特殊处理*******
    if (firstLevel.SubMenu.length > 0) {
      firstLevel.SubMenu.forEach((secondLevel) => {
        secondLevel.IsExpandCollapse = false
        if (secondLevel.SubMenu.length > 0) {
          secondLevel.IsMoreThanFour = secondLevel.SubMenu.length > 4
          secondLevel.ShowList = secondLevel.SubMenu.slice(0, 4)
          secondLevel.SwitchList = secondLevel.SubMenu.slice(4, secondLevel.SubMenu.length)
        }
        secondLevel.SubMenu = []
      })
    }
  })
  list.value = result.value
  show_loading.value = false
}

const onExpandAndCollapse = (e, index, itemId, secondIndex) => {
  let el = $('.custom-switch-list-' + itemId + '-' + secondIndex)
  if (el.css('display') === 'none') {
    el.show(500)
  } else {
    el.hide(500)
  }
  // console.log(index)
  let expandCollapse = $('.custom-expand-collapse-switch-' + itemId + '-' + secondIndex)
  if (expandCollapse.text() === '展开') {
    expandCollapse.find('span').eq(0).attr('uk-icon', 'triangle-up')
    expandCollapse.find('span').eq(1).text('收缩')
  } else {
    expandCollapse.find('span').eq(0).attr('uk-icon', 'triangle-down')
    expandCollapse.find('span').eq(1).text('展开')
  }

  list.value[index].SubMenu[secondIndex].IsExpandCollapse =
    list.value[index].SubMenu[secondIndex].IsExpandCollapse
}

// 日志
const logRecord = async (e, name, url, menuId) => {
  if (url === null || url === '' || url === undefined) {
    if (url === null || url === '' || url === undefined) {
      message.error('暂未关联相关业务功能。')
      return
    }
    return
  }
  let pageName = $('.uk-container-center ul li .active').text()
  let pageUrl = window.location.href
  let requestUrl = getFullUrl('/KHIDIService/SysLog/FuncLog')

  let objdata = {
    workno: userStore.user.workNo,
    pagename: pageName,
    pageurl: pageUrl,
    funcname: name,
    funcurl: url,
    menuId: menuId
  }
  // 相对路径修改为绝对路径
  if (!url.startsWith('http')) {
    url = getFullUrl(url)
  }
  if (url.indexOf('{token}') != -1) {
    url = url.replace('{token}', getAccessToken())
  } else if (url.indexOf('?') == -1) {
    url = url + '?token=' + getAccessToken()
  } else {
    url = url + '&token=' + getAccessToken()
  }
  // 暂时注释
  let tourl = url.replace('{WorkNo}', userStore.user.workNo)
  window.open(tourl)
  const log = await PortalApi.FuncLog(objdata)
}

const onServiceDirectory = (e, serviceDirectoryID, url, businessName) => {
  e.cancelBubble = true //取消事件冒泡，防止打开业务功能事件的响应

  // serviceDirectoryName = businessName
  // serviceDirectoryUrl = url
  // scrollDistance = getScrollTop()

  // //获取服务指南内容
  // $.post(
  //   requestGetServiceDirectoryPath,
  //   { serviceDirectoryID: serviceDirectoryID },
  //   function (result) {
  //     if (result.length > 0) {
  //       serviceDirectoryContent = result[0].ServiceDirectory
  //       Institutions = result[0].Institutions !== null ? result[0].Institutions.split(',') : ''
  //       OtherFile = result[0].OtherFile !== null ? result[0].OtherFile.split(',') : ''
  //     }
  //   }
  // )
}

const props = defineProps<{ itemJson: itemType; itemId: string; serviceList: any[] }>()

emitter.on('serviceItemFrom', (obj: any) => {
  init(obj)
})
const init = (itemJson: itemType) => {
  if (itemJson) {
    titleItem.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
    isShow.value = itemJson.isShow
  }
  if (titleItem.value) {
    loadData(props.serviceList)
  }
}

onMounted(() => {
  init(props.itemJson)
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/admin.css');

.emptybox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200px;
}

.emptybox .loading_icon div {
  background: #999;
}
.view-more {
  cursor: pointer;
}
</style>
