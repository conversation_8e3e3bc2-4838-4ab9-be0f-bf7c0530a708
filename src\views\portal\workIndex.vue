<template>
  <div v-if="show_index" class="uk-flex uk-flex-column uk-width-4-5">
    <div class="uk-flex work-top" id="workTop">
      <div class="uk-card uk-card-default">
        <!-- <swiper-container class="swiper-container" navigation="true" ref="mainSwiper" pagination="false" :autoplay="swiperOptions.autoplay" @init="init">
          <swiper-slide v-for="(item, index) in tpxwList" :key="index" class="khidi_banner">
            <img class="img" :src='item.PicFile' />
            <div class="desc" v-text="item.Title"></div>
          </swiper-slide>
        </swiper-container> -->

        <div class="hbm-zxzx-cell zxzx_banner" m="t-4">
          <el-carousel trigger="click" height="338px">
            <el-carousel-item v-for="(item, index) in tpxw" :key="index">
              <a
                :href="detail_url('TPXW', item.ID, '图片新闻')"
                :title="item.Title"
                target="_blank"
                @click="PageLogRecord('TPXW', item, '图片新闻')"
              >
                <img
                  v-show="!loading_image"
                  :src="item.PicFile"
                  class="TPXWImg"
                  @load="handleImageLoaded"
                />
                <div class="emptybox" v-if="loading_image" style="margin-top: 130px">
                  <div class="line-scale loading_icon">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              </a>

              <div class="btm">
                <h3 class="carousel-title">{{ item.Title }}</h3>
                <a
                  class="more"
                  href="/Portal/NewsCenter/PublicInfoList?code=TPXW"
                  @click="
                    PageLogRecord(
                      'TPXW',
                      {
                        Title: '更多',
                        ID: item.ID
                      },
                      '图片新闻'
                    )
                  "
                  target="_blank"
                  >更多 ></a
                >
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>

      <div class="uk-card uk-card-default">
        <div class="uk-flex uk-card-header">
          <div @click="loadYNXX(0)"><span class="uk-card-title">院发文</span></div>
          <div @click="loadYNXX(1)"><span class="uk-card-title">院通知</span></div>
          <div @click="loadYNXX(2)"><span class="uk-card-title">院公告</span></div>
          <a @click="tabMore(0, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
        </div>
        <div class="uk-card-body">
          <ul class="uk-list" v-if="YNXXList && YNXXList.length > 0">
            <template v-for="(item, index) in YNXXList" :key="index">
              <li @click="OpenView(1, item)">
                <span :title="item.Title" class="bgtitle"
                  ><i v-if="item.IsTop != null" class="top"></i>{{ item.Title }}</span
                >
                <span v-if="item && item.CreateTime">{{ item.CreateTime.split(' ')[0] }}</span>
              </li>
            </template>
          </ul>

          <el-empty v-else description="数据为空" />
        </div>
      </div>
    </div>

    <div class="uk-flex work-center">
      <div
        class="uk-card uk-card-default work-card"
        style="margin-left: 0; width: 100%; max-width: 100%"
      >
        <div class="uk-flex uk-card-header">
          <div @click="loadTask(0, 'newtask')">
            <span class="uk-card-title">我的待办</span>
            <span class="uk-badge" v-show="newtasknum > 0" v-text="newtasknum"></span>
          </div>

          <div @click="loadTask(1, 'completetask')">
            <span class="uk-card-title">我的已办</span>
            <span class="uk-badge" v-show="completetasknum > 0" v-text="completetasknum"></span>
          </div>

          <div @click="loadTask(2, 'focus')">
            <span class="uk-card-title">我的关注</span>
          </div>

          <a @click="tabMore(0, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
        </div>

        <div class="uk-card-body">
          <template v-if="TaskList.Count > 0 && is4Alogin">
            <ul class="uk-list">
              <template v-if="TaskList.DataType === 'newtask'">
                <template v-for="(item, index) in TaskList.DataList" :key="index">
                  <li class="uk-flex" @click="OpenView(2, item)">
                    <div class="icon">
                      <img
                        title="取消关注"
                        v-if="item.IsFocus == 'true'"
                        @click.stop="FocusFlow(0, item, TaskList.DataType)"
                        src="@/assets/icons/portal/icon-tuding-copy.png"
                      />
                      <img
                        title="关注任务"
                        v-else-if="item.IsFocus == 'false'"
                        @click.stop="FocusFlow(1, item, TaskList.DataType)"
                        src="@/assets/icons/portal/icon-tudingwb.png"
                      />
                    </div>

                    <div
                      class="title"
                      @click="openDetails(item.URL)"
                      :title="item.ActivityName"
                      :style="{ color: item.ExtsField }"
                    >
                      {{ item.ActivityName }}
                    </div>
                    <div class="cate">
                      <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                      <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                      <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                      <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                      <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                    </div>
                    <div class="info">
                      <span class="dept" :title="item.FromDeptName" v-if="item.FromDeptName"
                        >{{ item.ShortDeptName }}：</span
                      >
                      <span class="dept" v-else></span>
                      <span class="name" v-if="item.FromUserNames">{{ item.FromUserNames }}</span>
                      <span class="name" v-else></span>
                      <span class="time">{{ item.CreateTime.substring(0, 16) }}</span>
                    </div>
                  </li>
                </template>
              </template>

              <template v-else-if="TaskList.DataType === 'completetask'">
                <template v-for="(item, index) in TaskList.DataList" :key="index">
                  <li class="uk-flex" @click="OpenView(2, item)">
                    <div class="icon">
                      <img
                        title="取消关注"
                        v-if="item.IsFocus == 'true'"
                        @click.stop="FocusFlow(0, item, TaskList.DataType)"
                        src="@/assets/icons/portal/icon-tuding-copy.png"
                      />
                      <img
                        title="关注任务"
                        v-else-if="item.IsFocus == 'false'"
                        @click.stop="FocusFlow(1, item, TaskList.DataType)"
                        src="@/assets/icons/portal/icon-tudingwb.png"
                      />
                    </div>

                    <div class="title" @click="openDetails(item.ViewUrl)" :title="item.TaskName"
                      >{{ item.TaskName }}
                    </div>
                    <div class="cate">
                      <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                      <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                      <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                      <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                      <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                    </div>
                    <div class="info">
                      <span class="dept" v-if="item.CreateDeptName" :title="item.CreateDeptName">
                        {{ item.CreateUserDeptName }}：</span
                      >
                      <span class="name">{{ item.CreateUserName }}</span>
                      <!-- <span class='time'>{{ item.ExecTime.substring(0, 16) }}</span> -->
                    </div>
                  </li>
                </template>
              </template>

              <template v-else-if="TaskList.DataType === 'focus'">
                <template v-for="(item, index) in TaskList.DataList" :key="index">
                  <li class="uk-flex" @click="OpenView(2, item)">
                    <div class="icon">
                      <img
                        @click.stop="FocusFlow(0, item, TaskList.DataType)"
                        src="@/assets/icons/portal/icon-tuding-copy.png"
                      />
                    </div>

                    <div class="title" :title="item.FlowName">{{ item.FlowName }}</div>

                    <div class="apply-flow info">
                      <div class="dept">
                        <span>审批状态：</span>
                        <span :title="item.FlowState">{{ item.FlowState }}</span>
                      </div>
                      <div class="name">
                        <span>关注日期：</span>
                        <span v-if="item && item.CreateTime">{{
                          item.CreateTime.split(' ')[0]
                        }}</span>
                      </div>
                      <div class="time">
                        <span>当前审批步骤：</span>
                        <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
                      </div>
                    </div>
                  </li>
                </template>
              </template>
            </ul>
          </template>

          <div class="emptybox" v-if="show_loading1 || !is4Alogin">
            <div>
              <span class="line-scale loading_icon">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </span>
            </div>
          </div>

          <template v-if="TaskList.Count <= 0 && !show_loading1">
            <div class="no-data" style="padding-top: 100px">
              <img src="@/assets/imgs/nodata.png" />
              <div class="text" style="color: #999; margin-top: 10px">暂无数据</div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="work-menu">
      <div class="work-center uk-card uk-card-default work-card func_fav">
        <div class="uk-flex uk-card-header function-collect">
          <div>
            <img src="@/assets/icons/portal/icon-cygn.png" class="function-collect-img" /><span
              class="uk-card-title"
              >功能收藏</span
            >
          </div>
          <a id="gnscEdit" @click="CYGNModify()"
            ><img src="@/assets/icons/portal/icon-edit.png" class="function-collect-more"
          /></a>
        </div>

        <div class="uk-card-body cygn-body" style="min-height: 134px">
          <div class="list" v-if="CYGNList && CYGNList.length > 0">
            <a
              href="javascript:;"
              class="item"
              v-for="(item, index) in CYGNList"
              :key="index"
              @click="OpenRes(item.Url, item, '功能收藏')"
            >
              <img class="icon" :src="gnsc_img(item.IconUrl)" />
              <div class="text" :id="item.ID" v-text="item.Alias" style="font-size: 14px"></div>
            </a>
          </div>

          <el-empty v-else description="数据为空" />
        </div>
      </div>
    </div>

    <div class="uk-flex work-bottom" id="workBottom">
      <div class="uk-card uk-card-default bottom-card">
        <div class="uk-flex uk-card-header">
          <div>
            <img src="@/assets/icons/portal/icon-tx.png" />
            <span class="uk-card-title">我的消息</span>
            <span class="uk-badge" v-text="MsgNewCount"></span>
          </div>
          <a @click="tabMore(1, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
        </div>

        <div class="uk-card-body">
          <template v-if="MsgCount > 0">
            <ul class="uk-list">
              <template v-for="(item, index) in MsgList" :key="index">
                <li class="uk-flex" @click="OpenMsg(item)" style="height: 60px; line-height: 60px">
                  <div>
                    <img src="@/assets/icons/portal/icon-msg.png" />
                  </div>
                  <div :title="item.Title" v-text="item.Title"></div>
                  <div>
                    <hr class="uk-divider-vertical" />
                  </div>
                  <div>
                    <ul class="uk-list uk-list-disc">
                      <li
                        ><span>发送人<i></i></span><span>{{ item.SenderName }}</span></li
                      >
                      <li
                        ><span>时间<i></i></span
                        ><span v-if="item && item.SendTime">{{
                          item.SendTime.split(' ')[0]
                        }}</span></li
                      >
                    </ul>
                  </div>
                </li>
              </template>
            </ul>
          </template>

          <div class="emptybox" style="margin-top: 50px" v-if="show_loading">
            <div class="line-scale loading_icon">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>

          <template v-if="MsgCount <= 0 && !show_loading">
            <div class="no-data">
              <img src="@/assets/imgs/nodata.png" />
            </div>
          </template>
        </div>
      </div>

      <div class="uk-card uk-card-default bottom-card">
        <div class="uk-flex uk-card-header">
          <div>
            <img src="@/assets/icons/portal/icon-wjj.png" /><span class="uk-card-title"
              >我的文件</span
            >
          </div>
          <a @click="tabMore(2, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
        </div>

        <div class="uk-card-body">
          <template v-if="FileCount > 0">
            <ul class="uk-list">
              <template v-for="(item, index) in FileList" :key="index">
                <li class="uk-flex">
                  <div>
                    <img :src="proxy.$getFullUrl(item.Icon)" />
                  </div>
                  <div :title="item.FileName.substring(item.FileName.indexOf('_') + 1)"
                    >{{ item.FileName.substring(item.FileName.indexOf('_') + 1) }}
                  </div>
                  <div>
                    <hr class="uk-divider-vertical" />
                  </div>
                  <div>
                    <img
                      @click.stop="DownLoadAndPreviewFile(item, 0)"
                      title="下载"
                      src="@/assets/icons/portal/icon-download.png"
                    />
                    <img
                      @click.stop="CancelSc(item)"
                      title="取消收藏"
                      src="@/assets/imgs/sc_a.png"
                    />
                  </div>
                </li>
              </template>
            </ul>
          </template>

          <div class="emptybox" style="margin-top: 50px" v-if="show_loading">
            <div class="line-scale loading_icon">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>

          <template v-if="!show_loading && FileCount <= 0">
            <div class="no-data">
              <img src="@/assets/imgs/nodata.png" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import * as portal from '@/api/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import { replaceUrl } from '@/assets/js/NK.js'
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
import { getCurrentInstance } from 'vue'
import $ from 'jquery'
import { useAppStore } from '@/store/modules/app'

import { register } from 'swiper/element/bundle'

const message = useMessage()

register()

//功能收藏图片
import gnsc_img1 from '@/assets/icons/portal/01.png'
import gnsc_img2 from '@/assets/icons/portal/02.png'
import gnsc_img3 from '@/assets/icons/portal/03.png'
import gnsc_img4 from '@/assets/icons/portal/04.png'
import gnsc_img5 from '@/assets/icons/portal/05.png'
import gnsc_img6 from '@/assets/icons/portal/06.png'
import gnsc_img7 from '@/assets/icons/portal/07.png'
import gnsc_img8 from '@/assets/icons/portal/08.png'
import gnsc_img9 from '@/assets/icons/portal/09.png'
import gnsc_img10 from '@/assets/icons/portal/10.png'
import Message from '@/layout/components/Message/src/Message.vue'
import { constant } from 'lodash-es'
import { useRoute, useRouter } from 'vue-router'
import { getIsOpen, handlerSend, messageInfo, toggleConnectStatus } from '@/utils/websocket'
import emitter from '@/utils/mitt'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useTaskStore } from '@/store/modules/task'
const taskStore = useTaskStore()
const route = useRoute()
const { wsCache } = useCache()
//下载路径
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL

//根据QueryString参数名称获取值
function getQueryString(name) {
  var result = location.search.match(new RegExp('[\?\&]' + name + '=([^\&]+)', 'i'))
  if (result == null || result.length < 1) {
    return ''
  }
  return result[1]
}

const _UserInfo = {}

export default {
  data() {
    return {
      CYRKPath: '/Portal/WorkCenter/LeftTopMenu',
      CYRKList: [],
      tpxwPath: '/Portal/Home/getTpxw',
      tpxwList: [],
      YNXXPath: ['/Portal/Home/getYnfw', '/Portal/Home/getGsxw', '/Portal/Home/getTzgg'],
      YNXXList: [],
      TaskPath: '/Portal/WorkCenter/GetMyWork',
      TaskList: {},
      newtasknum: 0,
      completetasknum: 0,
      focusnum: 0,
      AuthBy4A: '/Portal/Home/AuthenticateBy4A',
      CYGNPath: '/Portal/WorkCenter/WorkSysMenu',
      CYGNList: [],
      collectList: [],
      tpxw: [], //图片新闻

      //服务指南
      serviceDicPath: '/Portal/WorkCenter/ResByID',
      serviceDirectoryContent: '',
      serviceDirectoryName: '',
      serviceDirectoryUrl: '',

      //关联制度
      Institutions: [],

      //相关附件
      OtherFile: [],
      MsgAndFilePath: '/Portal/WorkCenter/GetMyWork',
      MsgList: [],
      MsgNewCount: 0,
      MsgCount: 0,
      FileList: [],
      FileCount: 0,
      FocusFlowPath: '/system/Portal/WorkCenter/FocusFlow',
      MyFocusByIDPath: '/system/Portal/WorkCenter/GetMyFocusByID',
      scrollDistance: 0,
      wins: [], //已打开窗口数组
      timer: '', //窗口检查定时器
      show_loading: true,
      show_loading1: true,
      userStore: {},
      proxy: {},
      is4Alogin: false,
      is_open: true,
      wdgz: [], //我的工作
      swiperOptions: {
        autoplay: {
          delay: 5000,
          enabled: true
        }
      },

      show_index: true,

      key: 0, //用于校验请求是否是当前请求

      baseUrl: import.meta.env.VITE_TOURL_PREFIX,

      loading_image: false,

      messageInfo: messageInfo
    }
  },
  // 监听全局参数处理逻辑
  watch: {
    messageInfo: {
      handler: function (newValue, oldValue) {
        console.log('messageInfo.value:' + messageInfo.value)
        this.handleSocket(messageInfo.value)
      },
      deep: true
    }
  },
  mounted: function () {
    if (window.screen.width == 1366) {
      $('.page-work .uk-width-4-5').css('left', '20%')
    }
    $('.el-carousel__indicators--outside').hide()
    this.init()

    emitter.on('set4AcookieOK', () => {
      this.is4Alogin = true
    })
  },
  updated: function () {},
  created: function () {
    this.is4Alogin = wsCache.get(CACHE_KEY.login4A)
    this.loading_image = true //图片加载
    // 开启websocket监听
    if (!getIsOpen.value) {
      toggleConnectStatus()
    }
    setTimeout(() => {
      // 图片动态最多1.5秒，1.5秒后关闭loading
      if (this.loading_image) {
        this.loading_image = false
      }
    }, 1500)
  },
  methods: {
    init() {
      this.show_index = true //默认切换到办公中心首页
      this.loadCYRK() //左侧菜单
      this.loadTpxw() //图片新闻
      this.loadYNXX(0) //院发文、院内通知、院内公告
      this.loadTask(0, 'newtask') //待办、已办、我的关注
      this.loadCYGN() //功能收藏
      this.loadMsgAndFile('msg') //我的消息
      this.loadMsgAndFile('myfile') //我的文件

      this.userStore = useUserStore()

      //若是数据查询用户，则不显示图片新闻、院通知、我的消息、我的文件
      if (this.userStore.user.workNo == 'wp08108') {
        document.getElementById('workTop').style.display = 'none'
        document.getElementById('workBottom').style.display = 'none'
        document.getElementById('gnscEdit').style.display = 'none'
      }
      const { proxy } = getCurrentInstance()
      this.proxy = proxy

      //加载图片新闻
      this.get_tpxw()
    },
    //根据QueryString参数名称获取值
    getQueryString(name) {
      var result = location.search.match(new RegExp('[\?\&]' + name + '=([^\&]+)', 'i'))
      if (result == null || result.length < 1) {
        return ''
      }
      return result[1]
    },
    openDetails(url) {
      // window.open(url, '_blank')
    },
    //banner图片新闻
    get_tpxw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'TPXW', pageSize: 8 })
      this.tpxw = result.records

      let tpxw_timer = setInterval(function () {
        if (this.tpxw && this.tpxw.length > 0) {
          clearInterval(tpxw_timer)

          //图片新闻swiper
          let news_banner_swiper = new Swiper('.news_banner_swiper', {
            loop: true,
            speed: 800,
            autoplay: {
              delay: 4000 //5秒切换一次
            },
            pagination: {
              el: '.swiper-pagination',
              clickable: true
            },
            navigation: {
              nextEl: '.swiper-button-next2',
              prevEl: '.swiper-button-prev2'
            }
          })
        }
      }, 100)
    },

    //新闻详情url
    detail_url: function (code, id, cate) {
      return (
        '/Portal/NewsCenter/NewsDetail?Code=' +
        code +
        '&ID=' +
        id +
        '&navigation=' +
        encodeURI(cate)
      ) //url中文编码
    },

    //切换到我的待办
    go_wdgz(tag) {
      switch (tag) {
        case 'wddb':
          break

        default:
          break
      }
      this.show_index = false
    },

    //功能收藏图片处理
    gnsc_img(v) {
      // console.log(v);
      return v
    },

    //左侧菜单
    menu_click() {
      this.is_open = !this.is_open
    },

    //左侧导航
    loadCYRK: function () {
      portal.djph_list({}).then((ret) => {
        this.CYRKList = ret
      })
    },

    //图片新闻
    loadTpxw: function () {
      PortalApi.newsCenterGetNewsList({ code: 'TPXW', pageSize: 8 }).then((result) => {
        this.tpxwList = result.records
      })

      let tpxw_timer = setInterval(function () {
        if (this.tpxwList && this.tpxwList.length > 0) {
          clearInterval(tpxw_timer)

          //图片新闻swiper
          let news_banner_swiper = new Swiper('.news_banner_swiper', {
            loop: true,
            speed: 800,
            autoplay: {
              delay: 4000 //5秒切换一次
            },

            pagination: {
              el: '.swiper-pagination',
              clickable: true
            },

            navigation: {
              nextEl: '.swiper-button-next2',
              prevEl: '.swiper-button-prev2'
            }
          })
        }
      }, 100)
    },

    //院发文、院内通知、院内公告
    loadYNXX: function (index) {
      this.YNXXList = []

      //设置tab样式
      $('.work-top .uk-card-header').children().removeClass('active')
      $('.work-top .uk-card-header div').children().removeClass('active')

      $('.work-top .uk-card-header div:eq(' + index + ')').addClass('active')
      $('.work-top .uk-card-header div:eq(' + index + ') span').addClass('active')

      switch (index) {
        case 0: //院发文
          portal
            .yfw_list({
              page: 1,
              pageSize: 10
            })
            .then((ret) => {
              this.YNXXList = ret.records
            })
          break

        case 1: //院通知
          portal
            .ytz_list({
              page: 1,
              pageSize: 10
            })
            .then((ret) => {
              this.YNXXList = ret.records
            })
          break

        case 2: //院公告
          portal
            .ygg_list({
              page: 1,
              pageSize: 10
            })
            .then((ret) => {
              this.YNXXList = ret.records
            })
          break

        default:
          break
      }
    },

    //待办、已办、我的关注
    loadTask: async function (index, type) {
      // this.TaskList.DataList = [];
      this.TaskList = {}
      this.show_loading1 = true
      this.key = this.key + 1
      //设置tab样式
      $('.work-center .uk-card-header').children().removeClass('active')
      $('.work-center .uk-card-header div').children().removeClass('active')
      $('.work-center .uk-card-header div:eq(' + index + ')').addClass('active')
      $('.work-center .uk-card-header div:eq(' + index + ') span').addClass('active')
      if (type == 'newtask') {
        //我的待办新接口
        await this.taskStore.initNewTaskList({
          page: 1,
          pageSize: 8,
          Activity: ''
        })
        let ret = this.taskStore.getTask
        this.show_loading1 = false
        this.newtasknum = ret.total > 99 ? '99+' : ret.total //我的待办数量
        this.TaskList.DataList = ret.records
        this.TaskList.DataType = type
        this.TaskList.Count = ret.total
      } else {
        //其他
        portal
          .wdgz_list({
            type: type,
            page: 1,
            pageSize: type === 'newtask' || type === 'completetask' ? 7 : 10,
            key: this.key
          })
          .then((res) => {
            let ret = res.data
            if (ret && res.key == this.key) {
              this.show_loading1 = false
              if (type === 'newtask') {
                this.newtasknum = ret.total > 99 ? '99+' : ret.total //我的待办数量
              } else if (type === 'completetask') {
                this.completetasknum = ret.total > 99 ? '99+' : ret.total //我的待办数量
              }
              // this.completetasknum = ret.completetasknum > 99 ? "99+" : ret.completetasknum;//我的已办数量
              // this.focusnum = ret.focusnum > 99 ? "99+" : ret.focusnum;//我的关注数量
              this.TaskList.DataList = ret.records
              this.TaskList.DataType = type
              this.TaskList.Count = ret.total
            }
          })
          .catch(() => {
            this.show_loading1 = false
          })
      }
    },

    //常用入口
    loadCYGN: function () {
      this.CYGNList = []
      portal.gnsc_list({}).then((ret) => {
        //默认图标数组
        var defaultIconArray = [
          gnsc_img1,
          gnsc_img2,
          gnsc_img3,
          gnsc_img4,
          gnsc_img5,
          gnsc_img6,
          gnsc_img7,
          gnsc_img8,
          gnsc_img9,
          gnsc_img10
        ]
        let baseUrl = this.baseUrl
        var defaultIconIndex = 0
        ret.forEach(function (item, index, arr) {
          //没有别名更改为名称
          if (item.Alias === null || item.Alias === '') {
            item.Alias = item.Name
          }
          //图标处理逻辑
          if (item.IconUrl === null) {
            if (defaultIconIndex === 10) {
              defaultIconIndex = 0
            }
            item.IconUrl = defaultIconArray[defaultIconIndex]
            defaultIconIndex += 1
          } else {
            item.IconUrl = baseUrl + '/BasicApplication/DownloadFile?FileID=' + item.IconUrl
          }
        })

        this.CYGNList = ret
      })
    },

    //我的消息 我的文件
    loadMsgAndFile: function (type) {
      this.show_loading = true
      this.FileList = []
      this.MsgList = []

      //我的待办
      portal
        .wdgz_list({
          type: type,
          page: 1,
          pageSize: 10
        })
        .then((ret) => {
          this.show_loading = false
          ret = ret.data
          if (type == 'msg') {
            this.MsgList = ret.records
            this.MsgCount = ret.records.length
            this.MsgNewCount = ret.total
            if (ret.Count > 99) {
              this.MsgNewCount = '99+'
            }
          } else {
            this.FileList = ret.records
            this.FileCount = ret.total
          }
        })
        .catch(() => {
          this.show_loading = false
        })
    },

    //打开关注和新闻详情
    OpenView: async function (type, item) {
      console.log('item======================>', item)
      var url
      switch (type) {
        case 0:
          url =
            '/Portal/NewsCenter/NewsDetail?Code=TPXW&navigation=' +
            encodeURIComponent('图片新闻') +
            '&ID=' +
            item.ID
          break

        case 1:
          var Code = ''
          var navigation = $('.work-top .uk-card-header > .active > .active').text()
          if (navigation == '院发文') {
            navigation = '院发文'
            Code = 'YNFW'
          } else if (navigation == '院通知') {
            Code = 'YNTZ'
          } else if (navigation == '院公告') {
            Code = 'YNGG'
          }

          url =
            '/Portal/NewsCenter/NewsDetail?Code=' +
            Code +
            '&ID=' +
            item.ID +
            '&navigation=' +
            encodeURIComponent(navigation)
          this.PageLogRecord(Code, item, navigation)
          break

        case 2:
          var tabName = $('.work-center .uk-card-header .active .active').text()
          if (tabName.indexOf('我的待办') != -1) {
            this.openTask(item)
          } else if (tabName.indexOf('我的已办') != -1) {
            const res = await PortalApi.IsOuterForm({ flowId: item.FlowID })
            var _isouterform = res
            if (_isouterform == true) {
              isoutform = true
              url = item.ViewUrl
              if (url.indexOf('id=') > -1 && url.indexOf('ID=') > -1) {
                url = url.replace(/[?&]ID=[^&]*&?/g, '&')
              }
              url = url.replace(/\{token\}/g, getAccessToken())
            } else {
              url = item.ViewUrl + '&token=' + getAccessToken()
            }
            console.log(this.baseUrl + url)
            window.open(this.baseUrl + url)
            return
          } else if (tabName.indexOf('我的关注') != -1) {
            PortalApi.GetMyFocusByID({ ID: item.ID }).then((ret) => {
              if (!ret.newtask) {
                if (item.ExecURL.indexOf('{token}')) {
                  url = item.ExecURL.replace('{token}', getAccessToken())
                } else {
                  url = item.ExecURL + '&token=' + getAccessToken()
                }
                window.open(this.baseUrl + url)
              } else {
                this.openTask(ret.newtask)
              }
            })
          }
          const objdata = {
            menuId: item.ID,
            funcname: item.Title ? item.Title : item.FlowName,
            funcurl: item.URL ? item.URL : item.ExecURL
          }
          const log = LogApi.funcLog(objdata)
          break
      }

      if (!url) {
        return
      }
      window.open(url)
    },

    //打开菜单
    OpenRes: function (url, item, pageName) {
      //记录功能日志
      if (url.indexOf('?') > 0) url += '&'
      else url += '?'
      url = replaceUrl({ url: url })
      // 相对路径修改为绝对路径
      if (!url.startsWith('http')) {
        url = this.proxy.$getFullUrl(url)
      }
      if (url.indexOf('http') == 0 && url.indexOf('token') == -1) {
        url += 'token=' + getAccessToken()
      }
      window.open(url)

      //写日志
      const objdata = {
        funcname: item.Name,
        funcurl: item.Url,
        menuId: item.ID
      }
      const log = LogApi.funcLog(objdata)
    },

    //打开消息
    OpenMsg: function (item) {
      let url =
        import.meta.env.VITE_TOURL_PREFIX +
        `/BasicApplication/InfoManage/Msg/MsgView?FuncType=Receive&ID=${item.ID}&Type=${item.Type}`
      const objdata = {
        pagename: item.Content,
        pageurl: url,
        tag: '我的消息',
        id: item.ID
      }
      const log = LogApi.pageLog(objdata)
      // window.open('/Portal/MsgList')
      window.open(url)
    },
    //监听图片加载
    handleImageLoaded: function () {
      if (!this.loading_image) {
        return
      }
      this.loading_image = false
    },
    //取消关注、关注
    FocusFlow: function (type, item, tabName) {
      var Title
      if (tabName == 'newtask') {
        Title = item.ActivityName
      } else if (tabName == 'completetask') {
        Title = item.TaskName
      } else if (tabName == 'apply') {
        Title = item.Title
      } else if (tabName == 'focus') {
        Title = item.FlowName
      }

      var msg = type == 0 ? '是否取消关注【' + Title + '】？' : '是否关注【' + Title + '】？'

      message.confirm(msg, '提示').then(() => {
        PortalApi.execMyFocus({
          type: type,
          tabName: tabName,
          myFlow: JSON.stringify(item)
        }).then((ret) => {
          if (ret) {
            ElMessage({
              message: type == 0 ? '已经取消关注！' : '关注成功，可在【我的关注】列表查看。',
              type: 'success',
              offset: '10'
            })
            if (tabName == 'newtask') {
              this.loadTask(0, tabName)
            } else if (tabName == 'completetask') {
              this.loadTask(1, tabName)
            } else if (tabName == 'focus') {
              this.loadTask(2, tabName)
            }
          } else {
            ElNotification({
              title: 'Success',
              message: 'This is a success message',
              type: 'error'
            })
          }
        })
      })
    },

    _scgetopflag: function () {
      var rtn = {}
      var _skey = _getquerystring('sKey')
      var _stepkey = _getquerystring('StepKey')
      var _id = _getquerystring('Id') || _getquerystring('ID') || _getquerystring('id')
      var _templetcode = _getquerystring('TempletCode')
      if (_skey) {
        rtn['sKey'] = _skey
      }

      if (_stepkey) {
        rtn['StepKey'] = _stepkey
      }

      if (_id) {
        rtn['Id'] = _id
      }

      if (_templetcode) {
        rtn['TempletCode'] = _templetcode
      }
      return rtn
    },

    //取消收藏
    CancelSc: function (file) {
      var serverroot00 = 'http://10.10.1.173:8097/' + 'files/cancelscmyfav/'
      var ev = window.event
      if (!ev) {
        return
      }
      var _fileinfo = [file.FileKey, '', '']
      var _lk = _fileinfo[0]
      var _opfalg = this._scgetopflag()

      PortalApi.CancelSc()

      _cmaj(
        null,
        'post',
        serverroot00 + 'files/cancelscmyfav/',
        {
          fileKey: encodeURI(_lk),
          userId: _userid || _UserInfo['ID'],
          deptId: _deptid || _UserInfo['DeptID'],
          opflag: JSON.stringify(_opfalg)
        },
        null,
        null,
        function (dat) {
          var _tar = ev.target
          $(_tar).closest('li').remove()
        },
        function (err) {
          console.log(err)
        }
      )
    },

    //下载服务指南文件
    onDownloadAndPreviewFile: function (e, fileId, type) {
      var newWindow = window.open()
      var result = GetFileToken(fileId)
      if (result) {
        if (type === 1) {
          newWindow.location.href = '/FileManager/DownloadFile?FileToken=' + result
        } else if (type === 2) {
          newWindow.location.href = '/UIBuilder/UIViewer/PreviewPage?FileToken=' + result
        }
      } else {
        alert('该文件验证失败，暂不能下载，请联系管理员！')
      }
    },

    //下载附件
    downLoadFile: async function (fileName, type) {
      if (parseInt(type) === 1) {
        //获取文件token
        const result = await PortalApi.GetFileToken({ fileId: fileName })
        console.log(result)
        if (result) {
          window.open(downLoadUrl + '?FileToken=' + result)
        } else {
          alert('该文件验证失败，暂不能下载，请联系管理员！')
        }
      } else {
        //老的中心网下载
        window.location.href = 'http://10.10.1.29/Portal/Home/download?name=' + escape(fileName)
      }
    },

    //下载文件 预览文件
    DownLoadAndPreviewFile: async function (file, type) {
      var url = file.FileKey
      var _pre = ''
      var _correctfilekey = function (filekey) {
        if (filekey && filekey.startsWith('|')) {
          return filekey.substring(1, filekey.length)
        }
        return filekey
      }
      if (url.indexOf('http:') == 0 && url.indexOf('|') > 0) {
        _pre = url.split('|')[1]
        url = url.split('|')[0]
      } else {
        if (url.indexOf('|') == 0) {
          url = url.substring(1, url.length)
        }
      }
      console.log(file.Cgroup)
      switch (file.Cgroup) {
        case 'kd':
          //获取文件token
          const result = await PortalApi.GetFileToken({ fileId: _correctfilekey(file.FileKey) })
          if (result) {
            if (url.indexOf('http:') < 0) {
              url = ''
            }
            if (type === 0) {
              window.open(downLoadUrl + '?FileToken=' + result)
            } else if (type === 1) {
              window.open(url + '/UIBuilder/UIViewer/PreviewPage?FileToken=' + result)
            }
          } else {
            alert('该文件验证失败，暂不能下载，请联系管理员！')
          }
          break

        case 'mini':
          window.open('http://*********:8080/FileStore/Download.aspx?FileId=' + encodeURI(url))
          break

        default:
          if (url) {
            var _alias = file.AliasName
            if (_alias) {
              switch (_alias) {
                case 'attachmentold':
                  this.downLoadFile(url, 2)
                  break
                case 'attachmentnew':
                  this.downLoadFile(url, 1)
                  break
              }
            } else {
              if (url.indexOf('javascript:') == 0) {
                // var _aimstr = url.substring("javascript:".length, url.length);
                // eval(_aimstr);
                // console.log(url)
                if (url.indexOf('fileID') != -1) {
                  let idurl = url.substring(url.indexOf('fileID'))
                  if (idurl.indexOf('&') != -1) {
                    idurl = idurl.substring(idurl.indexOf('&'))
                  } else {
                    idurl = idurl.replace("')", '').replace('fileID=', '')
                  }
                  this.downLoadFile(idurl, 1)
                }
              } else {
                if (type === 0) {
                  if (url.indexOf('/BasicApplication/KindEditor/DownloadFile?path') != -1) {
                    url = url.substring(url.indexOf('/BasicApplication'))
                    url = this.baseUrl + url + '&token=' + getAccessToken()
                  }
                  window.open(url)
                } else if (type === 1) {
                  window.open(url)
                } else {
                  alert('该文件无法预览，请下载查看！')
                }
              }
            }
          }
          break
      }
    },

    //打开待办详情
    openTask: async function (data) {
      if (data.URL.indexOf('http') == 0) {
        window.open(data.URL)
      }
    },

    //功能收藏更多
    CYGNModify: function () {
      let url =
        '/UIBuilder/UIViewer/EditerListViewer?TempletCode=EditerList_ad5d00e2e6d442b4a6bde149818a986e&token=' +
        getAccessToken()
      url = this.baseUrl + url
      window.open(url)
    },

    //更多
    tabMore: function (type, e) {
      if (type == 0) {
        var tabName = $(e.currentTarget.parentElement).find('.active').text()

        if (tabName.indexOf('我的待办') != -1) {
          useAppStore().set_work_menu('newtask') //设置全局变量
          this.$router.push({ path: 'myWork' })
        } else if (tabName.indexOf('我的已办') != -1) {
          useAppStore().set_work_menu('completetask') //设置全局变量
          this.$router.push({ path: 'myWork' })
        } else if (tabName.indexOf('我的关注') != -1) {
          useAppStore().set_work_menu('focus') //设置全局变量
          this.$router.push({ path: 'myWork' })
        } else if (tabName.indexOf('院发文') != -1) {
          window.open('/Portal/NewsCenter/PublicInfoList?code=YNFW')
        } else if (tabName.indexOf('院通知') != -1) {
          window.open('/Portal/NewsCenter/PublicInfoList?code=YNTZ')
        } else if (tabName.indexOf('院公告') != -1) {
          window.open('/Portal/NewsCenter/PublicInfoList?code=YNGG')
        } else {
          alert('功能正在开发中...')
        }
      } else if (type == 1) {
        //我的消息
        // window.open('/Portal/MsgList')
        let url =
          import.meta.env.VITE_TOURL_PREFIX + '/BasicApplication/InfoManage/Msg/UnreadMsgList'
        window.open(url)
      } else if (type == 2) {
        //我的文件
        useAppStore().set_work_menu('myfile') //设置全局变量
        this.$router.push({ path: 'myWork' })
        // window.location.href = "/Portal/WorkCenter/MyWork?code=myfile";
      }
    },
    handleSocket: function (obj) {
      console.log(obj.type)
      if ('msg' == obj.type) {
        this.loadMsgAndFile('msg') //更新消息
      }

      if ('task' == obj.type) {
        this.loadTask(0, 'newtask') //更新任务
      }

      if ('completetask' == obj.type) {
        this.loadTask(0, 'completetask') //更新任务
      }

      if ('myfile' == obj.type) {
        this.loadMsgAndFile(0, 'myfile') //更新任务
      }

      if ('focus' == obj.type) {
        this.loadTask(0, 'focus') //更新任务
      }

      if ('news' == obj.type) {
        this.loadTpxw() //图片新闻
        this.loadYNXX(0) //院发文、院内通知、院内公告
      }
    },
    async PageLogRecord(code, item, tag) {
      let objdata = {
        pagename: item.Title,
        pageurl: this.detail_url(code, item.ID, tag),
        tag: tag,
        id: item.ID
      }
      const log = await LogApi.pageLog(objdata)
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('../../assets/css/uk.min.css');
@import url('../../assets/css/master.css');
@import url('../../assets/css/bootstrap.min.css');
@import url('../../assets/css/workcenter.css');

.db_list {
  width: 1104px;
  background: #fff;
  min-height: 700px;
  margin-left: auto;
}

.siderbar {
  position: fixed;
  z-index: 999;
  // margin-top: 80px;
  width: 260px;
  border-radius: 3px;
  background: #fff;
  color: #666;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  padding: 40px 0;

  ul {
    margin: 0 !important;
    padding: 0 !important;
  }

  .p_item {
    white-space: nowrap;
    text-overflow: ellipsis;
    border-left: 2px solid #fff;

    .m_a {
      display: inline-block;
      color: black;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      height: 30px;

      &.expend::after {
        background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        transform: rotate(180deg);
        transition: transform 0.4s ease-out;
        width: 1.5em;
        height: 1.5em;
        content: '';
        margin-left: 65px;
      }

      &.open::after {
        transform: rotate(90deg);
      }

      .icon {
        margin-left: 40px;
      }

      .text {
        font-size: 15px;
        margin-left: 6px;
        color: #000;
      }
    }

    .ul_sub {
      transition: height 0.3s ease-out;
      overflow: hidden;
      margin-left: -30px;

      li {
        height: 36px;
        line-height: 36px;
        width: 100%;

        a {
          width: 100%;
          height: 100%;
          display: inline-block;
          color: #999;
          font-weight: normal;
          padding-left: 66px;
          text-decoration: none;
          cursor: pointer;

          &:hover {
            color: #333;
          }
        }
      }
    }
  }
}

.work-top .uk-card .uk-card-header div:first-child > .active {
  background: url('@/assets/icons/portal/index_icon_yfw.png') no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(2) > .active {
  background: url('@/assets/icons/portal/index_icon_ytz.png') no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(3) > .active {
  background: url('@/assets/icons/portal/index_icon_ygg.png') no-repeat center center;
  background-size: 90% auto;
}

.work-center .uk-card .uk-card-header > div:first-child::before {
  content: '';
  width: 20px;
  height: 24px;
  //background: url("@/assets/icons/portal/index_icon_task.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(2)::before {
  content: '';
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_taskok.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(3)::before {
  content: '';
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_msg.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(1)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_task_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(2)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_taskok_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(3)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_msg_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.swiper-pagination {
  border: 2px solid blue !important;
}

.khidi_banner {
  width: 460px;
  height: 330px;

  .img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .desc {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 4;
    height: 30px;
    font-size: 14px;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 10px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
  }
}

.swiper-pagination-bullet {
  width: 30px !important;
  height: 30px;
  background-color: #fff;
  opacity: 0.5;
  border: 1px solid #000;
}

.swiper-pagination-bullet-active {
  background-color: #000;
  opacity: 1;
}

.hbm-zxzx-body-leftbar1 {
  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
}

.gcs_ztzl {
  padding: 0;
}

.demonstration {
  color: var(--el-text-color-secondary);
}

// .el-carousel__item h3 {
//   // color: #475669;
//   opacity: 0.75;
//   // line-height: 40px;
//   margin: 0;
//   text-align: center;
// }

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  // background-color: #d3dce6; //图片背景颜色
  background-color: #ffffff;
}

.TPXWImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.btm {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);

  .carousel-title {
    background: rgba(0, 0, 0, 0.3);
    color: #fff;

    // position: absolute;
    // bottom: 0px;
    // left: 0px;

    font-size: 14px;
    font-family: '微软雅黑';
    text-align: center;

    display: inline-block;
    background: none;

    border: none;
    height: 40px;
    line-height: 40px;
    width: 85%;

    padding: 0 10px;
    margin: 0;
    box-sizing: border-box;

    overflow: hidden; //隐藏文字
    text-overflow: ellipsis; //显示 ...
    white-space: nowrap; //不换行
  }

  .more {
    width: 50px;
    font-size: 13px;
  }
}

:deep(.el-carousel__indicators) {
  position: absolute;
  bottom: 0px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: -10px;
}

.more {
  color: #fff;
  position: absolute;
  right: 2px;
}

a:hover,
a:active {
  text-decoration: none;
  color: #fff;
  /* 设置鼠标悬停时的颜色 */
}
</style>
