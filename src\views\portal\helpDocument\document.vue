<template>
  <!-- v-if="edit"  v-hasPermi="['system:document:update']" system:user:list-->
  <div v-if="hasPermi('system:document:update')" style="background-color: #f1f3f4;">
    <Toolbar
      id="toolbar"
      style="border: 1px solid #e5e5ea;"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      id="editor"
      :style="{height: docHeight+'px'}"
      style="overflow-y: hidden;border: 1px solid #e5e5ea;
        margin: 0 auto;width: 900px;
        box-shadow: 0 2px 10px rgb(0 0 0 / 12%);"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @on-created="handleCreated"
    />
  </div>
  <div v-else style="background-color: #f1f3f4;">
    <!-- <Toolbar
      id="toolReader"
      style="border: 1px solid #e5e5ea;"
      :editor="readerRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
      :style="{height: docHeight+'px'}"
    /> -->
    <Editor
      id="reader"
      :style="{height: docHeight+'px'}"
      style="overflow-y: hidden;border: 1px solid #e5e5ea;
        margin: 0 auto;width: 900px;
        box-shadow: 0 2px 10px rgb(0 0 0 / 12%);"
      v-model="readerHtml"
      :defaultConfig="readerConfig"
      :mode="mode"
      @on-created="created"
    />
  </div>
</template>

<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import {CACHE_KEY, Storage} from '@/hooks/web/useCache'
import {onBeforeUnmount, ref, shallowRef, onMounted, computed} from 'vue'
import {Editor, Toolbar} from '@wangeditor/editor-for-vue'
import {getAccessToken, getTenantId} from '@/utils/auth'
import {getHelpDoc, updateHelpDoc} from '@/api/portal'
import {useRoute} from 'vue-router';
import {useAppStore} from '@/store/modules/app'

defineOptions({
  name: 'Document'
})
defineProps({
  docHeight: {
    type: Number, // 指定类型
    required: true, // 是否必传
    default: 0 // 默认值
  }
})
const docInfo = useAppStore().docInfo
//帮助文档富文本相关
type InsertFnType = (url: string, alt: string, href: string) => void
const mode = ref('default')
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const readerRef = shallowRef()
// 内容 HTML
const valueHtml = ref('')
const readerHtml = ref('')
const doc_id = ref('')
//获得帮助文档数据
const route = useRoute();
let routeName = computed(() => route.name)
const emit = defineEmits(['updateTitle'])
const routeList = ['PortalWorkIndex', 'ad86013c-bf5d-49d4-a11c-15239cd47d55', 'ad86013c-b70d-46b1-9db4-9ff6e26e72f4']
onMounted(() => {
  let name = routeList[0]
  if (routeList.includes(routeName.value)) {
    name = routeName.value
  }
  getDoc({name: name}).then(res => {
    emit('updateTitle', docInfo.title + '帮助文档')
    valueHtml.value = res?.doc_content
    readerHtml.value = res?.doc_content
    doc_id.value = res?.doc_id
  })
})
const getDoc = async (param) => {
  return await getHelpDoc(param)
}
let editorConfig = ref({
  placeholder: '请输入内容...',
  readOnly: false,
  customAlert: (s: string, t: string) => {
    switch (t) {
      case 'success':
        ElMessage.success(s)
        break
      case 'info':
        ElMessage.info(s)
        break
      case 'warning':
        ElMessage.warning(s)
        break
      case 'error':
        ElMessage.error(s)
        break
      default:
        ElMessage.info(s)
        break
    }
  },
  autoFocus: false,
  scroll: true,
  MENU_CONF: {
    ['uploadImage']: {
      server: import.meta.env.VITE_UPLOAD_URL,
      base64LimitSize: 10485760,
      // 单个文件的最大体积限制，默认为 2M
      maxFileSize: 5 * 1024 * 1024,
      // 最多可上传几个文件，默认为 100
      maxNumberOfFiles: 10,
      // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
      allowedFileTypes: ['image/*'],
      // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
      meta: {},
      // 将 meta 拼接到 url 参数中，默认 false
      metaWithUrl: false,
      // 自定义增加 http  header
      headers: {
        Accept: '*',
        Authorization: 'Bearer ' + getAccessToken(),
        'tenant-id': getTenantId()
      },
      // 跨域是否传递 cookie ，默认为 false
      withCredentials: true,
      // 超时时间，默认为 10 秒
      timeout: 5 * 1000, // 5 秒
      // form-data fieldName，后端接口参数名称，默认值wangeditor-uploaded-image
      fieldName: 'file',
      // 上传之前触发
      onBeforeUpload(file: File) {
        return file
      },
      // 上传进度的回调函数
      onProgress(progress: number) {
        // progress 是 0-100 的数字
      },
      onSuccess(file: File, res: any) {
        ElMessage.success('上传成功')
      },
      onFailed(file: File, res: any) {
        ElMessage.error('上传失败')
      },
      onError(file: File, err: any, res: any) {
        ElMessage.error('上传错误')
      },
      // 自定义插入图片
      customInsert(res: any, insertFn: InsertFnType) {
        insertFn(res.data, 'image', res.data)
      }
    }
  },
  uploadImgShowBase64: true
})
let readerConfig = ref({placeholder: '阅读模式...', readOnly: true,})
const toolbarConfig = {
  insertKeys: {
    index: -1,
    keys: ['my-menu-1'],
  },
  excludeKeys: ['fullScreen']
}
//保存帮助文档数据
const docName = (docInfo.title ? docInfo.title : '办公中心') + '帮助文档'
const updateDoc = (editor) => {
  let name = routeList[0]
  if (routeList.includes(routeName.value)) {
    name = routeName.value
  }
  const data = {
    docId: doc_id.value,
    docName: docName,
    routeName: name,
    docContent: editor.getHtml()
  }
  updateHelpDoc(data).then(() => {
    ElMessage.success('保存成功')
  }).catch(() => {
    ElMessage.error('保存失败')
  })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
onBeforeUnmount(() => {
  const reader = readerRef.value
  if (reader == null) return
  reader.destroy()
})
const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
  editor.on('updateDoc', (param) => {//注册自定义事件
    updateDoc(param)
  })
}
const created = (editor) => {
  readerRef.value = editor // 记录 editor 实例，重要！
}

//自定义权限切换组件
const {wsCache} = Storage()
const permissions = wsCache.get(CACHE_KEY.USER).permissions
const all_permission = '*:*:*'
const hasPermi = (key: string) => {
  const hasPermissions = permissions.some((permission: string) => {
    return all_permission === permission || key === permission
  })
  if (!hasPermissions) {
    return false
  } else {
    return true
  }
}
</script>

<style scoped>

</style>
