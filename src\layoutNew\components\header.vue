<template>
  <div class="head" :style="{height:isHome? `450px`:'200px'}">
    <div class="top">
      <a class="logo">
        <img src="@/assets/icons/portal/logo2.png" class="icon"/>
      </a>
      <div class="subtitle" style="display: none;">
        <div class="welcome">{{ userStore.getUser.nickname }}您好，欢迎来到中国电建！</div>
        <div class="time">
          <span>{{ today }}</span>
          <span class="sline"></span>
          <span>{{ currentWeekday }}</span>
          <span class="sline"></span>
          <span>{{ currentDateTime }}</span>
        </div>
      </div>
      <div class="tools">
        <div style="display: none;" class="phone">
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAEHUlEQVRYhbWYeYiWVRTGf06ihhEulUlF4p5Gf6i4lS2WpuMQmpMhk0ugDKFBuIeWjVKUmKJI9YHLOPZpkKRtn7a41Ki5lbQLpog72qKmpqaOHHleuN3u9877OvrAMMO99733Oeee85xzp9Y7736Kg1rAC8A4oCmwFhgB7CU5OgALgR+B9cBK4I98X5eWFF75XeCRKAeGAv2BG4F1QCXQIgWRH4BngC+BR4BdwFLg3riPajt/vwy0Bx4GTmjsVeAfwNzWxRmPwwXgJ/0sBm4CRgKfA+8Dk7TnfxB5pBswGngycNgsYCOwIIVXXJwCZsvIRsB24O4QEft5C5gA7Muz2fNy7bAEBw8B7gmM/6W5t4GvgZY+EfPCDYqPfDgDDAdmArfGrKsD9FSA/gI8512/YR4wDVgNNHSJ2AFzgapqLN0MLAemx6w5DzwLtFG2mZHbgObeOrvmD93rNiI9gFw1JCJMBZ723ZoHm4DeOsxirJW37EWgbSabK4qI3AwcSkjkKJABxgbm6gWuoUpXMRn4SJIQ4bz2mR4ROSHxSgqzcGBg3DTjT+C9gPUmcN8rIVxYnNTLZHNdjcgG4LEURI4AjT0xNKwC7gC2aM9e3nyZsq+OM2YeywLFBYrw4oQkbgGWAMuAS4H5v6UZT+gAVy9+BfYAHb1v1gAPFCgTHgSaxRBop9jYDRyQUsZhi9b7V2EZdJ83Zmnexogcl4aMz7OxBeBW4JzUcXRIogOoUNa4OOYFrBU9i9H60T3PAAbnScsLyvmT8kZSHHUFS/hMeuTjYkTkMDBHwhbCS/JEEv2IYJbe5o19k4fIHDfy39BBgwIL92i+IqAVcQgF9P9QWlI4ySVy1sYkQE0C6+36/lW7cM3ha8E6WV2uRsnFRTU8I1Oke2L4RJAcN1Et8LEfGKDU7HG9iZxT1bTetU9gfrMq9gqgX559O6jn2CvJ73o1RNAGg6WO7QPzH4tsuQi7aCq5r1Dp2CbS1ibemZYIkt6JTg3xYRZ3V99hVjfQfJlKwHzgN+BNZeNuFb5glxdHBG22SFUy1JntUlNtfenP8o71K695606raX5csWddff00RFAz9IXeOCEyp+WVIQrkWVLVEKxx7qRzrULfnoaIYYyeFJUx92xEH9LVxOGU4s+6wg2ZbO7KtadRyeg9YjJdpPu+WlRJJqyOfZDJ5ron9UgEs3aKXnFFNSAS4RVV455piaDX21MK5LIU1xuCeeZ3tw1Ii/XqtHqpLMQ1VXGw4G5tz9GaWHNQnZ3pzbfAqJTeeVTSMLS0pPBMTYigYJsmBbWC+B3Qt5pvCqQ3prTFpSWFlm2psiYOO4D71cuYkr4uaz+RotYF7pIX7Blq7YSp8k6X3bWCNUEm9fZYt/7XYugrjVu3Zpbbvzysn+nskgC4DHJr8eA69m4dAAAAAElFTkSuQmCC"
            class="icon"/>
          <div class="num">
            <span class="number">0871-63062895</span>
            <div class="txt">
              <span class="text">热线电话</span>
              <span class="sign">/</span>
            </div>
          </div>
        </div>
        <header_user/>
      </div>
    </div>
    <header_menu/>
    <headerBanner v-if="isHome"/>
    <img v-else src="@/assets/imgs/home/<USER>" class="top_bg"/>
  </div>
</template>
<script lang="ts" setup>
import {ref, watch} from 'vue';
import {useUserStore} from "@/store/modules/user";
import header_user from './user.vue';
import header_menu from './menu.vue';
import headerBanner from './header-banner.vue'
import {onMounted} from "vue";

const route = useRoute()
const userStore = useUserStore()
const currentPath = computed(() => route.path)
const today = ref<any>();
const currentDateTime = ref<any>();
const currentWeekday = ref<any>();
const currentPathString = ref(["/HomeTheme/newsHome"])
const isHome = ref(true)
import { useLayoutStore } from '@/store/modules/layout'
watch(
  () => currentPath.value,
  (newValue) => {
    isHome.value = currentPathString.value.includes(newValue);
  }
)

onMounted(async () => {
  isHome.value = currentPathString.value.includes(currentPath.value);
  getTodayDateTime()
  setInterval(() => {
    getTodayDateTime()
  }, 1000 * 60)
  useLayoutStore().setHeaderLoaded(true)
})
const getTodayDateTime = () => {
  let now = new Date();
//日期
  let DD = String(now.getDate()).padStart(2, '0'); // 获取日
  let MM = String(now.getMonth() + 1).padStart(2, '0'); //获取月份，1 月为 0
  let yyyy = now.getFullYear(); // 获取年
// 时间
  let hh = String(now.getHours()).padStart(2, '0');       //获取当前小时数(0-23)
  let mm = String(now.getMinutes()).padStart(2, '0');     //获取当前分钟数(0-59)

  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  today.value = yyyy + '年' + MM + '月' + DD + '日';
  currentDateTime.value = hh + ':' + mm;
  currentWeekday.value = weekdays[now.getDay()];
}


</script>

<style scoped lang="scss">
.head {
  height: 200px;
  position: relative;

  .top {
    height: 106px;
    position: absolute;
    top: 0;
    z-index: 3;
    width: 1252px;
    margin-left: -626px;
    left: 50%;
    display: flex;
    align-items: center;

    .icon {
      width: 600px;
      height: auto;
      margin-left: -97px;
    }

    .subtitle {
      width: 290px;
      height: 43px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-around;
      font-size: 16px;
      margin-left: 28px;
      padding-left: 28px;
      border-left: 1px solid #cccccc;

      .welcome {
        font-size: 16px;
        color: #f6f6f7;
      }

      .time {
        font-size: 14px;
        color: #94a0b1;
        display: flex;
        align-items: center;

        .sline {
          width: 1px;
          height: 16px;
          background: #afb3b5;
          display: block;
          margin: 0 15px;
        }
      }
    }

    .tools {
      display: flex;
      align-items: center;
      margin-left: auto;

      .phone {
        display: flex;
        align-items: center;
        margin-left: 20px;

        .icon {
          width: 34px;
          height: 34px;
        }

        .num {
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .number {
          font-size: 18px;
          color: #fff;
        }

        .text {
          font-size: 14px;
          color: #98a4b5;
        }

        .sign {
          font-size: 14px;
          color: #fff;
          margin-left: 5px;
        }
      }
    }
  }

  .top_bg {
    width: 100%;
    height: 200px;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
