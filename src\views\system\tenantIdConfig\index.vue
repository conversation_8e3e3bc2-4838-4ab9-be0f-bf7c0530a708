<template>

  <!-- 搜索 -->
  <ContentWrap>
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
      <el-form-item label="库名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入数据库名称" clearable @keyup.enter="handleQuery" class="!w-240px"
          style="width: auto;" />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>

        <el-button type="primary" plain @click="addTenantId">
          添加租户ID
        </el-button>

        <el-button type="danger" plain @click="delTenantId">
          去除租户ID
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="数据库表名">
        <template #default="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" min-width="110" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="showDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <el-dialog v-model="dialogTableVisible" title="处理情况" width="800" top="20%">
    <el-table :data="handleList">
      <el-table-column label="数据库" property="dataBase" width="150" />
      <el-table-column label="以处理表数量" property="setNum" width="200" />
      <el-table-column label="未处理表数量" property="notSetNum" width="200" />
      <el-table-column label="异常处理表数量" property="errorNum" width="200" />
      <el-table-column label="详情 1修改成功，0 没有修改" width="4000">
        <template #default="scope">{{ JSON.stringify(scope.row.data) }}</template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTableVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

</template>
<script lang="ts" setup>
import * as TenantApi from '@/api/system/tenant'
import * as TenantPackageApi from '@/api/system/tenantPackage'


defineOptions({ name: 'SystemTenant' })

const message = useMessage() // 消息弹窗

const dialogTableVisible = ref()
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数

const list = ref([]) // 列表的数据

const handleList = ref()

const dataBases = ref<Array<string>>([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
})
const queryFormRef = ref() // 搜索的表单
const packageList = ref([] as TenantPackageApi.TenantPackageVO[]) //租户套餐列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TenantApi.getDataBases(queryParams)

    list.value = data.list

    total.value = data.total
  } finally {
    loading.value = false
  }
}

const showDetail = (row: any) => {
  dialogTableVisible.value = true
}
/**添加租户id */
const addTenantId = async () => {
  if (dataBases.value.length < 1) {
    message.warning("请选择需要处理数据库")
    return;
  }
  loading.value = true
  let ret
  try {
    ret = await TenantApi.initTenant(dataBases.value);
    handleList.value = ret
  } finally {
    loading.value = false
  }
  dialogTableVisible.value = true

}

/**去除租户id */
const delTenantId = async () => {
  if (dataBases.value.length < 1) {
    message.warning("请选择需要处理数据库")
    return;
  }
  loading.value = true
  let ret
  try {
    ret = await TenantApi.delTenantId(dataBases.value);
    handleList.value = ret
  } finally {
    loading.value = false
  }
  dialogTableVisible.value = true

}

const handleSelectionChange = (selectedRows) => {
  const names = selectedRows.map(row => row.name);
  dataBases.value = names
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  packageList.value = await TenantPackageApi.getTenantPackageList()
})
</script>
