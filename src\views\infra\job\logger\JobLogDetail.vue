<template>
  <!-- 列表 -->
  <Dialog v-model="dialogVisible" title="Api执行列表信息" width="70%">
    <div style="text-align: center"><h3>ApiSQL执行列表</h3></div>
    <ContentWrap>
      <el-table v-loading="detailLoading" :data="apiSQLList">
        <el-table-column label="执行类别" align="center" prop="apiType" />
        <el-table-column label="步骤名称" align="center" prop="apiName" />
        <el-table-column label="步骤编号" align="center" prop="apiCode" />
        <el-table-column label="响应OR异常" prop="result" min-width="300" />
        <el-table-column label="耗时(ms)" align="center" prop="duration" min-width="100">
          <template #default="scope">
            <span>{{ scope.row.endTime - scope.row.beginTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="执行状态" align="center" prop="status">
          <template #default="scope">
            <el-tag
              :type="scope.row.status == 'success' ? 'primary' : 'danger'"
              effect="light"
              style="width: 60px"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" align="center" width="500px">
          <template #default="scope">
            <span>{{
              formatDate(scope.row.beginTime) + ' ~ ' + formatDate(scope.row.endTime)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="序号" align="center" prop="sortIndex" />
      </el-table>
    </ContentWrap>
    <div style="text-align: center"><h3>ApiUrl执行列表</h3></div>
    <ContentWrap>
      <el-table v-loading="detailLoading" :data="apiUrlList">
        <el-table-column label="执行类别" align="center" prop="apiType" />
        <el-table-column label="步骤名称" align="center" prop="apiName" />
        <el-table-column label="步骤编号" align="center" prop="apiCode" />
        <el-table-column label="响应OR异常" prop="result" min-width="300" />
        <el-table-column label="耗时(ms)" align="center" prop="duration" min-width="100">
          <template #default="scope">
            <span>{{ scope.row.endTime - scope.row.beginTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="执行状态" align="center" prop="status">
          <template #default="scope">
            <el-tag
              :type="scope.row.status == 'success' ? 'success' : 'danger'"
              effect="light"
              style="width: 60px"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" align="center" width="500px">
          <template #default="scope">
            <span>{{
              formatDate(scope.row.beginTime) + ' ~ ' + formatDate(scope.row.endTime)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="序号" align="center" prop="sortIndex" />
      </el-table>
    </ContentWrap>
  </Dialog>
</template>
<script lang="ts" setup>
import * as JobLogApi from '@/api/infra/jobLog'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'JobLogDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref({})
const apiSQLList = ref([])
const apiUrlList = ref([])
/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  // 查看，设置数据
  if (row) {
    detailLoading.value = true
    try {
      if (row.Result) {
        detailData.value = JSON.parse(row.Result)
      }
      if (detailData.value.ApiSQL) {
        apiSQLList.value = JSON.parse(detailData.value.ApiSQL)
      }
      if (detailData.value.ApiUrl) {
        apiUrlList.value = JSON.parse(detailData.value.ApiUrl)
      }
    } finally {
      detailLoading.value = false
    }
  }
}

watch(dialogVisible, (val) => {
  if (!val) {
    apiUrlList.value = []
    apiSQLList.value = []
  }
})
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
