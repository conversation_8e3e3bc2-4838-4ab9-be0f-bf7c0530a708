<template>
  <div style="height:100%; width:100%;text-align: center">
    <TreeDiagram
      ref="tree"
      :treeData="treeData"
      :height="500"
      @click-node="onClickNode" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {TreeDiagram} from '@/components/treeDiagram'
import {AResApi} from "@/api/system/ares";

const tree = ref()
const treeData = ref<any[]>([])
onMounted(() => {
  getTreeData()
})
const getTreeData = async () => {
  tree.value.showLoading()
  //todo 模拟参数暂时写死
  const params ={
    id: "a878014c-f01e-4ea5-8ae0-fb980bbddf19"
    //id: "MenuRoot"
  }
  const data =  await AResApi.getResTree(params);
  setTimeout(() => {
    treeData.value.push(...data)
    tree.value.hideLoading()
  }, 1000)
}
function onClickNode (data: any) {
  console.log('data:', data)
}
</script>
