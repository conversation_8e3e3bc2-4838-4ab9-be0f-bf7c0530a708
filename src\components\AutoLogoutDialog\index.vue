<template>
  <div v-if="visible" class="auto-logout-dialog-overlay">
    <div class="auto-logout-dialog">
      <div class="dialog-icon">
        <el-icon :size="48" color="#f56c6c">
          <WarningFilled />
        </el-icon>
      </div>
      <div class="dialog-title">
        自动退出登录
      </div>
      <div class="dialog-content">
        由于您长时间没有操作，已自动退出登录
      </div>
      <div class="dialog-actions">
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'

defineOptions({ name: 'AutoLogoutDialog' })

const visible = ref(false)
const loading = ref(false)

// 显示弹窗
const show = () => {
  visible.value = true
}

// 隐藏弹窗
const hide = () => {
  visible.value = false
}

// 确认按钮点击事件
const handleConfirm = () => {
  loading.value = true
  // 跳转到登录页
  window.location.href = '/portal/home/<USER>'
}

// 暴露方法给外部调用
defineExpose({
  show,
  hide
})
</script>

<style scoped>
.auto-logout-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.auto-logout-dialog {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  padding: 32px;
  text-align: center;
  min-width: 400px;
  max-width: 500px;
  animation: slideIn 0.3s ease-out;
}

.dialog-icon {
  margin-bottom: 16px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.dialog-content {
  font-size: 16px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 24px;
}

.dialog-actions {
  display: flex;
  justify-content: center;
}

.dialog-actions .el-button {
  min-width: 100px;
  height: 40px;
  font-size: 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 确保弹窗在所有内容之上 */
.auto-logout-dialog-overlay {
  pointer-events: all;
}

/* 防止页面滚动 */
.auto-logout-dialog-overlay {
  overflow: hidden;
}
</style>
