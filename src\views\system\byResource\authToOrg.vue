<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="角色多选" width="30%" height="400px">
          <div style="margin-bottom: 20px">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-checkbox style="margin-left: 30px" v-model="checkStrictly" label="对下级节点进行授权" size="large" />
          </div>
          <ContentWrap class="h-1/1">
            <Tree v-if="treeDataLoaded" ref="treeRef" :expandedKeys="expandedKeys" :checkStrictly="!checkStrictly" :showCheckbox="true" :data="treeData" @node-clicked="nodeClicked" :height="600"/>
          </ContentWrap>
  </Dialog>
</template>

<script lang="ts" setup>
import {Tree} from '@/components/tree';
import * as ByResourceApi from '@/api/system/byResource'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
//选项卡默认选中
const dialogVisible = ref(false)
// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const treeRef = ref(null)
const nodeId = ref('')
const resId = ref('')
//已绑定列表的数据
const isAuthLower = ref()
const checkStrictly = ref(false)
const expandedKeys = ref([])
//查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const open = async (id,isAuth) => {
  resId.value = id.value;
  isAuthLower.value = isAuth.value;
  treeRef.showCheckbox = true;
  getTreeData();

  dialogVisible.value = true;
}
defineExpose({ open })

/** 获取树数据 */
const getTreeData = async () => {
  try {
    const response = await ByResourceApi.getGroupTree({ id: null, groupType: "Org" });
    treeData.value = response;
    expandedKeys.value = response.map(item => item.id);
    treeDataLoaded.value = true;
  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
}
const nodeClicked = (node) => {
  nodeId.value = node.key
}

const submitForm = async () => {
  // 校验表单
  let checkedNodes = treeRef.value.getCheckedNodes();
  try {
    if (checkedNodes.length < 1){
      message.warning(t('common.selectOne'))
      return;
    } else {
      let params = {
        resId: resId.value,
        groupIds: checkedNodes.map(item => item.id).toString(),
        isAuthLower: isAuthLower.value,
      }
      await ByResourceApi.saveAGroupRes(params);
      message.success(t('common.createSuccess'))
    }
  } catch (e){

  } finally {
    dialogVisible.value = false
  }
}


</script>

