<template>
  <div class="panel" :style="{height:props.height+'px'}">
    <div class="panel-title">
      <div v-if="props.isLogo" class="logo">
        <img class="logo-img" :src="props.logoImg" alt=""/>
      </div>
      <div v-if="!props.isLogo" class="icon">
        <div class="top"></div>
        <div class="bottom"></div>
      </div>
      <div class="cn">
        <slot name="title">
          <span>{{ props.title }}</span>
          <span v-if="props.number" class="num">{{ props.numberNum }}</span>
        </slot>
      </div>
      <div class="eng">
        {{ props.subtitle }}
      </div>
      <div v-if="props.showMore" class="more" @click="moreEventClick">
        更多
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
defineOptions({name: 'MyPanel'})
const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  number: {
    type: Boolean,
    default: false
  },
  isLogo: {
    type: Boolean,
    default: false
  },
  logoImg: {
    type: String,
    default: ''
  },
  numberNum: {
    type: Number,
    default: 0
  },
  subtitle: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 200
  },
  showMore: {
    type: Boolean,
    default: true
  },
  showNavigationBar: {
    type: Boolean,
    default: true
  },
  showTabBar: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['moreClick'])
const moreEventClick = () => {
  emit('moreClick')
}
</script>
<style scoped lang="scss">
.panel {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 8px;
  font-size: 14px;
  color: #363636;
  width: 100%;

  .panel-title {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: 1px solid #999;

    .logo {
      .logo-img {
        width: 20px;
        height: 20px;
      }
    }

    .icon {
      width: 4px;
      height: 22px;
      padding-left: 6px;

      .top {
        width: 100%;
        height: 11px;
        background: #cecece;
      }

      .bottom {
        width: 100%;
        height: 11px;
        background: #3c91d6;
      }
    }

    .cn {
      display: flex;
      font-size: 16px;
      font-weight: 700;
      color: #060001;
      margin-left: 16px;

      .num {
        box-sizing: border-box;
        min-width: 22px;
        height: 22px;
        padding: 0 5px;
        border-radius: 500px;
        vertical-align: middle;
        background: #1e87f0;
        color: #fff !important;
        font-size: 12px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin-left: 3px;
      }
    }

    .eng {
      color: #b2b3b5;
      font-size: 18px;
      margin-left: 16px;
      margin-right: auto;
    }

    .more {
      color: #939293;
      font-size: 14px;
      border: 1px solid #dcdcdc;
      height: 20px;
      line-height: 20px;
      padding: 0 6px;
      position: relative;
      cursor: pointer;
      margin-left: auto;
      transition: all .3s;
    }

    .more:hover {
      color: #3ca1f7;
      border: 1px solid #3ca1f7;
    }

    .more:hover:after {
      background: #3ca1f7;
    }

    .more:after {
      content: "";
      display: block;
      width: 4px;
      height: 4px;
      background: #dcdcdc;
      position: absolute;
      right: -5px;
      top: 4px;
      border: 3px solid #fff;
      transition: all .3s;
    }

  }
}
.panel-0 {
  background-color: #fff;
  border: 0 solid transparent;
  border-radius: 4px;
}
.panel-1 {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}
</style>
