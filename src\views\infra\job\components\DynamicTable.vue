<template>
  <div class="table-info">
    <div class="table-list">
      <el-table :data="tableData" @selection-change="selsChange" style="width: 100%">
        <el-table-column type="selection" width="55" v-if="show" />
        <el-table-column label="步骤名称" prop="stepName">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.stepName }}</span>
            <el-input v-else v-model="scope.row.stepName" />
          </template>
        </el-table-column>
        <el-table-column label="步骤编码" prop="stepCode">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.stepCode }}</span>
            <el-input v-else v-model="scope.row.stepCode" />
          </template>
        </el-table-column>
        <el-table-column label="请求方式" prop="requestType" v-if="isApiUrl">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.requestType }}</span>
            <el-input v-else v-model="scope.row.requestType" />
          </template>
        </el-table-column>
        <el-table-column label="请求地址" prop="requestUrl" v-if="isApiUrl">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.requestUrl }}</span>
            <el-input v-else v-model="scope.row.requestUrl" />
          </template>
        </el-table-column>
        <el-table-column label="header(key)" prop="headerKey" v-if="isApiUrl">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.headerKey }}</span>
            <el-input v-else v-model="scope.row.headerKey" />
          </template>
        </el-table-column>
        <el-table-column label="header(value)" prop="headerValue" v-if="isApiUrl">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.headerValue }}</span>
            <el-input v-else v-model="scope.row.headerValue" />
          </template>
        </el-table-column>
        <el-table-column label="数据库" prop="connName" v-if="isApiSQL">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.connName }}</span>
            <el-input v-else v-model="scope.row.connName" />
          </template>
        </el-table-column>
        <el-table-column label="执行SQL" prop="execSQL" v-if="isApiSQL">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.execSQL }}</span>
            <el-input v-else v-model="scope.row.execSQL" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark">
          <template #default="scope">
            <span v-if="!scope.row.editable">{{ scope.row.remark }}</span>
            <el-input v-else v-model="scope.row.remark" />
          </template>
        </el-table-column>
        <el-table-column align="right" width="200px" v-if="props.show">
          <template #header>
            <el-form-item>
              <el-button type="primary" :icon="Plus" plain size="small" @click="addTableList">
                添加
              </el-button>
              <el-button type="danger" :icon="Delete" plain size="small" @click="delTableList">
                删除
              </el-button>
            </el-form-item>
          </template>
          <template #default="scope">
            <el-button size="small" @click="scope.row.editable = true" v-if="!scope.row.editable">
              编辑
            </el-button>
            <el-button size="small" @click="scope.row.editable = false" v-if="scope.row.editable">
              完成
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index, scope.row.editable)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'
import { Delete, Plus } from '@element-plus/icons-vue'
interface TableType {
  id: string
  newTimerTaskID: string
  headerKey: string
  sortIndex: number
  headerValue: string
  requestUrl: string
  stepCode: string
  stepName: string
  requestType: string
  // sql
  execSQL: string
  connName: string
  remark: string
}

const props = defineProps({
  ModelType: String,
  tableList: Array<TableType>,
  show: Boolean
})
const isApiUrl = props.ModelType === 'ApiUrl'
const isApiSQL = props.ModelType === 'ApiSQL'

const tableData = ref<Array<TableType>>([])

// 处理选择数据
const selsData = ref<Array<string>>([])
const selsChange = (sels) => {
  selsData.value = []
  selsData.value = sels.map((item) => item.id)
  console.log(selsData.value)
}

const handleDelete = (index: number, editable) => {
  if (!editable) {
    useMessage()
      .delConfirm('数据编辑完成，确定删除第' + index + 1 + '行数据')
      .then(() => {
        tableData.value.splice(index, 1)
      })
  } else {
    tableData.value.splice(index, 1)
  }
  // 重新刷新rowId
  tableData.value.forEach((item, i) => {
    item.sortIndex = i
  })
}
// 添加列表
const addTableList = () => {
  const newRow: TableType = {
    id: '',
    newTimerTaskID: '',
    headerKey: '',
    sortIndex: tableData.value.length,
    headerValue: '',
    requestUrl: '',
    stepCode: '',
    stepName: '',
    requestType: '',
    remark: ''
  }
  tableData.value.push(newRow)
}
// 删除列表
const delTableList = () => {
  if (selsData.value.length == 0) {
    useMessage().warning('请选择删除数据')
    return
  }
  useMessage()
    .delConfirm('确定删除选中数据')
    .then(() => {
      tableData.value = tableData.value.filter((item) => {
        return !selsData.value.includes(item.id)
      })
      // 重新刷新rowId
      tableData.value.forEach((item, i) => {
        item.sortIndex = i
      })
    })
    .catch(() => {
      useMessage().info('取消删除')
    })
}
// 监听表格数据变化
watch(
  () => tableData,
  () => {
    emitter.emit('ApiUrlOrSQLData', { modelType: props.ModelType, tableList: tableData.value })
  },
  { immediate: true, deep: true }
)

watch(
  () => props,
  () => {
    if (props.tableList?.length) {
      tableData.value = props.tableList
    }
  },
  { deep: true }
)

onMounted(() => {})
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__header-wrapper th {
  background-color: #f5f8ff;
  color: #343d3f;
}

.table-info {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
