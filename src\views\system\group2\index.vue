<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="简称" prop="shortName">
        <el-input
          v-model="queryParams.shortName"
          placeholder="请输入简称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:A-group:create']"
        >
          <Icon icon="ep:plus" class="mr-5px"/>
          新增
        </el-button>
        <el-button
          type="danger"
          @click="handleDelete(deleteId)"
          v-hasPermi="['system:A-res:delete']"
        >
          删除
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:A-group:export']"
        >
          <Icon icon="ep:download" class="mr-5px"/>
          导出
        </el-button>
        <el-button type="danger" plain @click="toggleExpandAll">
          <Icon icon="ep:sort" class="mr-5px"/>
          展开/折叠
        </el-button>
        <el-tooltip
          class="box-item"
          effect="dark"
          placement="top"
        >
          <template #content>注：选择系统角色时，只需要选择角色用户即可；
            <br/>选择组织角色后，需要选择关联的组织；
            <br/>选择动态角色，需要写对应的sql，查询人员信息；其中“UserID“字段必须有；动态角色尽量不要从大数据表中选择和过多使用，会影响性能；
          </template>
          <Icon icon="svg-icon:help" class="icon-help"/>
        </el-tooltip>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row>
      <el-col :span="4">
        <div class="tree_container height70">
          <el-table
            class="scrollable-row width220"
            @row-click="rowClick"
            v-loading="loading"
            :data="list"
            :stripe="true"
            :show-overflow-tooltip="true"
            row-key="id"
            :default-expand-all="isExpandAll"
            v-if="refreshTable"
          >
            <el-table-column label="名称" align="center" prop="name"/>
          </el-table>
          <!-- 分页 -->
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-col>
      <el-col :span="18" :offset="1">
        <AGroupForm ref="formRef" v-show="formif" @success="getList"/>
      </el-col>
    </el-row>

  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
</template>

<script setup lang="ts">
import {handleTree} from '@/utils/tree'
import download from '@/utils/download'
import {AGroupApi, AGroupVO} from '@/api/system/group'
import AGroupForm from './AGroupForm.vue'

/** 系统角色管理 列表 */
defineOptions({name: 'AGroup'})
const formif = ref(false);
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AGroupVO[]>([]) // 列表的数据
const queryParams = reactive({
  name: undefined,
  shortName: undefined,
  createTime: [],
  groupType: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.groupType = "Org";
    const data = await AGroupApi.getAGroupList(queryParams)
    list.value = handleTree(data, 'id', 'parentID')
  } finally {
    loading.value = false
  }
}
let deleteId = ref();
const rowClick = (row) => {
  deleteId = row.id;
  formif.value = true;
  formRef.value.open("edit", row.id)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}


/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AGroupApi.deleteAGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AGroupApi.exportAGroup(queryParams)
    download.excel(data, '系统角色管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 展开/折叠操作 */
const isExpandAll = ref(false) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态
const toggleExpandAll = async () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  await nextTick()
  refreshTable.value = true
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
