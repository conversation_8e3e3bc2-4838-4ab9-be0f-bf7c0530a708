<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="value" multiple placeholder="请选择" clearable @change="titleChange">
            <el-option
              v-for="item in titleList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.titleStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.spanStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">日期格式</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="form.itemStyle.dateFormate" placeholder="请选择" clearable>
            <el-option
              v-for="item in dateOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { dateOption } from '@/utils/formatTime';
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ConsultForm'
})
const props = defineProps<{ itemJson: any }>()

const titleList = ref([
  { id: 'YNFW', title: '院发文', name: '院内发文' },
  { id: 'RYRM', title: '人员任免', name: '人员任免' },
  { id: 'YNTZ', title: '院通知', name: '院内通知' },
  { id: 'YNGG', title: '院公告', name: '院内公告' },
  { id: 'XMRYRM', title: '项目人员任免', name: '项目人员任免' },
  { id: 'BMFW', title: '部门发文', name: '部门发文' },
  { id: 'ZDBD', title: '重点报道', name: '重点报道' }
])

const value = ref()


const form = ref({
  titleList: [{ id: 'YNFW', title: '院发文', name: '院内发文' }],
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: '',
    dateFormate: ''
  },
  isShow: {}
})

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('consult', form.value)
  },
  { immediate: true, deep: true }
)

defineExpose({ form })
const titleChange = () => {
  let data = titleList.value.filter((item) => value.value.indexOf(item.id) !== -1)
  form.value.titleList = data
}
const setValue = () => {
  value.value = form.value.titleList.map((item) => item.id)
}

onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
    setValue()
  }
})
</script>
