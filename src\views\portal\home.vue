<template>
  <div class="body">
    <div class="row">
      <banner :width="712" :height="372" :list="tpxwList"/>
      <div class="col" style="width: 514px;height: 372px;">
        <!--集团要闻&时政新闻-->
        <div class="hbm-zxzx-cell toper" style="width: 514px;height: 100%;">
          <div class="gcs_tab_jtywszxw">
            <div class="gcs_tabhd">
              <span><b><i>HOT</i></b></span>
              <ul>
                <li
                  @click="tab1_index = 1"
                  :class="1 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                  style="margin-left:0"
                  atr="jtyw"></li>
                <li
                  @click="tab1_index = 2"
                  :class="2 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                  style="margin-left: 50px"
                  atr="szxw"></li>
              </ul>
              <div class="more" @click="moreEventClick(tab1_index)">
                更多
              </div>
            </div>
            <div class="gcs_tabbd">
              <div v-if="1 == tab1_index" class="gcs_tabitem">
                <div class="list" style="width: 100%;">
                  <div
                    class="item"
                    v-for="(item, index) in jtywList?.slice(0, 8) || []"
                    :key="index"
                    @click="openHref('XMRYRM', item.ID, '集团要闻', item)">
                    <img
                      src="@/assets/imgs/home/<USER>"
                      v-if="item.IsTop&&item.IsTop!=='0'"
                      class="isTop"/>
                    <div class="date">
                      <div class="day">{{
                          dateFormat(item.CreateTime, 'month')
                        }}-{{ dateFormat(item.CreateTime, 'day') }}
                      </div>
                    </div>
                    <div
                      :title="item.Title"
                      class="href1"
                      :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                      {{ item.Title }}
                    </div>
                  </div>
                </div>
                <el-empty v-if="jtywList.length==0" description="数据为空"/>
              </div>
              <div v-if="2 == tab1_index" class="gcs_tabitem" v-cloak>
                <div class="list" style="width: 100%;">
                  <div
                    class="item"
                    v-for="(item, index) in szxwList?.slice(0, 8) || []"
                    :key="index"
                    @click="openHref('SZXW', item.ID, '时政新闻', item)">
                    <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                         class="isTop"/>
                    <div class="date">
                      <div class="day">{{
                          dateFormat(item.CreateTime, 'month')
                        }}-{{ dateFormat(item.CreateTime, 'day') }}
                      </div>
                    </div>
                    <div :title="item.Title" class="href1"
                         :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                      {{ item.Title }}
                    </div>
                  </div>
                </div>
                <el-empty v-if="szxwList.length==0" description="数据为空"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col bborder" style="position: relative">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane name="BMFW" label="院发文">
            <template #label>
              <span class="custom-tabs-label label-1">
                <span>院发文</span>
              </span>
            </template>
            <div class="list" style="width: 400px">
              <div
                class="item"
                v-for="(item, index) in ynfwList?.slice(0, 6) || []"
                :key="index"
                @click="openHref('YNFW', item.ID, '院发文', item)">
                <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                     class="isTop"/>
                <div class="date">
                  <div class="day">{{
                      dateFormat(item.CreateTime, 'month')
                    }}-{{ dateFormat(item.CreateTime, 'day') }}
                  </div>
                </div>
                <div :title="item.Title" class="href1"
                     :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                  {{ item.Title }}
                </div>
              </div>
              <el-empty v-if="ynfwList.length === 0" description="数据为空"/>
            </div>
          </el-tab-pane>
          <el-tab-pane name="RYRM" label="人员任免">
            <template #label>
              <span class="custom-tabs-label label-2">
                <span>人员任免</span>
              </span>
            </template>
            <div class="list" style="width: 400px">
              <div
                class="item"
                v-for="(item, index) in ryrmList?.slice(0, 6) || []"
                :key="index"
                @click="openHref('RYRM', item.ID, '人员任免', item)">
                <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                     class="isTop"/>
                <div class="date">
                  <div class="day">{{
                      dateFormat(item.CreateTime, 'month')
                    }}-{{ dateFormat(item.CreateTime, 'day') }}
                  </div>
                </div>
                <div :title="item.Title" class="href1"
                     :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                  {{ item.Title }}
                </div>
              </div>
              <el-empty v-if="ryrmList.length === 0" description="数据为空"/>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="custom-buttons" @click="openDetails(tabName)">
          <div class="work-more"> 更多</div>
        </div>
      </div>
      <div class="col" style="width: 400px">
        <myPanel title="院通知" :height="320" @more-click="moreClick('YNTZ', '院通知')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in ytzList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('YNTZ', item.ID, '院通知', item)"
            >
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="ytzList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 400px">
        <myPanel title="院公告" :height="320" @more-click="moreClick('YNGG', '院公告')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in yggList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('YNGG', item.ID, '院公告', item)"
            >
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="yggList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
    </div>
    <div class="row">
      <div class="col" style="width: 400px">
        <myPanel title="项目人员任免" :height="320"
                 @more-click="moreClick('XMRYRM', '项目人员任免')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in xmryrmList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('XMRYRM', item.ID, '项目人员任免', item)"
            >
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="xmryrmList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 400px">
        <myPanel title="部门发文" :height="320" @more-click="moreClick('BMFW', '部门发文')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in bmfwList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('BMFW', item.ID, '部门发文', item)">
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="bmfwList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 400px">
        <myPanel title="重点报道" :height="320" @more-click="moreClick('ZDBD', '重点报道')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in zdbdList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('ZDBD', item.ID, '重点报道', item)">
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
          </div>
        </myPanel>
      </div>

    </div>
    <div class="row">
      <div class="col" style="width: 400px">
        <myPanel title="专题专栏" :height="416" :show-more="false">
          <div class="list" style="width: 400px">
            <div class="row-list">
              <div
                class="c1btn btn"
                v-for="(item, index) in specialColumnList"
                :key="index"
                @click="openQuickLink(item)">
                <div class="txt">
                  <div class="c">{{ item.title }}</div>
                  <div class="e">{{ item.subTitle }}</div>
                </div>
                <img :src="item.imgUrl" class="icon"/>
              </div>
              <el-empty v-if="specialColumnList.length === 0" description="数据为空"/>
            </div>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 826px">
        <myPanel title="快速入口" :height="416" :show-more="false"
                 @more-click="moreClick('CWZJ', '财务资金')">
          <div class="list" style="width: 826px">
            <div class="row-list" style="justify-content:flex-start;gap: 6px;">
              <div
                class="c4btn btn"
                v-for="(item, index) in ksrkList?.slice(0, 12) || []"
                @click="openLink(item, '快速入口')"
                :key="index">
                <img src="@/assets/imgs/home/<USER>" class="icon"/>
                <div class="text">
                  <div class="tit">{{ item.Name }}</div>
                  <div class="desc"></div>
                </div>
              </div>
              <el-empty v-if="ksrkList.length === 0" description="数据为空"/>
            </div>
          </div>
        </myPanel>
      </div>
    </div>
    <div class="row">
      <div class="col" style="width: 400px">
        <myPanel title="财务资金" :height="314" @more-click="moreClick('CWZJ', '财务资金')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in cwzjList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('CWZJ', item.ID, '财务资金', item)">
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="cwzjList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 400px">
        <myPanel title="税务管理" :height="314" @more-click="moreClick('SWGL', '税务管理')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in swglList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('SWGL', item.ID, '税务管理', item)">
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="swglList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 400px">
        <myPanel
          title="资产与产权管理"
          :height="314"
          @more-click="moreClick('ZCYCQ', '资产与产权管理')">
          <div class="list" style="width: 400px">
            <div
              class="item"
              v-for="(item, index) in zcycqList?.slice(0, 6) || []"
              :key="index"
              @click="openHref('ZCYCQ', item.ID, '资产与产权管理', item)">
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop&&item.IsTop!=='0'"
                   class="isTop"/>
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <div :title="item.Title" class="href1"
                   :class="[item.IsTop&&item.IsTop!=='0' ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="zcycqList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
    </div>
    <div class="row">
      <div class="col" style="width: 1000px">
        <Science/>
      </div>
      <div class="col" style="width: 226px">
        <myPanel title="友情链接" class="panel-1" :height="595" :show-more="false">
          <div class="list" style="width: 226px">
            <a
              v-for="(item, index) in yqljList?.slice(0, 12) || []"
              :key="index"
              :href="_url(item.Url)"
              style="text-decoration: none"
              target="_blank"
              @click="savePageLog(item, '友情链接')">
              <div class="c3btn btn">
                <div class="text href1" style="flex: 1">
                  {{ item.Name }}
                </div>
                <DArrowRight class="icon"/>
              </div>
            </a>
            <el-empty v-if="yqljList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import myPanel from "@/components/Panel/src/index.vue"
import banner from '@/views/portal/components/banner.vue'
import {ref} from 'vue';
import * as PortalApi from "@/api/system/portal";
import * as portal from "@/api/portal";
import * as LogApi from '@/api/system/pageAndFuncLog'
import zlgh from "@/assets/imgs/zxzx/170036_row2_icon1.png"
import szyxt from "@/assets/imgs/zxzx/170036_row2_icon2.png"
import yzxx from "@/assets/imgs/zxzx/170036_row2_icon3.png"
import jlbgt from "@/assets/imgs/zxzx/170036_row2_icon4.png"
import {getAccessToken} from "@/utils/auth";
import Science from "@/layoutNew/portal/Science.vue"
import type {TabsPaneContext} from "element-plus";
import {DArrowRight} from "@element-plus/icons-vue";

const tpxwList = ref<Array<any> | null>([])
const ynfwList = ref<Array<any> | null>([])
const cwzjList = ref<Array<any> | null>([])
const swglList = ref<Array<any> | null>([])
const zcycqList = ref<Array<any> | null>([])
const zdbdList = ref<Array<any> | null>([])
const yggList = ref<Array<any> | null>([])
const ytzList = ref<Array<any> | null>([])
const ksrkList = ref<Array<any> | null>([])
const yqljList = ref<Array<any> | null>([])
const bmfwList = ref<Array<any> | null>([])
const xmryrmList = ref<Array<any> | null>([])
const ryrmList = ref<Array<any> | null>([])
const jtywList = ref<Array<any> | null>([])
const szxwList = ref<Array<any> | null>([])
const tab1_index = ref<number | null>(1)
const activeName = ref<string | null>('BMFW')
const tabName = ref<string | null>('院发文')
const router = useRouter() // 直接获取 router 实例
const specialColumnList = ref<Array<any> | null>([
  {
    title: '战略规划',
    subTitle: 'Strategic Planning',
    url: 'http://10.10.1.173:8001/Portal/NewsCenter/PublicInfoList?code=ZLGH',
    imgUrl: zlgh
  },
  {
    title: '企业宣传',
    subTitle: 'Company Promotion',
    url: '/Portal/NewsCenter/PublicInfoList?code=QYXC',
    imgUrl: szyxt
  },
  {
    title: '采购公示',
    subTitle: "Procurement Notice",
    url: import.meta.env.VITE_TOURL_PREFIX + '/UIBuilder/UIViewer/ListViewer?TempletCode=List_b1d9012c18cf4981a75e1539645a57fb',
    imgUrl: yzxx
  },
  {
    title: '减利因素曝光台',
    subTitle: 'Exposure Table',
    url: '/Portal/NewsCenter/PublicInfoList?code=JLBGT',
    imgUrl: jlbgt
  }
])
const loadTpxw = function () {
  PortalApi.newsCenterGetNewsList({code: 'TPXW', pageSize: 10}).then((result) => {
    tpxwList.value = result.records
  })
}
const loadYnfw = () => {
  portal
    .yfw_list({
      page: 1,
      pageSize: 10
    })
    .then((ret) => {
      ynfwList.value = ret.records
    })
}
const getJtyw = async () => {
  let result = await PortalApi.newsCenterGetNewsList({code: 'JTYW', pageSize: 9})
  jtywList.value = result.records
}
const getSzxw = async () => {
  let result = await PortalApi.newsCenterGetNewsList({code: 'SZXW', pageSize: 9})
  szxwList.value = result.records
}
//财务资金
const loadCwzj = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'CWZJ', pageSize: 8})
  cwzjList.value = result.records
}
//税务管理
const loadSwgl = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'SWGL', pageSize: 8})
  swglList.value = result.records
}
//资产与产权管理
const loadZcycq = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'ZCYCQ', pageSize: 8})
  zcycqList.value = result.records
}
//重点报道
const loadZdbd = async () => {
  let result = await PortalApi.newsCenterGetNewsList({code: 'ZDBD', pageSize: 8})
  zdbdList.value = result.records
}
//院公告
const loadYgg = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'YNGG', pageSize: 8})
  yggList.value = result.records
}
//院通知
const loadYtz = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'YNTZ', pageSize: 8})
  ytzList.value = result.records
}
//快速入口
const loadKsrk = async function () {
  let result = await PortalApi.newsCenterGetLinks({type: '快速入口', page: 1, pageSize: 120})
  ksrkList.value = result.records
}
//友情链接
const loadYqlj = async function () {
  let result = await PortalApi.newsCenterGetLinks({type: '友情链接', page: 1, pageSize: 6})
  yqljList.value = result.records
}
//获取 - 部门发文
const loadBmfw = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'BMFW', pageSize: 8})
  bmfwList.value = result.records
}
//项目人员任免
const loadXmryrm = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'XMRYRM', pageSize: 8})
  xmryrmList.value = result.records
}
//人员任免
const loadRyrm = async function () {
  let result = await PortalApi.newsCenterGetNewsList({code: 'RYRM', pageSize: 8})
  ryrmList.value = result.records
}

const addPasswordLog = async function () {
  let ret = await PortalApi.addPasswordLog({
    workNo: ''
  })
  console.log(ret)
}
onMounted(() => {
  // addPasswordLog()
  loadTpxw()
  getJtyw()
  getSzxw()
  loadZdbd()
  loadCwzj()
  loadSwgl()
  loadZcycq()
  loadYnfw()
  loadYgg()
  loadYtz()
  loadKsrk()
  loadYqlj()
  loadBmfw()
  loadXmryrm()
  loadRyrm()
})
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab.props.label)
  tabName.value = tab.props.label
}
const openDetails = function (tabName) {
  if (tabName == '院发文') {
    moreClick('YNFW', '院发文')
  } else if (tabName == '人员任免') {
    moreClick('RYRM', '人员任免')
  }
}
const moreClick = (type, name) => {
  const url = '/Portal/NewsCenter/PublicInfoList?code=' + type
  const objdata = {
    pagename: name,
    pageurl: url,
    tag: '更多',
    id: ''
  }
  LogApi.pageLog(objdata)
  window.open(url, '_blank')
}
const dateFormat = (date: string, type: string) => {
  const d = new Date(date)
  if (type === 'year') {
    return d.getFullYear()
  }
  if (type === 'month') {
    return d.getMonth() > 8 ? d.getMonth() + 1 : '0' + (d.getMonth() + 1)
  }
  if (type === 'day') {
    return d.getDate() > 9 ? d.getDate() : '0' + d.getDate()
  }
  return d.getMonth() + 1 + '-' + d.getDate()
}
const moreEventClick = (index) => {
  let url = index == 1 ? '/Portal/NewsCenter/PublicInfoList?code=JTYW' : '/Portal/NewsCenter/PublicInfoList?code=SZXW'
  window.open(url, '_blank')
}
const openHref = function (code, id, cate, item) {
  if (['TPXW', 'YNGG', 'YNTZ', 'YNFW', 'ZDBD', 'ZCYCQ', 'SWGL', 'SZXW', 'RYRM', 'CWZJ', 'XMRYRM', 'BMFW'].indexOf(code) > -1) {
    const url =
      '/Portal/NewsCenter/NewsDetail?Code=' + code + '&ID=' + id + '&navigation=' + encodeURI(cate)
    const objdata = {
      pagename: item.Title ? item.Title : item.title,
      pageurl: url,
      tag: cate,
      id: item.ID
    }
    LogApi.pageLog(objdata)
    window.open(url, '_blank')
  }
}

const openQuickLink = (item) => {
  const objdata = {
    pagename: item.title,
    pageurl: item.url,
    tag: '专题专栏',
    id: item.id
  }
  LogApi.pageLog(objdata)
  if (item.url.includes('http')) {
    window.open(item.url, '_blank')
  } else {
    window.open(_url(item.url), '_blank')
  }
}
const _url = function (url) {
  if (url != null && url != '') {
    if (url.indexOf('http') == -1 && url.indexOf('https') == -1) {
      if (url.indexOf('/UIBuilder/UIViewer/') != -1) {
        url = import.meta.env.VITE_TOURL_PREFIX + url
      }
    }

    if (url.indexOf('ftp://') == -1) {
      //排除ftp链接
      if (url.indexOf('?') > 0) {
        url += '&' //如果已经有？号则拼接&符号
      } else {
        url += '?' //如果没有？号则拼接?符号
      }
      if (url.indexOf('token') == -1) {
        //最后拼接token
        url += 'token=' + getAccessToken()
      }
    }
  }
  return url
}
const openLink = (item) => {
  window.open(_url(item.Url), '_blank')
  savePageLog(item, '快速入口')
}

function savePageLog(item, tag) {
  const objdata = {
    pagename: item.Name,
    pageurl: item.Url,
    tag: tag,
    id: item.ID
  }
  LogApi.pageLog(objdata)
}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/default.css');
@import url('@/assets/css/newsCenterMaster.css');

.c3btn:hover {
  .text {
    color: #0070ff;
  }

  .icon {
    color: #0070ff;
  }
}

:deep(.swiper) {
  width: 100%;
  height: 100%;
}

.panel-body {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 64px);
}

.gcs_tabhd {
  border-bottom: 1px solid #999 !important;
}

.gcs_tabhd span {
  margin-left: 76px !important;
}

.gcs_tabitem {
  font-size: 14px;
}

.links {
  text-decoration: none;
  color: #1c5283;
  line-height: 35px;
}

.links:hover {
  font-weight: bold;
}

.more {
  color: #939293;
  font-size: 14px;
  border: 1px solid #dcdcdc;
  height: 22px;
  line-height: 22px;
  padding: 0 6px;
  position: absolute;
  cursor: pointer;
  margin-left: auto;
  transition: all .3s;
  right: 0;
  top: 14px;
}

.more:hover {
  color: #3ca1f7;
  border: 1px solid #3ca1f7;
}

.more:hover:after {
  background: #3ca1f7;
}

.more:after {
  content: "";
  display: block;
  width: 4px;
  height: 4px;
  background: #dcdcdc;
  position: absolute;
  right: -5px;
  top: 4px;
  border: 3px solid #fff;
  transition: all .3s;
}

.bborder {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.custom-buttons {
  position: absolute;
  right: 0;
  cursor: pointer;
  top: 22px;

  .work-more {
    color: #939293;
    font-size: 14px;
    border: 1px solid #dcdcdc;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    position: relative;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.3s;
  }

  .work-more:hover {
    color: #3ca1f7;
    border: 1px solid #3ca1f7;
  }

  .work-more:hover:after {
    background: #3ca1f7;
  }

  .work-more:after {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    background: #dcdcdc;
    position: absolute;
    right: -5px;
    top: 4px;
    border: 3px solid #fff;
    transition: all 0.3s;
  }
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  margin: 0 !important;
}

:deep(.el-tabs__item) {
  height: 65px;
  font-size: 16px !important;
  font-weight: 700 !important;
}

:deep(.custom-tabs-label) {
  span {
    color: #060001;
  }
}

:deep(.is-active) {
  .custom-tabs-label {
    span {
      color: #409eff;
    }
  }
}

:deep(.el-tabs__nav-wrap) {
  height: 65px;
}

:deep(.el-tabs__nav-scroll) {
  position: absolute;
  bottom: 0;
}

.swiper-button-prev,
.swiper-button-next {
  color: white; /* 按钮颜色 */
  background: rgba(0, 0, 0, 0.5); /* 背景色 */
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 20px; /* 箭头大小 */
}

.swiper-button-prev {
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}
</style>
