<template>
  <div class="icon-title-wrapper">
    <div class="icon">
      <svg :width="width" :height="height" :fill="fill" class="svg">
        <use :xlink:href="`#icon-${icon}`" />
      </svg>
    </div>
    <div class="title" :itemId="itemId" :dataId="dataId">
      {{ title }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    //组件图标
    icon: {
      type: String,
      required: true,
      default: ''
    },
    //拖拽时寻找对应组件的ID
    itemId: {
      type: String,
      required: true,
      default: ''
    },
    //数据库保存的ID
    dataId: {
      type: String,
      required: false,
      default: ''
    },
    width: {
      type: [Number, String],
      default: '16'
    },
    height: {
      type: [Number, String],
      default: '16'
    },
    fill: {
      type: String,
      default: '#6F6161'
    },
    title: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {}
}
</script>

<style scoped>
.icon-title-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center; /* 如果需要水平居中 */
}

.icon {
  margin-top: 20px;
}

.title {
  font-size: 12px; /* 或者其他适合你的设计的尺寸 */
  font-weight: bold; /* 可选：加粗标题 */
  color: #6f6161;
}
</style>
