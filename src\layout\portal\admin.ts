import {getAccessToken} from "@/utils/auth";
import * as PortalApi from '@/api/system/portal'
import $ from 'jquery'
import {useAppStore} from "@/store/modules/app";
import router from "@/router";
// 访问前缀
const baseUrl = import.meta.env.VITE_TOURL_PREFIX;

export const openTask = async (data) => {
  if (data.URL.indexOf('http') == 0) {
    window.open(data.URL)
  } else {
    window.open(baseUrl + data.URL)
  }
  // let url = "";
  // let isoutform;
  // if (data.GoodWaySoft == "NK") //新平台任务
  // {
  //   const _flowId = data["FlowID"];
  //   const res = await PortalApi.IsOuterForm({ "flowId": _flowId });
  //   const _isouterform = res;
  //   if (_isouterform == true) {
  //     isoutform = true;
  //     url = "/UIBuilder/UIViewer/FlowPageOuter";
  //   }
  //   else {
  //     url = "/UIBuilder/UIViewer/FlowPage";
  //   }
  //   if (data.FlowCode == "Flow_ae62010b6dae4e09b23ed09a12c15718") { //任务查看页面
  //     url = "/UIBuilder/UIViewer/FlowPageViewer";
  //   }
  //   if (data.URL.indexOf("?") > 0) {
  //     url = url + "?" + data.URL.split("?")[1];
  //   }
  //
  //   if (url.indexOf('?') > 0) {
  //     url += "&";
  //   } else {
  //     url += "?";
  //   }
  //   url += "TaskID=" + data.ID;
  //
  //   if (isoutform == true) {
  //     url += "&outform=1";
  //     url = url.replace(/\{forminsid\}/g, data.FormInstanceID);
  //   }
  //   if (url.indexOf("{token}") > -1) {
  //     url = url.replace(/\{token\}/g, getAccessToken());
  //   } else {
  //     if (url.indexOf("?") > -1) {
  //       url += "&token=" + getAccessToken()
  //     } else {
  //       url += "?token=" + getAccessToken()
  //     }
  //   }
  //   if (data.FormInstanceID != "" && data.FormInstanceID != null && typeof data.FormInstanceID != "undefined" && !isoutform) {
  //     url += "&ID=" + data.FormInstanceID;
  //   }
  //   window.open(baseUrl + url);//打开窗口
  // } else if (data.GoodWaySoft == "RIIT") { //将75任务open对象push到数组
  //   url = "http://10.10.1.1:8901/UIBuilder/UIViewer/FlowPage";
  //
  //   if (data.URL.indexOf("?") > 0) {
  //     url = url + "?" + data.URL.split("?")[1];
  //   }
  //
  //   if (url.indexOf('?') > 0) {
  //     url += "&";
  //   } else {
  //     url += "?";
  //   }
  //
  //   url += "TaskID=" + data.ID;
  //   if (data.FormInstanceID != "" && data.FormInstanceID != null && data.FormInstanceID != undefined) {
  //     url += "&ID=" + data.FormInstanceID;
  //   }
  //
  //   url += "&token=" + getAccessToken();
  //
  //   window.open(baseUrl + url);//打开窗口
  // }
  // else if (data.GoodWaySoft == "SDY") { //将水电院任务open对象push到数组
  //   url = "http://10.10.1.173:10001/UIBuilder/UIViewer/FlowPage";
  //   if (data.URL.indexOf("?") > 0) {
  //     url = url + "?" + data.URL.split("?")[1];
  //   }
  //
  //   if (url.indexOf('?') > 0) {
  //     url += "&";
  //   } else {
  //     url += "?";
  //   }
  //
  //   url += "TaskID=" + data.ID;
  //   if (data.FormInstanceID != "" && data.FormInstanceID != null && data.FormInstanceID != undefined) {
  //     url += "&ID=" + data.FormInstanceID;
  //   }
  //
  //   url += "&token=" + getAccessToken();
  //   window.open(baseUrl + url);//打开窗口
  // }
  // else if (data.GoodWaySoft == "CJY") { //将水电院任务open对象push到数组
  //   url = "http://10.10.1.173:10003/UIBuilder/UIViewer/FlowPage";
  //   if (data.URL.indexOf("?") > 0) {
  //     url = url + "?" + data.URL.split("?")[1];
  //   }
  //
  //   if (url.indexOf('?') > 0) {
  //     url += "&";
  //   } else {
  //     url += "?";
  //   }
  //
  //   url += "TaskID=" + data.ID;
  //   if (data.FormInstanceID != "" && data.FormInstanceID != null && data.FormInstanceID != undefined) {
  //     url += "&ID=" + data.FormInstanceID;
  //   }
  //
  //   url += "&token=" + getAccessToken();
  //   window.open(baseUrl + url);//打开窗口
  // }
  // else if (data.GoodWaySoft == "XNY") { //将水电院任务open对象push到数组
  //   url = "http://10.10.1.173:10005/UIBuilder/UIViewer/FlowPage";
  //   if (data.URL.indexOf("?") > 0) {
  //     url = url + "?" + data.URL.split("?")[1];
  //   }
  //
  //   if (url.indexOf('?') > 0) {
  //     url += "&";
  //   } else {
  //     url += "?";
  //   }
  //
  //   url += "TaskID=" + data.ID;
  //   if (data.FormInstanceID != "" && data.FormInstanceID != null && data.FormInstanceID != undefined) {
  //     url += "&ID=" + data.FormInstanceID;
  //   }
  //
  //   url += "&token=" + getAccessToken();
  //   window.open(baseUrl + url);//打开窗口
  // }
  // else if (data.GoodWaySoft == "PC_PRPFlow" || data.GoodWaySoft == "PC_SRMSFlow" || data.GoodWaySoft == "JiuQiCaiWu") {
  //   //PRP系统的任务
  //   url = data.URL;
  //   url += "&token=" + getAccessToken();
  //   window.open(baseUrl + url);//打开窗口
  // } else if (data.GoodWaySoft == 'cwpt') {
  //   //智慧共享业务系统的任务
  //   //记录功能日志
  //   const pageUrl = window.location.href
  //   const objdata = {
  //     workno: userStore.user.workNo,
  //     pagename: '集团财务共享平台待办任务',
  //     pageurl: pageUrl,
  //     funcname: '第三方待办任务集成应用',
  //     funcurl: data.URL,
  //     MenuId: ''
  //   }
  //   PortalApi.FuncLog(objdata)
  //   window.open(data.URL) //打开窗口
  // } else {//老平台任务
  //   $.ajax({
  //     url: "/KHIDIService/Task/GetGWSTaskUrl?TaskID=" + data.ID,
  //     success: function (data) {
  //       try {
  //         const ret = eval(data);
  //         url = ret[0].url;
  //         if (url.indexOf("token") == -1) {
  //           url += "&token=" + getAccessToken();
  //         }
  //         window.open(baseUrl + url);//打开窗口
  //       } catch (ex) {
  //         url = data.substring(data.indexOf("'") + 1, data.indexOf(",") - 1);
  //       }
  //     },
  //     error: function (XMLHttpRequest, textStatus, errorThrown) { }
  //   });
  // }
}


//更多
export const tabMore = (type, e) => {

  if (type == 0) {
    const tabName = $(e.currentTarget.parentElement).find(".active").text();

    if (tabName.indexOf("我的待办") != -1) {
      useAppStore().set_work_menu('newtask');//设置全局变量
      router.push({path: 'myWork'});
    } else if (tabName.indexOf("我的已办") != -1) {
      useAppStore().set_work_menu('completetask');//设置全局变量
      router.push({path: 'myWork'});
    } else if (tabName.indexOf("我的关注") != -1) {
      useAppStore().set_work_menu('focus');//设置全局变量
      router.push({path: 'myWork'});
    } else if (tabName.indexOf("院发文") != -1) {
      window.open("/Portal/NewsCenter/PublicInfoList?code=YNFW");
    } else if (tabName.indexOf("院通知") != -1) {
      window.open("/Portal/NewsCenter/PublicInfoList?code=YNTZ");
    } else if (tabName.indexOf("院公告") != -1) {
      window.open("/Portal/NewsCenter/PublicInfoList?code=YNGG");
    } else {
      alert("功能正在开发中...");
    }
  } else if (type == 1 || type == 'msg') {//我的消息
    window.open("/Portal/MsgList");
  } else if (type == 2 || type == 'myfile') {//我的文件
    useAppStore().set_work_menu('myfile');//设置全局变量
    router.push({path: '/Portal/workCenter/myWork'});
  }
}
// 点击查看详情
export const OpenView = async (type, item) => {
  let url = '';
  switch (type) {
    case 0:
      url = "/Portal/NewsCenter/NewsDetail?Code=TPXW&navigation=" + encodeURIComponent("图片新闻") + "&ID=" + item.ID;
      break;

    case 1:
      let Code = "";
      let navigation = $(".work-top .uk-card-header > .active > .active").text();
      if (navigation == "院发文") {
        navigation = "院发文";
        Code = "YNFW";
      } else if (navigation == "院通知") {
        Code = "YNTZ";
      } else if (navigation == "院公告") {
        Code = "YNGG";
      }

      url = "/Portal/NewsCenter/NewsDetail?Code=" + Code + "&ID=" + item.ID + "&navigation=" + encodeURIComponent(navigation);
      break;

    case 2:
      const tabName = $(".work-center .uk-card-header .active .active").text();
      if (tabName.indexOf("我的待办") != -1) {
        openTask(item);
      } else if (tabName.indexOf("我的已办") != -1) {
        const res = await PortalApi.IsOuterForm({"flowId": item.FlowID});
        const _isouterform = res;
        if (_isouterform == true) {
          url = item.ViewUrl;
          if (url.indexOf("id=") > -1 && url.indexOf("ID=") > -1) {
            url = url.replace(/[?&]ID=[^&]*&?/g, "&");
          }
          url = url.replace(/\{token\}/g, getAccessToken());
        } else {
          url = item.ViewUrl + "&token=" + getAccessToken();
        }
        window.open(url);
        return;
      } else if (tabName.indexOf("我的关注") != -1) {
        PortalApi.GetMyFocusByID({ID: item.ID}).then(ret => {
          if (!ret.newtask || ret == null) {
            url = item.ExecURL + "&token=" + getAccessToken();
            window.open(url);
          } else {
            openTask(ret.newtask);
          }
        })
      }
      break;
  }
  if (!url) {
    return;
  }
  window.open(url);
}

// 基础路径加载
export const getFullUrl = (params) => {
  return baseUrl + params
}

//下载路径
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL

//下载文件 预览文件
export const DownLoadAndPreviewFile = async (file, type) => {
  let url = file.FileKey;
  const _correctfilekey = function (filekey) {
    if (filekey && filekey.startsWith("|")) {
      return filekey.substring(1, filekey.length);
    }
    return filekey;
  };
  if (url.indexOf("http:") == 0 && url.indexOf("|") > 0) {
    url = url.split("|")[0];
  } else {
    if (url.indexOf("|") == 0) {
      url = url.substring(1, url.length);
    }
  }
  switch (file.Cgroup) {
    case "kd":
      //获取文件token
      const result = await PortalApi.GetFileToken({fileId: _correctfilekey(file.FileKey)});
      if (result) {
        if (url.indexOf("http:") < 0) {
          url = "";
        }
        if (type === 0) {
          window.open(downLoadUrl + '?FileToken=' + result)
        } else if (type === 1) {
          window.open(url + "/UIBuilder/UIViewer/PreviewPage?FileToken=" + result);
        }
      } else {
        alert("该文件验证失败，暂不能下载，请联系管理员！");
      }
      break;

    case "mini":
      window.open("http://*********:8080/FileStore/Download.aspx?FileId=" + encodeURI(url));
      break;

    default:
      if (url) {
        const _alias = file.AliasName;
        if (_alias) {
          switch (_alias) {
            case "attachmentold":
              downLoadFile(url, 2);
              break;
            case "attachmentnew":
              downLoadFile(url, 1);
              break;
          }
        } else {
          if (url.indexOf("javascript:") == 0) {
            if (url.indexOf("fileID") != -1) {
              let idurl = url.substring(url.indexOf("fileID"))
              if (idurl.indexOf("&") != -1) {
                idurl = idurl.substring(idurl.indexOf("&"))
              } else {
                idurl = idurl.replace("')", "").replace("fileID=", "")
              }
              downLoadFile(idurl, 1);
            }
          } else {
            if (type === 0) {
              if (url.indexOf("/BasicApplication/KindEditor/DownloadFile?path") != -1) {
                url = url.substring(url.indexOf("/BasicApplication"))
                url = baseUrl + url + "&token=" + getAccessToken();
              }
              window.open(url);
            } else if (type === 1) {
              window.open(url);
            } else {
              alert("该文件无法预览，请下载查看！");
            }
          }
        }
      }
      break;
  }
}

// 下载文件
const downLoadFile = async (fileName, type) => {
  if (parseInt(type) === 1) {
    //获取文件token
    const result = await PortalApi.GetFileToken({"fileId": fileName});
    console.log(result)
    if (result) {
      window.open(downLoadUrl + '?FileToken=' + result)
    } else {
      alert('该文件验证失败，暂不能下载，请联系管理员！')
    }
  } else {
    //老的中心网下载
    window.location.href = 'http://10.10.1.29/Portal/Home/download?name=' + escape(fileName)
  }
}
// 取消收藏
export const CancelSc = async (Item: any) => {
  console.log(Item)
}

//新闻详情url
export const detail_url = (code, id, cate) => {
  return (
    '/Portal/NewsCenter/NewsDetail?Code=' +
    code +
    '&ID=' +
    id +
    '&navigation=' +
    encodeURI(cate)
  ) //url中文编码
}

//头部新闻baner图片url
export const banner_url = (url) => {
  if (url != null && url != '') {
    if (url.indexOf('http') == -1 && url.indexOf('https') == -1) {
      if (url.indexOf('/UIBuilder/UIViewer/') != -1) {
        url = baseUrl + url
      }
    }

    if (url.indexOf('ftp://') == -1) {
      //排除ftp链接
      if (url.indexOf('?') > 0) {
        url += '&' //如果已经有？号则拼接&符号
      } else {
        url += '?' //如果没有？号则拼接?符号
      }
      if (url.indexOf('token') == -1) {
        //最后拼接token
        url += 'token=' + getAccessToken()
      }
    }
  }
  return url
}
