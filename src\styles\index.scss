@use './FormCreate/index.scss';
@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
            0 0 10px var(--el-color-primary),
            0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.center-button {
  display: flex;
  justify-content: center;
  align-items: center;
}
.tree_container {
  display: flex;
  min-width: 100%;
  justify-content: center;
  align-items: center;
  background-color: white;
  &.height60{
    height: 60vh; /* 设置容器高度为视口高度 */
  }
  &.height65{
    height: 65vh; /* 设置容器高度为视口高度 */
  }
  &.height70{
    height: 70vh; /* 设置容器高度为视口高度 */
  }
  &.height85{
    height: 85vh; /* 设置容器高度为视口高度 */
  }
}

.scrollable-row {
  height: 100%; /* 设置高度为父容器高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
  border: 1px solid #ccc; /* 可选：添加边框 */
  &.width220{
    min-width: 220px;
  }
  &.width100{
    width: 100%;
  }
}


.icon-help{
  margin: 5px
}
