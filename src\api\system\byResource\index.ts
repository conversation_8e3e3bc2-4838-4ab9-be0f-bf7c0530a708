import request from '@/config/axios'

export interface DeptVO {
}
// 树数据查询
export const getResTree2 = async (params: any) => {
  return await request.get({ url: '/system/A-res/getResTree2', params })
}
// 树数据查询
export const getResTree = async (params: any) => {
  return await request.get({ url: '/system/A-res/getResTree', params })
}

// 已关联角色查询
export const getAuthRole = async (params: any) => {
  return await request.get({ url: '/system/A-group/getAuthRole', params })
}

// 移除部门
export const deleteDeptOrRole = async (params: any) => {
  return await request.delete({ url: '/system/A-group/deleteGroupRes', params })
}

// 角色树数据查询
export const getGroupTree = async (params: any) => {
  return await request.get({ url: '/system/A-group/getGroupTree', params })
}

// 角色数据查询
export const getAGroupDataList = async (params: any) => {
  return await request.get({ url: '/system/A-group/getAGroupDataList', params })
}

//已经绑定当前资源角色数据查询
export const getAGroupDataListById = async (params: any) => {
  return await request.get({ url: '/system/A-group/getAuthRoleByResId', params })
}
//保存授权到角色
export const saveAGroupRes = async (params: any) => {
  return await request.get({ url: '/system/A-group/saveGroupRes', params })
}

